<Window
    x:Class="SmartRouting.App.Commands.数据录入.View.JointEditorView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:SmartRouting.App.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:SmartRouting.App.Commands.数据录入.Model"
    Title="编辑接头信息"
    ResizeMode="NoResize"
    ShowInTaskbar="False"
    SizeToContent="WidthAndHeight"
    Topmost="True"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">
    <Window.Resources>
        <converters:EnumToBooleanConverter x:Key="EnumToBooleanConverter" />
    </Window.Resources>
    <Border Padding="15">
        <StackPanel>
            <GroupBox Padding="5" Header="接头设置">
                <StackPanel Orientation="Horizontal">
                    <RadioButton
                        Margin="10,0"
                        Content="总"
                        GroupName="JointTypeGroup"
                        IsChecked="{Binding OperationEnum, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static model:OperationEnum.标记接头_总}}" />
                    <RadioButton
                        Margin="10,0"
                        Content="分支"
                        GroupName="JointTypeGroup"
                        IsChecked="{Binding OperationEnum, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static model:OperationEnum.标记接头_分支}}" />
                </StackPanel>
            </GroupBox>

            <Button
                MinWidth="80"
                Margin="0,20,0,0"
                HorizontalAlignment="Center"
                Click="SaveButton_Click"
                Content="确定" />
        </StackPanel>
    </Border>
</Window> 