﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App
{
    public static class W
    {
        //当前开的文档
        public static Document Doc { get { return CadApp.DocumentManager.MdiActiveDocument; } }
        //当前数据库
        public static Database Db { get { return Doc.Database; } }
        //当前编辑器
        public static Editor Ed { get { return Doc.Editor; } }
        public static DateTime dt;
        public static void ShowTime()
        {
            Ed.WriteMessage((DateTime.Now - dt).ToString("fff") + "\n");
            dt = DateTime.Now;
        }
        public static void StartTime()
        {
            dt = DateTime.Now;
        }
        //开启事务
        public static Transaction Trans { get { return Db.TransactionManager.StartTransaction(); } }
        //开启文档锁
        public static DocumentLock DocLock { get { return Doc.LockDocument(); } }
        //聚焦到文档
        public static void FocusDoc()
        {
            SetFocus(W.Doc.Window.Handle);
        }
        /// <summary>
        /// 获取系统变量
        /// </summary>
        /// <param name="name"></param>
        public static object GetSysVar(string name)
        {
            return CadApp.GetSystemVariable(name);
        }
        /// <summary>
        /// 设置系统变量
        /// </summary>
        /// <param name="name"></param>
        /// <param name="value"></param>
        public static void SetSysVar(string name, object value)
        {
            Application.SetSystemVariable(name, value);
        }
        [DllImport("user32.dll")]
        public static extern System.IntPtr SetFocus(System.IntPtr hwnd);
        public static Random m_rand = new Random();
    }
}
