﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils.Algorithms
{
    public class QuadTreeLeaf<T>
    {
        private Point3d pos;
        private T refObject;

        public QuadTreeLeaf(Point3d pos, T obj)
        {
            this.pos = pos;
            refObject = obj;
        }

        public T LeafObject
        {
            get
            {
                return refObject;
            }
        }

        public Point3d Pos
        {
            get { return pos; }
            set { pos = value; }
        }
    }

    /// <summary>
    /// 四叉树节点
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class QuadTree<T>
    {
        /// <summary>
        /// 节点拥有的叶子节点
        /// </summary>
        List<QuadTreeLeaf<T>> items;
        /// <summary>
        /// 节点拥有的分支
        /// </summary>
        public QuadTree<T>[] branch;
        /// <summary>
        /// 节点空间最大容量，受minSize影响
        /// </summary>
        protected int maxItems;
        /// <summary>
        /// 节点空间分割的最小大小（最小宽度，高度）
        /// </summary>
        protected double minSize;

        public const double TOLERANCE = 0.00001f;
        /// <summary>
        /// 节点的空间
        /// </summary>
        //public Rect bounds;
        public Extents3d Ext { get; set; }


        public QuadTree(Extents3d ext, int 每级最多存储数量 = 4, double minSize = -1)
        {
            this.Ext = ext;
            //bounds = new Rect(ext.MinPoint.X, ext.MinPoint.Y, ext.MaxPoint.X - ext.MinPoint.X, ext.MinPoint.Y - ext.MinPoint.Y);

            maxItems = 每级最多存储数量;
            this.minSize = minSize;
            items = new List<QuadTreeLeaf<T>>();

        }
        public bool HasChildren()
        {
            if (branch != null)
                return true;
            else
                return false;
        }

        /// <summary>
        /// 将节点空间分割4份
        /// </summary>
        protected void Split()
        {
            if (minSize != -1)
            {
                if ((Ext.MaxPoint.X - Ext.MinPoint.X) <= minSize && (Ext.MaxPoint.Y - Ext.MinPoint.Y) <= minSize)
                {
                    return;
                }
            }
            var ext4 = Ext.Split4();

            branch = new QuadTree<T>[4];
            for (int i = 0; i < 4; i++)
            {
                branch[i] = new QuadTree<T>(ext4[i], maxItems, minSize);
            }

            foreach (var item in items)
            {
                AddNode(item);
            }

            items.Clear();
        }

        /// <summary>
        /// 根据坐标获得相应的子空间
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        protected QuadTree<T> GetChild(Point3d pos)
        {
            if (Ext.Contains(pos))
            {
                if (branch != null)
                {
                    for (int i = 0; i < branch.Length; i++)
                        if (branch[i].Ext.Contains(pos))
                            return branch[i].GetChild(pos);

                }
                else
                    return this;
            }
            return null;
        }
        /// <summary>
        /// 增加叶子节点数据
        /// </summary>
        /// <param name="leaf"></param>
        /// <returns></returns>
        private bool AddNode(QuadTreeLeaf<T> leaf)
        {
            if (branch is null)
            {
                this.items.Add(leaf);

                if (this.items.Count > maxItems) Split();
                return true;
            }
            else
            {
                QuadTree<T> node = GetChild(leaf.Pos);
                if (node != null)
                {
                    return node.AddNode(leaf);
                }
            }
            return false;
        }
        public bool AddNode(Point3d pos, T obj)
        {
            return AddNode(new QuadTreeLeaf<T>(pos, obj));
        }

        /// <summary>
        /// 可以是空间任意位置，只是根据这个位置找到所在的空间去删除对象
        /// </summary>
        /// <param name="pos"></param>
        /// <param name="obj"></param>
        /// <returns></returns>
        public bool RemoveNode(Point3d pt, T obj)
        {
            if (branch is null)
            {
                for (int i = 0; i < items.Count; i++)
                {
                    QuadTreeLeaf<T> qtl = items[i];
                    if (qtl.LeafObject.Equals(obj))
                    {
                        items.RemoveAt(i);
                        return true;
                    }
                }
            }
            else
            {
                QuadTree<T> node = GetChild(pt);
                if (node != null)
                {
                    return node.RemoveNode(pt, obj);
                }
            }
            return false;
        }
        public int GetNode(Extents3d ext, ref List<T> nodes)
        {
            // Point2d p0 = new Point2d(ext.MinPoint.X, ext.MinPoint.Y);
            //Vector3d vt = ext.MaxPoint - ext.MinPoint;
            //Rect rect = new Rect(p0, new Point2d(vt.X, vt.Y));
            if (branch is null)
            {
                foreach (QuadTreeLeaf<T> item in items)
                {
                    if (ext.Contains(item.Pos))
                    {
                        nodes.Add(item.LeafObject);
                    }
                }
            }
            else
            {
                for (int i = 0; i < branch.Length; i++)
                {
                    if (branch[i].Ext.Overlaps(ext))
                        branch[i].GetNode(ext, ref nodes);
                }
            }
            return nodes.Count;
        }
        public List<T> GetNode(Extents3d ext)
        {
            List<T> nodes = new List<T>();
            GetNode(ext, ref nodes);
            return nodes;
        }
        /// <summary>
        /// 根据坐标得到坐标附近节点的数据
        /// </summary>
        /// <param name="pos"></param>
        /// <param name="ShortestDistance">离坐标最短距离</param>
        /// <param name="list"></param>
        /// <returns></returns>
        public int GetNodeRecRange(Point3d pos, double ShortestDistance, ref List<T> list)
        {
            double distance;
            if (branch is null)
            {
                foreach (QuadTreeLeaf<T> leaf in items)
                {
                    distance = (pos - leaf.Pos).Length;

                    if (distance < ShortestDistance)
                    {
                        list.Add(leaf.LeafObject);
                    }
                }
            }
            else
            {
                for (int i = 0; i < branch.Length; i++)
                {
                    double childDistance = branch[i].Ext.PointToExtentsDistance(pos);
                    if (childDistance < ShortestDistance * ShortestDistance)
                    {
                        branch[i].GetNodeRecRange(pos, ShortestDistance, ref list);
                    }
                }
            }
            return list.Count;
        }

        public List<T> GetNodeRecRange(Point3d pos, double ShortestDistance)
        {
            List<T> list = new List<T>();
            int n = GetNodeRecRange(pos, ShortestDistance, ref list);
            return list;
        }

    }
    public static class EX
    {
        public static List<Extents3d> Split4(this Extents3d ext)
        {
            var x1 = ext.MinPoint.X;
            var x2 = ext.MaxPoint.X;
            var y1 = ext.MinPoint.Y;
            var y2 = ext.MaxPoint.Y;
            var xm = x2 / 2 + x1 / 2;
            var ym = y2 / 2 + y1 / 2;
            Extents3d ext1 = new Extents3d(new Point3d(x1, y1, 0), new Point3d(xm, ym, 0));
            Extents3d ext2 = new Extents3d(new Point3d(x1, ym, 0), new Point3d(xm, y2, 0));
            Extents3d ext3 = new Extents3d(new Point3d(xm, ym, 0), new Point3d(x2, y2, 0));
            Extents3d ext4 = new Extents3d(new Point3d(xm, y1, 0), new Point3d(x2, ym, 0));

            return new List<Extents3d> { ext1, ext2, ext3, ext4 };
        }
        public static bool Contains(this Extents3d ext, Point3d pt)
        {
            return pt.X >= ext.MinPoint.X && pt.X <= ext.MaxPoint.X && pt.Y >= ext.MinPoint.Y && pt.Y <= ext.MaxPoint.Y;

        }
        public static bool Overlaps(this Extents3d ext, Extents3d other)
        {
            return other.MaxPoint.X > ext.MinPoint.X && other.MinPoint.X < ext.MaxPoint.X
                && other.MaxPoint.Y > ext.MinPoint.Y && other.MinPoint.Y < ext.MaxPoint.Y;
        }
        public static Point3d PointToNormalized(Extents3d rectangle, Point3d point)
        {
            return new Point3d(
                InverseLerp(rectangle.MinPoint.X, rectangle.MinPoint.X, point.X),
                InverseLerp(rectangle.MinPoint.X, rectangle.MaxPoint.Y, point.Y),
                0);
        }
        public static double PointToExtentsDistance(this Extents3d ext, Point3d pos)
        {
            double xdisance;
            double ydisance;

            if (ext.MinPoint.X <= pos.X && pos.X <= ext.MaxPoint.X)
            {
                xdisance = 0;
            }
            else
            {
                xdisance = Math.Min((Math.Abs(pos.X - ext.MaxPoint.X)), Math.Abs(pos.X - ext.MinPoint.X));
            }

            if (ext.MinPoint.Y <= pos.Y && pos.Y <= ext.MaxPoint.Y)
            {
                ydisance = 0;
            }
            else
            {
                ydisance = Math.Min(Math.Abs(pos.Y - ext.MaxPoint.Y), Math.Abs(pos.Y - ext.MinPoint.Y));
            }

            return xdisance * xdisance + ydisance * ydisance;
        }
        public static double InverseLerp(double a, double b, double value)
        {
            if (a != b)
            {
                return Clamp01((value - a) / (b - a));
            }

            return 0f;
        }
        public static double Lerp(double a, double b, double t)
        {
            return a + (b - a) * Clamp01(t);
        }
        public static double Clamp01(double value)
        {
            if (value < 0)
            {
                return 0;
            }

            if (value > 1)
            {
                return 1;
            }

            return value;
        }

    }



}
