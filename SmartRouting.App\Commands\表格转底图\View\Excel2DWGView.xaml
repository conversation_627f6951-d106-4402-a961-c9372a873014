﻿<Window
    x:Class="SmartRouting.App.Commands.表格转底图.View.Excel2DWGView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SmartRouting.App.Commands.表格转底图.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="表格转底图 V20250622"
    Width="400"
    Height="AUTO"
    Closing="Window_Closing"
    Loaded="Window_Loaded"
    ResizeMode="NoResize"
    ShowInTaskbar="False"
    SizeToContent="Height"
    SnapsToDevicePixels="True"
    Topmost="True"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/SmartRouting.App;component/Common/WPFUI/CommonUIStyle.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <DockPanel Margin="5">
        <Grid DockPanel.Dock="Top">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="AUTO" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="AUTO" />
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" Text="Excel表格路径:" />
            <TextBox x:Name="Tb_ExcelPath" Grid.Column="1" />
            <Button
                x:Name="Btn_SelectExcelPath"
                Grid.Column="2"
                Click="Btn_SelectExcelPath_Click"
                Content="选择" />
        </Grid>
        <DockPanel>
            <DockPanel>
                <TextBlock Text="Sheet名称:" />
                <ComboBox x:Name="Cb_SheetName" Width="150" />
            </DockPanel>
            <WrapPanel HorizontalAlignment="Right" DockPanel.Dock="Top">

                <Button
                    x:Name="Btn_Creation"
                    Click="Btn_Creation_Click"
                    Content="生成工作底图" />
                <Button
                    x:Name="Btn_Cancle"
                    Click="Btn_Cancle_Click"
                    Content="取消" />
            </WrapPanel>
        </DockPanel>

    </DockPanel>


</Window>
