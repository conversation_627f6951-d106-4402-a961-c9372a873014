﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.MVVM
{
    public abstract class ViewModelBase : BindableBase
    {
        /// <summary>
        /// 配置文件所在文件夹
        /// </summary>
        protected string SettingsFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), AssemblyFileProductName());

        /// <summary>
        /// 配置文件路径
        /// </summary>
        protected string SettingsFile { get; set; }

        /// <summary>
        /// 初始化
        /// </summary>
        protected abstract void Init();

        /// <summary>
        /// 构造函数
        /// </summary>
        public ViewModelBase()
        {
            Init();
        }

        ///// <summary>
        ///// 读取数据
        ///// </summary>
        ///// <typeparam name="T"></typeparam>
        ///// <param name="file">数据文件路径</param>
        //protected virtual T ReadData<T>(string file = null)
        //{
        //    T data = default;
        //    try
        //    {
        //        if (!string.IsNullOrWhiteSpace(file))
        //        {
        //            SettingsFile = file;
        //        }
        //        else
        //        {
        //            if (string.IsNullOrWhiteSpace(SettingsFile))
        //            {
        //                SettingsFile = Path.Combine(SettingsFolder, typeof(T).Name + ".xml");
        //            }
        //        }
        //        if (File.Exists(SettingsFile))
        //        {
        //            data = XmlUtil.XmlDeserializeFromFile<T>(SettingsFile, Encoding.UTF8);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Trace.WriteLine("读取数据失败：" + ex.ToString());
        //    }
        //    return data;
        //}

        ///// <summary>
        ///// 保存数据
        ///// </summary>
        ///// <typeparam name="T"></typeparam>
        ///// <param name="t"></param>
        ///// <param name="file">数据文件路径</param>
        //protected virtual bool SaveData<T>(object obj, string file = null)
        //{
        //    bool result = false;
        //    try
        //    {
        //        if (!string.IsNullOrWhiteSpace(file))
        //        {
        //            SettingsFile = file;
        //        }
        //        else
        //        {
        //            if (string.IsNullOrWhiteSpace(SettingsFile))
        //            {
        //                SettingsFile = Path.Combine(SettingsFolder, typeof(T).Name + ".xml");
        //            }
        //        }
        //        CheckAndCreateSettingsFolder();
        //        XmlUtil.XmlSerializeToFile(obj, SettingsFile, Encoding.UTF8);
        //        result = true;
        //    }
        //    catch (Exception ex)
        //    {
        //        Trace.WriteLine("保存数据失败：" + ex.ToString());
        //    }
        //    return result;
        //}

        /// <summary>
        /// 检查并创建配置所在位置文件夹
        /// </summary>
        private void CheckAndCreateSettingsFolder()
        {
            SettingsFolder = Path.GetDirectoryName(SettingsFile);


            if (!Directory.Exists(SettingsFolder))
            {
                Directory.CreateDirectory(SettingsFolder);
            }

            Process.Start(SettingsFolder);
        }

        #region 获取程序集产品名称
        /// <summary>
        /// 获取程序集产品名称
        /// </summary>
        /// <returns>产品名称</returns>
        public static string AssemblyFileProductName()
        {
            object[] attributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyProductAttribute), false);
            if (attributes.Length == 0)
            {
                return string.Empty;
            }
            else
            {
                return ((AssemblyProductAttribute)attributes[0]).Product;
            }
        }
        #endregion
    }
}

