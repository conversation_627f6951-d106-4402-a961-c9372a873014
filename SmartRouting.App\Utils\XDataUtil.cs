﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    /// <summary>
    /// 扩展数据操作类 (无内置事务版)
    /// 【重要】: 此类的所有方法都必须在一个外部开启的事务中调用。
    /// </summary>
    public static class XDataUtil
    {
        /// <summary>
        /// 在模型空间中查找所有包含指定扩展数据的、特定类型的实体。
        /// 这个函数是通用的，可以查找任何继承自Entity的类型。
        /// </summary>
        /// <typeparam name="T">要查找的实体类型，必须继承自 Autodesk.AutoCAD.DatabaseServices.Entity。</typeparam>
        /// <param name="regAppName">要查找的扩展数据所属的注册应用程序名。</param>
        /// <returns>找到的实体对象列表。</returns>
        public static List<T> FindEntitiesWithXDataWithTrans<T>(string regAppName) where T : Entity
        {
            // 创建一个用于存储结果的列表
            var foundEntities = new List<T>();
            var doc = W.Doc;
            if (doc == null) return foundEntities;


            // 使用 using 来确保事务在结束时总是被正确处理（提交或中止）
            using (var transaction =W.Trans)
            {
                // 1. 获取模型空间的块表记录
                var modelSpace = (BlockTableRecord)transaction.GetObject(
                    SymbolUtilityServices.GetBlockModelSpaceId(W.Db),
                    OpenMode.ForRead
                );

                // 2. 遍历模型空间中的所有对象
                foreach (ObjectId objId in modelSpace)
                {
                    // 3. 检查当前对象的类型是否为我们正在寻找的 T 类型
                    //    IsDerivedFrom 会检查对象本身及其所有父类，所以这是正确的
                    if (objId.ObjectClass.IsDerivedFrom(RXObject.GetClass(typeof(T))))
                    {
                        // 4. 将对象作为 T 类型打开
                        var entity = (T)transaction.GetObject(objId, OpenMode.ForRead);

                        // 5. 获取扩展数据
                        //    使用 using 确保 ResultBuffer 被正确释放
                        using (ResultBuffer xdata = entity.XData)
                        {
                            if (xdata != null)
                            {
                                // 6. 检查扩展数据中是否包含指定的应用程序名
                                foreach (TypedValue typedValue in xdata)
                                {
                                    if (typedValue.TypeCode == (int)DxfCode.ExtendedDataRegAppName &&
                                        typedValue.Value.ToString().Equals(regAppName, System.StringComparison.OrdinalIgnoreCase))
                                    {
                                        // 如果找到，将这个实体添加到结果列表中
                                        foundEntities.Add(entity);

                                        // 既然已经找到了匹配的 RegAppName，就无需再检查此对象的其他扩展数据
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                // 事务只是为了读取，所以结束后可以中止它，避免不必要的数据库写操作
                // 如果您在事务中没有做任何修改，Commit() 和 Abort() 效果类似，但 Abort() 更能表明“只读”的意图
                transaction.Abort();
            }

            return foundEntities;
        }















        /// <summary>
        /// 获取实体上所有扩展数据，并按应用程序名组织成字典。
        /// </summary>
        /// <param name="ent">要读取的实体。</param>
        /// <returns>一个字典，键为应用程序名，值为该应用程序下的数据元组列表。</returns>
        public static Dictionary<string, List<Tuple<int, object>>> GetEntityXData(Entity ent)
        {
            var xDataDict = new Dictionary<string, List<Tuple<int, object>>>();
            if (ent?.XData == null) return xDataDict;

            string currentApp = "";
            foreach (TypedValue tv in ent.XData)
            {
                if (tv.TypeCode == (int)DxfCode.ExtendedDataRegAppName)
                {
                    currentApp = tv.Value.ToString();
                    if (!xDataDict.ContainsKey(currentApp))
                    {
                        xDataDict.Add(currentApp, new List<Tuple<int, object>>());
                    }
                }
                else if (!string.IsNullOrEmpty(currentApp))
                {
                    xDataDict[currentApp].Add(Tuple.Create((int)tv.TypeCode, tv.Value));
                }
            }
            return xDataDict;
        }

        /// <summary>
        /// 【无事务】为对象添加或替换扩展数据。
        /// </summary>
        /// <param name="id">对象Id</param>
        /// <param name="regAppName">应用名</param>
        /// <param name="values">要写入的纯数据列表</param>
        /// <param name="tr">【必需】外部传入的有效事务</param>
        public static void AddXData(this ObjectId id, string regAppName, TypedValueList values, Transaction tr)
        {
            DBObject obj = tr.GetObject(id, OpenMode.ForWrite);
            SafeSetXData_Internal(obj, regAppName, values, tr);
        }

        /// <summary>
        /// 【无事务】获取对象的扩展数据。
        /// </summary>
        /// <param name="id">对象Id</param>
        /// <param name="regAppName">应用名</param>
        /// <param name="tr">【必需】外部传入的有效事务</param>
        /// <returns>返回不包含应用名的纯数据列表，如果无数据则返回null</returns>
        public static TypedValueList GetXData(this ObjectId id, string regAppName, Transaction tr)
        {
            TypedValueList values = null;
            DBObject obj = tr.GetObject(id, OpenMode.ForRead);
            ResultBuffer rb = obj.GetXDataForApplication(regAppName);
            if (rb != null)
            {
                // 【重要】我们返回的列表不应包含应用名，只返回纯数据
                values = new TypedValueList(rb.AsArray().Skip(1).ToArray());
            }
            return values;
        }

        /// <summary>
        /// 【无事务】通过索引精确地修改扩展数据列表中的某一项。
        /// </summary>
        /// <param name="id">对象Id</param>
        /// <param name="regAppName">应用名</param>
        /// <param name="index">要修改的数据在列表中的索引（从0开始）。</param>
        /// <param name="newValue">新的数据值。</param>
        /// <param name="tr">【必需】外部传入的有效事务</param>
        public static void ModXDataByIndex(this ObjectId id, string regAppName, int index, object newValue, Transaction tr)
        {
            DBObject obj = tr.GetObject(id, OpenMode.ForWrite);

            // 1. 先安全地读取出该应用的所有数据
            ResultBuffer rb = obj.GetXDataForApplication(regAppName);
            if (rb == null) return; // 如果原本就没有数据，那就没什么可改的了

            // 2. 转换为列表，这里面包含了应用名在第一个位置
            TypedValueList valuesWithAppName = new TypedValueList(rb.AsArray());

            // 3. 检查索引是否有效
            int targetIndexInList = index + 1;
            if (targetIndexInList > 0 && targetIndexInList < valuesWithAppName.Count)
            {
                // 4. 在内存中精确地修改这一项
                TypedValue original_tv = valuesWithAppName[targetIndexInList];
                valuesWithAppName[targetIndexInList] = new TypedValue(original_tv.TypeCode, newValue);

                // 5. 将修改后的整个列表安全地写回去
                // 核心方法需要的是纯数据列表，所以要把应用名去掉再传进去
                valuesWithAppName.RemoveAt(0);
                SafeSetXData_Internal(obj, regAppName, valuesWithAppName, tr);
            }
        }

        /// <summary>
        /// 【核心方法】安全写入XData的内部实现。此方法本身就需要事务。
        /// </summary>
        private static void SafeSetXData_Internal(DBObject obj, string regAppName, TypedValueList newValues, Transaction tr)
        {
            Database db = obj.Database;
            RegAppTable rat = (RegAppTable)tr.GetObject(db.RegAppTableId, OpenMode.ForRead);
            if (!rat.Has(regAppName))
            {
                rat.UpgradeOpen();
                RegAppTableRecord ratr = new RegAppTableRecord { Name = regAppName };
                rat.Add(ratr);
                tr.AddNewlyCreatedDBObject(ratr, true);
                rat.DowngradeOpen();
            }

            ResultBuffer allXData = obj.XData;
            List<TypedValue> finalValues = new List<TypedValue>();

            if (allXData != null)
            {
                bool isOurAppData = false;
                foreach (TypedValue tv in allXData)
                {
                    if (tv.TypeCode == (short)DxfCode.ExtendedDataRegAppName)
                    {
                        isOurAppData = tv.Value.ToString().Equals(regAppName);
                    }
                    if (!isOurAppData)
                    {
                        finalValues.Add(tv);
                    }
                }
            }

            if (newValues != null && newValues.Count > 0)
            {
                finalValues.Add(new TypedValue((int)DxfCode.ExtendedDataRegAppName, regAppName));
                finalValues.AddRange(newValues);
            }
            obj.XData = new ResultBuffer(finalValues.ToArray());
        }
    }

    /// <summary>
    /// 一个继承自 List<TypedValue> 的辅助类，提供了方便的构造函数和隐式转换。
    /// </summary>
    public class TypedValueList : List<TypedValue>
    {
        public TypedValueList() { }
        public TypedValueList(params TypedValue[] args) { AddRange(args); }
        public void Add(int typecode, object value) => base.Add(new TypedValue(typecode, value));
        public void Add(DxfCode typecode, object value) => base.Add(new TypedValue((int)typecode, value));
        public static implicit operator ResultBuffer(TypedValueList src) => src != null ? new ResultBuffer(src.ToArray()) : null;
        public static implicit operator TypedValueList(TypedValue[] src) => src != null ? new TypedValueList(src) : null;
        public static implicit operator TypedValueList(ResultBuffer src) => src != null ? new TypedValueList(src.AsArray()) : null;
    }
}
