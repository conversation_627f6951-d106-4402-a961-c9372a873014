﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <!--   <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDark.xaml" />   -->
        <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDefault.xaml" />

        <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml" />
    </ResourceDictionary.MergedDictionaries>
    <Style BasedOn="{StaticResource BaseStyle}" TargetType="Window">
        <Setter Property="ResizeMode" Value="NoResize" />
        <Setter Property="ShowInTaskbar" Value="False" />
        <Setter Property="SizeToContent" Value="Width" />
        <Setter Property="ShowInTaskbar" Value="False" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
    </Style>
    <Style BasedOn="{StaticResource ButtonBaseStyle}" TargetType="Button">
        <Setter Property="Width" Value="AUTO" />
        <Setter Property="Foreground" Value="Black" />
        <Setter Property="Margin" Value="2" />
    </Style>
    <Style BasedOn="{StaticResource BaseStyle}" TargetType="GroupBox">
        <Setter Property="Margin" Value="2" />
    </Style>
    <Style BasedOn="{StaticResource BaseStyle}" TargetType="Expander" />
    <Style BasedOn="{StaticResource ComboBoxBaseStyle}" TargetType="ComboBox">
        <Setter Property="Margin" Value="2" />
        <Setter Property="Height" Value="20" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
    </Style>
    <Style TargetType="{x:Type DataGridCell}" />
    <Style BasedOn="{StaticResource LabelDefault.Small}" TargetType="Label">
        <Setter Property="Margin" Value="5" />
        <Setter Property="FontSize" Value="15" />
    </Style>
    <Style BasedOn="{StaticResource TextBoxBaseStyle}" TargetType="TextBox">
        <Setter Property="Height" Value="AUTO" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
    </Style>
    <Style BasedOn="{StaticResource TextBlockDefault}" TargetType="TextBlock">
        <Setter Property="Margin" Value="2" />
        <Setter Property="HorizontalAlignment" Value="Right" />
    </Style>
    <Style BasedOn="{StaticResource RadioButtonBaseStyle}" TargetType="RadioButton">
        <Setter Property="Margin" Value="2" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>
    <Style BasedOn="{StaticResource BaseStyle}" TargetType="ListView" />
    <Style BasedOn="{StaticResource BaseStyle}" TargetType="DataGrid">
        <Setter Property="Foreground" Value="#FF333333" />
    </Style>



    <Style BasedOn="{StaticResource TabControlBaseStyle}" TargetType="TabControl" />
    <!--<cv1:TabItemConverter x:Key="TabItemConverter" />-->
    <!--<Style x:Key="TabItemVerticalStyle" TargetType="TabItem">
        <Setter Property="HeaderTemplate">
            <Setter.Value>
                <DataTemplate>
                    <TextBlock Text="{Binding Path=Header, RelativeSource={RelativeSource AncestorType=TabItem}, Converter={StaticResource TabItemConverter}}" />
                </DataTemplate>
            </Setter.Value>
        </Setter>
    </Style>-->






    <Style BasedOn="{StaticResource CheckBoxBaseStyle}" TargetType="CheckBox">
        <Setter Property="Margin" Value="2" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
    </Style>
</ResourceDictionary>