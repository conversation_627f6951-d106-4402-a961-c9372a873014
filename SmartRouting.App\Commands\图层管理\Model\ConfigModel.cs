﻿
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.图层管理.Model
{
    public class ConfigModel
    {

        /// <summary>
        /// 图块的配置信息
        /// </summary>
        public List<BlockModelConfig> BlockModelConfigs { get; set; }



        /// <summary>
        /// 图层的配置信息
        /// </summary>
        public List<LayerModelConfig> LayerModelConfigs { get; set; }


        /// <summary>
        /// 数据特性
        /// </summary>
        public List<DataModelConfig> DataModelConfigs { get; set; }

    }

    public class DataModelConfig
    {
        public string LayerName { get; set; }


        public int ColorIndex { get; set; }


        public string LineStyle { get; set; }


        public int LineScale { get; set; }


        public bool IsFunc { get; set; }

    }

    public class LayerModelConfig
    {

        public string LayerName { get; set; }

        public bool IsFunc { get; set; }

    }

    public class BlockModelConfig
    {
        public string BlockName { get; set; }

        public bool IsFunc { get; set; }

    }
}
