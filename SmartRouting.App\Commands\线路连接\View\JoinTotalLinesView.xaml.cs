﻿using SmartRouting.App.Commands.线路连接.Model;
using SmartRouting.App.Commands.表格转底图.Model;
using SmartRouting.App.Properties;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using static OfficeOpenXml.ExcelErrorValue;

namespace SmartRouting.App.Commands.线路连接.View
{
    /// <summary>
    /// JoinTotalLinesView.xaml 的交互逻辑
    /// </summary>
    public partial class JoinTotalLinesView : Window
    {
        ObservableCollection<JoinTotalLinesModel> blockModels = new ObservableCollection<JoinTotalLinesModel>();

        public ObservableCollection<string> totalBlocks = new ObservableCollection<string> { "非普", "上杆", "人孔1", "人孔2", "手孔1", "手孔2", "DL人孔井", "747", "检修井（特殊）", "DJ", "S362", "LJXJ", "DL手孔", "A$C632328B2", "746B（特殊）", "S358", "LSK", "TX人孔井", "746A", "A$C792A5455", "RK", "S352", "DJXJ", "A$C3D2F6F75", "D_SK", "746B", "DSK", "检修井", "探测点", "接线箱2" };//所有的块
        public ObservableCollection<string> breakBlocks = new ObservableCollection<string> { "非普", "人孔1", "人孔2", "手孔1", "手孔2", "接线箱2", "上杆", };//需要断开的块
        public readonly static double tol = 0.01;
        public JoinTotalLinesView()
        {
            InitializeComponent();
            Dg_BlockInfo.ItemsSource = blockModels;
        }

        private void Btn_SelectLineLayer_Click(object sender, RoutedEventArgs e)
        {
            using (W.DocLock)
            {
                W.FocusDoc();
                SelectEntitiesWithTurnOffLayer<CadDb.Curve>(
                    layerName =>
                    {
                        var totalTexts = Tb_LineLayerName.Text.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                        if (!totalTexts.Any(x => x.Equals(layerName)))
                        {
                            if (string.IsNullOrEmpty(Tb_LineLayerName.Text) || Tb_LineLayerName.Text.EndsWith(","))
                            {
                                Tb_LineLayerName.Text += layerName + ",";
                            }
                            else
                            {
                                Tb_LineLayerName.Text += "," + layerName + ",";
                            }
                        }
                    });
            }
        }

        /// <summary>
        /// 关闭指定图层的实体
        /// </summary>
        /// <param name="message"></param>
        /// <returns></returns>
        public static bool SelectEntitiesWithTurnOffLayer<T>(Action<string> onEachLayerSelected, SelectionFilter selectionFilter = null, string message = "选择图形") where T : Entity
        {
            List<string> layerNameList = new List<string>();
            Editor ed = W.Ed;
            PromptEntityOptions peo = new PromptEntityOptions(message);

            while (true)
            {
                var per = ed.GetEntity(peo);
                if (per.Status == PromptStatus.OK)
                {
                    using (Transaction tran = W.Trans)
                    {
                        Entity entity = (Entity)per.ObjectId.GetObject(OpenMode.ForWrite);
                        if (entity is T e)
                        {
                            if (!layerNameList.Contains(e.Layer))
                            {
                                LayerUtil.TurnLayerIsOff(e.Layer);
                                layerNameList.Add(e.Layer);
                            }
                            onEachLayerSelected?.Invoke(e.Layer);
                        }
                        tran.Commit();
                    }
                }
                else
                {
                    break;
                }
            }

            foreach (var item in layerNameList)
            {
                LayerUtil.TurnLayerIsOn(item);
            }

            return layerNameList.Count > 0;
        }



        private void Btn_Cancel_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            Tb_LineLayerName.Text = Settings.Default.线图层;
            List<string> blockNames = new List<string>();
            using (W.DocLock)
            {
                W.FocusDoc();
                var allBlockNames = ModelUtil.GetAllBlockNames();
                foreach (var blockName in allBlockNames)
                {
                    blockNames.Add(blockName);
                    if (totalBlocks.Contains(blockName))
                    {
                        if (breakBlocks.Contains(blockName))
                        {
                            JoinTotalLinesModel blockModel1 = new JoinTotalLinesModel();
                            blockModel1.BlockName = blockName;
                            blockModel1.IsBreak = true;
                            blockModels.Add(blockModel1);
                        }
                        else
                        {
                            JoinTotalLinesModel blockModel1 = new JoinTotalLinesModel();
                            blockModel1.BlockName = blockName;
                            blockModel1.IsBreak = false;
                            blockModels.Add(blockModel1);
                        }
                    }
                }
            }


            Cb_BlockName.ItemsSource = blockNames;
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            Settings.Default.线图层 = Tb_LineLayerName.Text;
            Settings.Default.Save();
        }

        /// "执行"按钮点击事件，处理线路连接和打断的核心逻辑。
        /// </summary>
        private void Btn_Test_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(Tb_LineLayerName.Text))
            {
                MessageBoxUtil.MessageInformation("请设置线图层");
                return;
            }
            TypedValueList values = new TypedValueList();
            values.Add(new TypedValue(1000, "管道"));//块名
            var stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();

            try
            {
                using (W.DocLock)
                {
                    W.FocusDoc();

                    // =================================================================================
                    // 1. 选择实体, 并准备原始ID用于最终删除
                    // =================================================================================
                    var lineLayers = Tb_LineLayerName.Text.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                    var models = Dg_BlockInfo.ItemsSource as ObservableCollection<JoinTotalLinesModel>;
                    if (!SelectUtil.SelectEntities(out List<Entity> ents, FilterUtil.OrFilter("LWPolyline,line,INSERT,polyline"), "请选择多段线或直线或块")) return;

                    var originalCurveIdsToDelete = new List<ObjectId>();
                    var curves = new List<CadDb.Line>();
                    var blockReferences = new List<BlockReference>();
                    var breakBlockRefers = new List<BlockReference>();
                    var modelNames = new HashSet<string>(models.Select(m => m.BlockName));
                    var breakModelNames = new HashSet<string>(models.Where(m => m.IsBreak).Select(m => m.BlockName));

                    foreach (var entity in ents)
                    {
                        if (entity is BlockReference blockReference && modelNames.Contains(blockReference.Name))
                        {
                            blockReferences.Add(blockReference);
                            if (breakModelNames.Contains(blockReference.Name)) breakBlockRefers.Add(blockReference);
                        }
                        else if (entity is Curve curve && lineLayers.Contains(curve.Layer))
                        {
                            originalCurveIdsToDelete.Add(curve.ObjectId); // 记录原始ID
                            if (curve is CadDb.Line line) curves.Add(line.Clone() as CadDb.Line);
                            else curves.AddRange(ModelUtil.ExplodeCurve(curve).Cast<CadDb.Line>());
                        }
                    }
                    if (curves.Count == 0 && blockReferences.Count == 0) return;
                    //  W.Ed.WriteMessage($"\n[诊断] 1. 选择与分类完成: {stopwatch.ElapsedMilliseconds} ms");

                    // =================================================================================
                    // 2. 内存计算：吸附与去重
                    // =================================================================================
                    double gridSize = 2.0;
                    var blockGrid = new Dictionary<Tuple<int, int>, List<BlockReference>>();
                    foreach (var br in blockReferences)
                    {
                        var pos = br.Position;
                        var key = new Tuple<int, int>((int)(pos.X / gridSize), (int)(pos.Y / gridSize));
                        if (!blockGrid.TryGetValue(key, out var blockList))
                        {
                            blockList = new List<BlockReference>();
                            blockGrid[key] = blockList;
                        }
                        blockList.Add(br);
                    }
                    foreach (var line in curves)
                    {
                        var startPoint = line.StartPoint.SetZ(0);
                        var lineDir = (line.EndPoint.SetZ(0) - startPoint).GetNormal();
                        if (FindNearestSnapPoint(startPoint, lineDir, GetCandidateBlocks(startPoint, blockGrid, gridSize)) is Point3d nearestToStart) line.StartPoint = nearestToStart;

                        var endPoint = line.EndPoint.SetZ(0);
                        lineDir = (startPoint - endPoint).GetNormal();
                        if (FindNearestSnapPoint(endPoint, lineDir, GetCandidateBlocks(endPoint, blockGrid, gridSize)) is Point3d nearestToEnd) line.EndPoint = nearestToEnd;
                    }
                    curves = curves.Distinct(new EqualityForOnePlaneCADLine()).ToList();
                    //  W.Ed.WriteMessage($"\n[诊断] 2. 吸附与去重完成: {stopwatch.ElapsedMilliseconds} ms");

                    // =================================================================================
                    // 3. 创建临时线实体用于几何分析 (批处理)
                    // =================================================================================
                    ModelUtil.AddEntities(curves);
                    var tempIdsToDelete = curves.Select(c => c.ObjectId).ToList(); // 此刻ID已有效
                                                                                   //  W.Ed.WriteMessage($"\n[诊断] 3. 创建临时线完成: {stopwatch.ElapsedMilliseconds} ms");

                    // =================================================================================
                    // 4. 路径追踪、打断判断、并收集最终要创建的实体
                    // =================================================================================
                    var finalEntitiesToCreate = new List<Entity>();
                    var linesGroups = GeometryUtil.GroupEntities(curves, 0.01, GeometryUtil.IsOverlapping);
                    var breakLinePoints = breakBlockRefers.Select(b => b.Position.SetZ(0)).ToList();

                    foreach (var linesForOneGroup in linesGroups)
                    {
                        var x = new 线图(linesForOneGroup);
                        var tree = new Dictionary<节点, List<节点>>();
                        foreach (var d in x.dds)
                        {
                            var childs = new List<节点>();
                            foreach (var line in d.连接线) childs.Add(line.GetOther(d));
                            tree.Add(d, childs);
                        }
                        var solution = new Solution();
                        var groups = solution.GroupPaths(tree);

                        foreach (var group in groups)
                        {
                            var point2Ds = new Point2dCollection();
                            foreach (var item in group) point2Ds.Add(item.Pt.ToPoint2d());
                            var pline = ModelUtil.CreatPolyline(point2Ds, 0, false);
                            pline.ColorIndex = 256;
                            pline.Layer = linesForOneGroup.First().Layer;

                            var pointsOnPolyline = breakLinePoints.Where(pt => GeometryUtil.PtRelationToPoly(pline, pt.ToPoint2d(), 0.001) == 0).ToList();
                            if (pointsOnPolyline.Any())
                            {
                                var pts = pointsOnPolyline.OrderBy(p => pline.GetParameterAtPoint(pline.GetClosestPointTo(p, false))).ToList();
                                DBObjectCollection dbCurveList = pline.GetSplitCurves(pts.ToPoint3dCollection());
                                foreach (Entity ent in dbCurveList)
                                {
                                    ent.ColorIndex = 256;
                                    ent.Layer = pline.Layer;
                                    //  ent.ObjectId.AddXData(RegAppName_Pipe, values, W.Trans); // 假设给另一个应用添加数据
                                    finalEntitiesToCreate.Add(ent);
                                }
                            }
                            else
                            {
                                // pline.ObjectId.AddXData(RegAppName_Pipe, values, W.Trans); // 假设给另一个应用添加数据
                                finalEntitiesToCreate.Add(pline);
                            }
                        }
                    }
                    //  W.Ed.WriteMessage($"\n[诊断] 4. 路径追踪与打断判断完成: {stopwatch.ElapsedMilliseconds} ms");

                    // =================================================================================
                    // 5. 创建最终的实体 (批处理)
                    // =================================================================================
                    //  ModelUtil.AddEntities(finalEntitiesToCreate,);

                    // 使用新的 AddEntities 方法
                    //ModelUtil.AddEntities(finalEntitiesToCreate, (entity, transaction) =>
                    //{
                    //    TypedValueList values = new TypedValueList();
                    //    values.Add(new TypedValue(1000, "管道"));
                    //    entity.ObjectId.AddXData(GlobalSetting.RegAppName_Pipe, values, transaction);
                    //});


                    // W.Ed.WriteMessage($"\n[诊断] 5. 创建最终实体完成: {stopwatch.ElapsedMilliseconds} ms");

                    // =================================================================================
                    // 6. 清理所有临时和原始实体 (批处理)
                    // =================================================================================
                    ModelUtil.EraseEntities(originalCurveIdsToDelete);
                    ModelUtil.EraseEntities(tempIdsToDelete);
                    // W.Ed.WriteMessage($"\n[诊断] 6. 清理完成: {stopwatch.ElapsedMilliseconds} ms");
                }
                stopwatch.Stop();
                W.Ed.WriteMessage($"\n--- 总耗时: {stopwatch.ElapsedMilliseconds} ms ---");
            }
            catch (Exception ex)
            {
                MessageBoxUtil.Message_Error(ex.Message + "," + ex.StackTrace);
            }
        }

        #region Helper Methods for Optimization
        private List<BlockReference> GetCandidateBlocks(Point3d point, Dictionary<Tuple<int, int>, List<BlockReference>> grid, double gridSize)
        {
            var candidates = new List<BlockReference>();
            int x = (int)(point.X / gridSize);
            int y = (int)(point.Y / gridSize);
            for (int i = -1; i <= 1; i++)
            {
                for (int j = -1; j <= 1; j++)
                {
                    if (grid.TryGetValue(new Tuple<int, int>(x + i, y + j), out var blockList))
                    {
                        candidates.AddRange(blockList);
                    }
                }
            }
            return candidates;
        }

        private Point3d? FindNearestSnapPoint(Point3d point, Vector3d lineDir, List<BlockReference> candidates)
        {
            if (candidates.Count == 0) return null;
            var nearestBlock = candidates.FirstOrDefault(b => b.Position.SetZ(0).DistanceTo(point) <= 0.0001);
            if (nearestBlock != null) return nearestBlock.Position.SetZ(0);
            nearestBlock = candidates
                .Where(b => b.Position.SetZ(0).DistanceTo(point) <= 1)
                .Where(b => (b.Position.SetZ(0) - point).IsParallelTo(lineDir, new Tolerance(0.05, 0.05)))
                .OrderBy(b => b.Position.SetZ(0).DistanceTo(point))
                .FirstOrDefault();
            return nearestBlock?.Position.SetZ(0);
        }
        #endregion

        public double TraverseEntitiesInBlock(string blockName)
        {
            using (Transaction trans = W.Trans)
            {
                BlockTable bt = (BlockTable)trans.GetObject(W.Db.BlockTableId, OpenMode.ForRead);

                BlockTableRecord btr = null;

                // 查找特定的块定义
                foreach (ObjectId btrId in bt)
                {
                    BlockTableRecord currentBtr = (BlockTableRecord)trans.GetObject(btrId, OpenMode.ForRead);

                    if (currentBtr.Name.ToUpper() == blockName.ToUpper())
                    {
                        btr = currentBtr;
                        break;
                    }
                }

                if (btr != null)
                {
                    // 遍历块定义中的所有实体
                    foreach (ObjectId entId in btr)
                    {
                        Entity ent = (Entity)trans.GetObject(entId, OpenMode.ForRead);
                        if (ent is Circle circle)
                        {
                            return Math.Round(circle.Radius, 2);
                        }
                    }
                }
                trans.Commit();
            }
            return -1;
        }

        private void CheckBox_Click(object sender, RoutedEventArgs e)
        {

        }
        public static void RunLispCmd(string cmd)
        {
            var doc = W.Doc;
            StringBuilder sb = new StringBuilder();
            var cmdActive = CadApp.GetSystemVariable("CMDACTIVE");
            if (null != cmdActive)
            {
                int n = Convert.ToInt32(cmdActive);
                if (n != 0)
                {
                    char c = (char)0x1b;
                    sb.Append(c);
                    sb.Append(c);
                    sb.Append(c);
                    sb.Append(cmd);
                }
            }
            if (sb.Length == 0)
                sb.Append(cmd);
            sb.Append(" ");

            CadApp.DocumentManager.MdiActiveDocument.SendStringToExecute(sb.ToString(), true, false, false);
            // SetFocus(doc.Window.Handle);//这里调用SetFocus可以防止卡顿
        }

        private void SnapLinesToBlocks(List<CadDb.Line> curves, List<BlockReference> blockReferences)
        {
            foreach (var line in curves)
            {
                //判断与直线起点最近的块的基点
                var startPoint = line.StartPoint.SetZ(0);
                var lineDir = line.EndPoint.SetZ(0) - line.StartPoint.SetZ(0);

                var nearestPointToStartPoint = blockReferences.Where(x => x.Position.SetZ(0).DistanceTo(startPoint) <= 0.0001).FirstOrDefault();//1、判断是否有与直线的起点重合的块基点
                if (nearestPointToStartPoint != null) line.StartPoint = nearestPointToStartPoint.Position.SetZ(0);
                else
                {
                    nearestPointToStartPoint = blockReferences.Where(x => x.Position.SetZ(0).DistanceTo(startPoint) <= 1).Where(x => (x.Position.SetZ(0) - startPoint).IsParallelTo(lineDir, new Tolerance(0.05, 0.05))).OrderBy(x => x.Position.SetZ(0).DistanceTo(startPoint)).FirstOrDefault();
                    if (nearestPointToStartPoint != null) line.StartPoint = nearestPointToStartPoint.Position.SetZ(0);
                }
            }

            foreach (var line in curves)
            {
                //判断与直线终点点最近的块的基点
                var endPoint = line.EndPoint.SetZ(0);
                var lineDir = line.EndPoint.SetZ(0) - line.StartPoint.SetZ(0);

                var nearestPointToEndPoint = blockReferences.Where(x => x.Position.SetZ(0).DistanceTo(endPoint) <= 0.0001).FirstOrDefault();//1、判断是否有与直线的终点重合的块基点
                if (nearestPointToEndPoint != null) line.EndPoint = nearestPointToEndPoint.Position.SetZ(0);
                else
                {
                    nearestPointToEndPoint = blockReferences.Where(x => x.Position.SetZ(0).DistanceTo(endPoint) <= 1).Where(x => (x.Position.SetZ(0) - endPoint).IsParallelTo(lineDir, new Tolerance(0.05, 0.05))).OrderBy(x => x.Position.SetZ(0).DistanceTo(endPoint)).FirstOrDefault();
                    if (nearestPointToEndPoint != null) line.EndPoint = nearestPointToEndPoint.Position.SetZ(0);
                }
            }
        }
    }
}


