﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;

namespace SmartRouting.App.Commands.数据录入.Model
{

    /// <summary>
    /// 工井类定义
    /// </summary>
    public class WellModel
    {
        ///// <summary>
        ///// 工井块的ID
        ///// </summary>
        //public ObjectId WellId { get; set; }

        /// <summary>
        /// 工井编号
        /// </summary>
        public string WellName { get; set; }

        /// <summary>
        /// 工井的权属
        /// </summary>
        public string WellTelecomOperator { get; set; }

        /// <summary>
        /// 工井的照片
        /// </summary>
        public string WellImage { get; set; }

        /// <summary>
        /// 一个工井内部线缆的集合
        /// </summary>
        public ObservableCollection<CableModel> CableModels { get; set; }
    }


    /// <summary>
    /// 工井内的线缆类
    /// </summary>

    public class CableModel : INotifyPropertyChanged
    {
        private static readonly List<string> CableSpecificationOptions = new List<string> { "20", "50", "100", "200", "300", "600", "400", "10", "30", "400", "500", "900", "1200" };
        private static readonly List<string> FiberSpecificationOptions = new List<string> { "4", "8", "12", "16", "24", "36", "48", "72", "96", "144", "288" };

        public CableModel()
        {
            // 立即设置默认操作，确保绑定时有值
            OperationEnum = OperationEnum.复制;
            // 初始化可选规格列表
            SpecificationOptions = new ObservableCollection<string>();
            // 根据默认类型填充规格
            UpdateSpecificationOptions();
        }

        private void UpdateSpecificationOptions()
        {
            SpecificationOptions.Clear();
            List<string> options = null;

            switch (CableEntryEnum)
            {
                case CableEntryEnum.光缆:
                    options = FiberSpecificationOptions;
                    break;
                case CableEntryEnum.电缆:
                    options = CableSpecificationOptions;
                    break;
            }

            if (options != null)
            {
                options.ForEach(o => SpecificationOptions.Add(o));
            }

            // 切换列表后，自动选择第一项作为默认值
            Specification = SpecificationOptions.FirstOrDefault();
        }

        private ObservableCollection<string> _specificationOptions;
        private string _cableEntryNumber;
        private TelecomOperatorEnum _telecomOperatorEnum;
        private string _remarks;
        private CableEntryEnum _cableEntryEnum;
        private string _specification;
        private bool _isSelected;
        private bool _used;
        private OperationEnum _operationEnum;
        private string _cableTo;
        private string _cableFrom;

        public event PropertyChangedEventHandler PropertyChanged;

        protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }


        /// <summary>
        /// 线缆序号
        /// </summary>
        public string CableEntryNumber
        {
            get => _cableEntryNumber;
            set
            {
                if (_cableEntryNumber != value)
                {
                    _cableEntryNumber = value;
                    OnPropertyChanged();
                }
            }
        }
        /// <summary>
        /// 可选规格列表
        /// </summary>
        [JsonIgnore]
        [ExtensionDictionaryIgnore]
        public ObservableCollection<string> SpecificationOptions
        {
            get => _specificationOptions;
            set
            {
                if (_specificationOptions != value)
                {
                    _specificationOptions = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        ///  权属
        /// </summary>
        public TelecomOperatorEnum TelecomOperatorEnum
        {
            get => _telecomOperatorEnum;
            set
            {
                if (_telecomOperatorEnum != value)
                {
                    _telecomOperatorEnum = value;
                    OnPropertyChanged();
                }
            }
        }


        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks
        {
            get => _remarks;
            set
            {
                if (_remarks != value)
                {
                    _remarks = value;
                    OnPropertyChanged();
                }
            }
        }


        /// <summary>
        /// 光缆类型
        /// </summary>
        public CableEntryEnum CableEntryEnum
        {
            get => _cableEntryEnum;
            set
            {
                if (_cableEntryEnum != value)
                {
                    _cableEntryEnum = value;

                    // 【核心修正点 1】
                    // 当类型改变时，立即调用方法来更新规格列表
                    UpdateSpecificationOptions();

                    // 【核心修正点 2】
                    // 通知UI，CableEntryEnum 属性本身已经改变
                    OnPropertyChanged();

                    // 【核心修正点 3】
                    // (可选但推荐) 明确通知UI，另一个属性 SpecificationOptions 也因为这次改变而更新了。
                    // 这样做可以确保UI在任何情况下都能正确刷新规格下拉框。
                    OnPropertyChanged(nameof(SpecificationOptions));
                }
            }
        }





























        /// <summary>
        /// 规格
        /// </summary>
        public string Specification
        {
            get => _specification;
            set
            {
                if (_specification != value)
                {
                    _specification = value;
                    OnPropertyChanged();
                }
            }
        }


        /// <summary>
        /// 使用已经使用了
        /// </summary>
        public bool Used
        {
            get => _used;
            set
            {
                if (_used != value)
                {
                    _used = value;
                    OnPropertyChanged();
                }
            }
        }







        /// <summary>
        /// 选择的操作
        /// </summary>

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 操作的方式枚举
        /// </summary>
        public OperationEnum OperationEnum
        {
            get => _operationEnum;
            set
            {
                if (_operationEnum != value)
                {
                    _operationEnum = value;
                    OnPropertyChanged();
                }
            }
        }


        /// <summary>
        /// 用于在UI上显示的动态文本（只读计算属性）
        /// </summary>
        [JsonIgnore]
        [ExtensionDictionaryIgnore]
        public string JointOperationDisplayName
        {
            get
            {
                switch (OperationEnum)
                {
                    case OperationEnum.标记接头_总:
                        return "标记接头_总";
                    case OperationEnum.标记接头_分支:
                        return "标记接头_分支";
                    default:
                        return "标记接头";
                }
            }
        }

        /// <summary>
        /// 流向
        /// </summary>

        public string CableFrom
        {
            get => _cableFrom;
            set
            {
                if (_cableFrom != value)
                {
                    _cableFrom = value;
                    OnPropertyChanged();
                }
            }
        }






        /// <summary>
        /// 流向
        /// </summary>

        public string CableTo
        {
            get => _cableTo;
            set
            {
                if (_cableTo != value)
                {
                    _cableTo = value;
                    OnPropertyChanged();
                }
            }
        }
    }
}
