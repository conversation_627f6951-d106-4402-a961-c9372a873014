﻿using SmartRouting.App.MVVM;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.侧边菜单.Model
{
    public class ParentModel : BindableBase
    {

        /// <summary>
        /// 父节点名称。(如建筑、结构、通用等)
        /// </summary>
        public string ParentName
        {
            get => GetProperty(() => ParentName);
            set => SetProperty(() => ParentName, value);
        }

        /// <summary>
        /// 这个父类下面的子节点
        /// </summary>
        public ObservableCollection<ChildrenModel> Childrens
        {
            get => GetProperty(() => Childrens);
            set => SetProperty(() => Childrens, value);
        }
        public ParentModel(string parentName, ObservableCollection<ChildrenModel> childrens)
        {
            this.ParentName = parentName;
            this.Childrens = childrens;
        }
    }
}
