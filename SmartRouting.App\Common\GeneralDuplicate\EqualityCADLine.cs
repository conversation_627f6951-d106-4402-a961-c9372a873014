﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Common.GeneralDuplicate
{
    internal class EqualityForOnePlaneCADLine : IEqualityComparer<Line>
    {
        public bool Equals(Line x, Line y)
        {
            if (x.StartPoint.SetZ(0).Equals(y.StartPoint.SetZ(0))&& (x.EndPoint.SetZ(0).Equals(y.EndPoint.SetZ(0))))
            {
                return true;
            }
            else if(x.EndPoint.SetZ(0).Equals(y.StartPoint.SetZ(0)) && (x.StartPoint.SetZ(0).Equals(y.EndPoint.SetZ(0))))
            {
                return true;
            }
            return false;
        }

        public int GetHashCode(Line obj)
        {
            return 1;
        }
    }
}
