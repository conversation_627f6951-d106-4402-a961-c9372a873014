﻿using SmartRouting.App.Commands.数据录入.Model;
using SmartRouting.App.Utils.XRecordUtil;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SmartRouting.App.Commands.测试.View
{
    /// <summary>
    /// CableEntryManagerWindow.xaml 的交互逻辑
    /// </summary>
    public partial class CableEntryManagerWindow : Window
    {
        private ObjectId _selectedEntityId = ObjectId.Null;
        private ObservableCollection<CableModel> _records = new ObservableCollection<CableModel>();

        public CableEntryManagerWindow()
        {
            InitializeComponent();
            InitializeComboBoxes();
            DgRecords.ItemsSource = _records;
            UpdateUI();
        }

        private void InitializeComboBoxes()
        {
            // 初始化权属枚举
            CmbTelecomOperator.ItemsSource = Enum.GetValues(typeof(TelecomOperatorEnum));
            CmbTelecomOperator.SelectedIndex = 0;

            // 初始化光缆类型枚举
            CmbCableEntryEnum.ItemsSource = Enum.GetValues(typeof(CableEntryEnum));
            CmbCableEntryEnum.SelectedIndex = 0;

            // 初始化操作枚举
            CmbOperationEnum.ItemsSource = Enum.GetValues(typeof(OperationEnum));
            CmbOperationEnum.SelectedIndex = 0;

            // 触发规格选项更新
            CmbCableEntryEnum_SelectionChanged(null, null);
        }

        private void CmbCableEntryEnum_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbCableEntryEnum.SelectedItem is CableEntryEnum selectedType)
            {
                // 创建临时对象来获取规格选项
                var tempModel = new CableModel();
                tempModel.CableEntryEnum = selectedType;

                CmbSpecification.ItemsSource = tempModel.SpecificationOptions;
                if (tempModel.SpecificationOptions.Count > 0)
                {
                    CmbSpecification.SelectedIndex = 0;
                }
            }
        }

        private void BtnSelectEntity_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                this.Hide();

                using (var locker = W.DocLock)
                {
                    W.FocusDoc();
                    using (Transaction trans = W.Trans)
                    {
                        PromptEntityResult entityResult = W.Ed.GetEntity("\n请选择一个对象:");
                        if (entityResult.Status == PromptStatus.OK)
                        {
                            _selectedEntityId = entityResult.ObjectId;
                            Entity entity = trans.GetObject(_selectedEntityId, OpenMode.ForRead) as Entity;
                            TxtSelectedEntity.Text = $"{entity.GetRXClass().Name} (ID: {_selectedEntityId.Handle})";

                            UpdateStatus("实体选择成功");
                            LoadExistingRecords();
                        }
                        else
                        {
                            UpdateStatus("取消选择实体");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"选择实体失败: {ex.Message}");
            }
            finally
            {
                this.Show();
                this.Activate();
            }

            UpdateUI();
        }

        private void LoadExistingRecords()
        {
            _records.Clear();

            if (_selectedEntityId.IsNull)
                return;

            try
            {
                string recordName = typeof(WellModel).Name; // "CableEntryModel"

                using (var locker = W.DocLock)
                {
                    W.FocusDoc();

                    var typeValueList = _selectedEntityId.GetObjXrecordWithOutTrans(recordName);
                    if (typeValueList != null)
                    {
                        var record = GenericExtensionDictionaryManager.TypeValueListToObject<CableModel>(typeValueList);
                        if (record != null)
                        {
                            _records.Add(record);
                            UpdateStatus($"加载到 1 个 {recordName} 扩展记录");

                            // 自动填充到编辑界面
                            FillEditControls(record);
                        }
                    }
                    else
                    {
                        UpdateStatus($"未找到名为 {recordName} 的扩展记录");
                        // 创建默认对象并填充界面，而不是清空
                        var defaultModel = new CableModel();
                        FillEditControls(defaultModel);
                    }
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"加载扩展记录失败: {ex.Message}");
                // 出错时也创建默认对象
                try
                {
                    var defaultModel = new CableModel();
                    FillEditControls(defaultModel);
                }
                catch
                {
                    ClearEditControls();
                }
            }
        }

        private void FillEditControls(CableModel model)
        {
            try
            {
                TxtCableEntryNumber.Text = model.CableEntryNumber ?? "";

                // 安全设置枚举值
                if (Enum.IsDefined(typeof(TelecomOperatorEnum), model.TelecomOperatorEnum))
                {
                    CmbTelecomOperator.SelectedItem = model.TelecomOperatorEnum;
                }
                else
                {
                    CmbTelecomOperator.SelectedIndex = 0;
                }

                TxtRemarks.Text = model.Remarks ?? "";

                if (Enum.IsDefined(typeof(CableEntryEnum), model.CableEntryEnum))
                {
                    CmbCableEntryEnum.SelectedItem = model.CableEntryEnum;
                }
                else
                {
                    CmbCableEntryEnum.SelectedIndex = 0;
                }

                // 等待规格选项更新后再设置规格值
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    if (!string.IsNullOrEmpty(model.Specification) &&
                        CmbSpecification.Items.Contains(model.Specification))
                    {
                        CmbSpecification.SelectedItem = model.Specification;
                    }
                    else if (CmbSpecification.Items.Count > 0)
                    {
                        CmbSpecification.SelectedIndex = 0;
                    }
                }));

                ChkIsSelected.IsChecked = model.IsSelected;

                if (Enum.IsDefined(typeof(OperationEnum), model.OperationEnum))
                {
                    CmbOperationEnum.SelectedItem = model.OperationEnum;
                }
                else
                {
                    CmbOperationEnum.SelectedIndex = 0;
                }

                TxtState.Text = model.CableTo ?? "";
            }
            catch (Exception ex)
            {
                UpdateStatus($"填充控件失败: {ex.Message}");
                ClearEditControls();
            }
        }

        private void ClearEditControls()
        {
            try
            {
                TxtCableEntryNumber.Clear();
                CmbTelecomOperator.SelectedIndex = 0;
                TxtRemarks.Clear();
                CmbCableEntryEnum.SelectedIndex = 0;
                if (CmbSpecification.Items.Count > 0)
                    CmbSpecification.SelectedIndex = 0;
                ChkIsSelected.IsChecked = false;
                CmbOperationEnum.SelectedIndex = 0;
                TxtState.Clear();
            }
            catch (Exception ex)
            {
                UpdateStatus($"清空控件失败: {ex.Message}");
            }
        }

        private CableModel CreateModelFromControls()
        {
            var model = new CableModel
            {
                CableEntryNumber = TxtCableEntryNumber.Text?.Trim() ?? "",
                TelecomOperatorEnum = (TelecomOperatorEnum)(CmbTelecomOperator.SelectedItem ?? default(TelecomOperatorEnum)),
                Remarks = TxtRemarks.Text?.Trim() ?? "",
                CableEntryEnum = (CableEntryEnum)(CmbCableEntryEnum.SelectedItem ?? default(CableEntryEnum)),
                Specification = CmbSpecification.SelectedItem?.ToString() ?? "",
                IsSelected = ChkIsSelected.IsChecked ?? false,
                OperationEnum = (OperationEnum)(CmbOperationEnum.SelectedItem ?? default(OperationEnum)),
                CableTo = TxtState.Text?.Trim() ?? ""
            };

            return model;
        }

        private void BtnAdd_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput()) return;

            try
            {
                using (var locker = W.DocLock)
                {
                    W.FocusDoc();

                    var model = CreateModelFromControls();
                    var typeValueList = GenericExtensionDictionaryManager.ObjectToTypeValueList(model);

                    string recordName = typeof(WellModel).Name;
                    bool success = _selectedEntityId.AddXRecordToObj(recordName, typeValueList);

                    if (success)
                    {
                        UpdateStatus($"扩展记录 {recordName} 添加成功");
                        LoadExistingRecords();
                    }
                    else
                    {
                        UpdateStatus($"扩展记录 {recordName} 已存在，添加失败");
                    }
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"添加扩展记录失败: {ex.Message}");
            }
        }

        private void BtnUpdate_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput()) return;

            try
            {
                using (var locker = W.DocLock)
                {
                    W.FocusDoc();

                    var model = CreateModelFromControls();
                    var typeValueList = GenericExtensionDictionaryManager.ObjectToTypeValueList(model);

                    string recordName = typeof(WellModel).Name;
                    bool success = _selectedEntityId.ModObjXrecord(recordName, typeValueList);

                    if (success)
                    {
                        UpdateStatus($"扩展记录 {recordName} 更新成功");
                        LoadExistingRecords();
                    }
                    else
                    {
                        UpdateStatus($"扩展记录 {recordName} 不存在，更新失败");
                    }
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"更新扩展记录失败: {ex.Message}");
            }
        }

        private void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedEntityId.IsNull)
            {
                MessageBox.Show("请先选择实体", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show("确定要删除CableEntryModel扩展记录吗？",
                "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    using (var locker = W.DocLock)
                    {
                        W.FocusDoc();

                        string recordName = typeof(WellModel).Name;
                        bool success = _selectedEntityId.DelObjXrecord(recordName);

                        if (success)
                        {
                            UpdateStatus($"扩展记录 {recordName} 删除成功");
                            ClearEditControls();
                            LoadExistingRecords();
                        }
                        else
                        {
                            UpdateStatus($"扩展记录 {recordName} 不存在或删除失败");
                        }
                    }
                }
                catch (Exception ex)
                {
                    UpdateStatus($"删除扩展记录失败: {ex.Message}");
                }
            }
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearEditControls();
            UpdateStatus("界面已清空");
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadExistingRecords();
            UpdateStatus("数据已刷新");
        }

        private void DgRecords_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DgRecords.SelectedItem is CableModel selectedRecord)
            {
                FillEditControls(selectedRecord);
                UpdateStatus("已加载选中记录");
            }
        }

        private bool ValidateInput()
        {
            if (_selectedEntityId.IsNull)
            {
                MessageBox.Show("请先选择一个实体", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtCableEntryNumber.Text))
            {
                MessageBox.Show("请输入序号", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void UpdateUI()
        {
            bool hasEntity = !_selectedEntityId.IsNull;

            BtnAdd.IsEnabled = hasEntity;
            BtnUpdate.IsEnabled = hasEntity;
            BtnDelete.IsEnabled = hasEntity;
            BtnRefresh.IsEnabled = hasEntity;
        }

        private void UpdateStatus(string message)
        {
            TxtStatus.Text = $"{DateTime.Now:HH:mm:ss} - {message}";
        }
    }
}
