﻿
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using ICSharpCode.AvalonEdit.Highlighting;
using SmartRouting.App.Properties;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Xml;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.Emit;
using Newtonsoft.Json;
using Path = System.IO.Path;
using Match = System.Text.RegularExpressions.Match;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using HttpMethod = System.Net.Http.HttpMethod;

namespace SmartRouting.App.Commands.常用.AI助手.View
{
    /// <summary>
    /// AICopilotView.xaml 的交互逻辑
    /// </summary>
    public partial class AICopilotView : Window
    {
        private double _aspectRatio;
        private bool _isResizing;
        private bool _aspectRatioInitialized = false;
        private bool _headerRemoved;

        private static readonly HttpClient client = new HttpClient();

        public AICopilotView()
        {
            InitializeComponent();
            SetResourceReference(BackgroundProperty, "WindowBackgroundColor");
            LoadCustomHighlighting();
            this.ContentRendered += AICopilotView_ContentRendered;
        }

        private void LoadCustomHighlighting()
        {
            try
            {
                var assembly = typeof(AICopilotView).Assembly;
                var resourceNames = assembly.GetManifestResourceNames();

                string resourceName = resourceNames.FirstOrDefault(name =>
                    name.EndsWith("CustomCSharp-Dark.xshd", StringComparison.OrdinalIgnoreCase));

                if (string.IsNullOrEmpty(resourceName))
                {
                    System.Diagnostics.Debug.WriteLine("可用的嵌入资源:");
                    foreach (var name in resourceNames)
                    {
                        System.Diagnostics.Debug.WriteLine($"  - {name}");
                    }

                    codeEditor.SyntaxHighlighting = HighlightingManager.Instance.GetDefinition("C#");
                    return;
                }

                using (Stream s = assembly.GetManifestResourceStream(resourceName))
                {
                    if (s == null)
                    {
                        codeEditor.SyntaxHighlighting = HighlightingManager.Instance.GetDefinition("C#");
                        System.Diagnostics.Debug.WriteLine($"无法找到资源: {resourceName}");
                        return;
                    }

                    using (XmlReader reader = new XmlTextReader(s))
                    {
                        codeEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                        System.Diagnostics.Debug.WriteLine("自定义高亮加载成功");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载自定义高亮时发生错误: {ex.Message}");
                try
                {
                    codeEditor.SyntaxHighlighting = HighlightingManager.Instance.GetDefinition("C#");
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"设置默认高亮时也发生错误: {fallbackEx.Message}");
                }
            }
        }

        private void AICopilotView_ContentRendered(object sender, EventArgs e)
        {
            this.ContentRendered -= AICopilotView_ContentRendered;

            if (this.ActualWidth > 0 && this.ActualHeight > 0)
            {
                _aspectRatio = this.ActualWidth / this.ActualHeight;
                _aspectRatioInitialized = true;
            }
            else if (this.Width > 0 && this.Height > 0 && !double.IsNaN(this.Width) && !double.IsNaN(this.Height) && this.Height != 0)
            {
                _aspectRatio = this.Width / this.Height;
                _aspectRatioInitialized = true;
            }
            else
            {
                _aspectRatio = 800.0 / 600.0;
                _aspectRatioInitialized = true;
            }

            this.SizeToContent = SizeToContent.Manual;
            this.SizeChanged += AICopilotView_SizeChanged;
            _isResizing = false;
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            codeEditor.Text = Settings.Default.LastCodes;
            txtPrompt.Text = Settings.Default.Prompt;
        }

        private void AICopilotView_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (!_aspectRatioInitialized || _isResizing || this.WindowState == WindowState.Maximized || _aspectRatio == 0)
                return;

            _isResizing = true;

            if (e.WidthChanged && e.NewSize.Width > 0)
            {
                double newHeight = e.NewSize.Width / _aspectRatio;
                this.Height = newHeight;
            }
            else if (e.HeightChanged && e.NewSize.Height > 0)
            {
                double newWidth = e.NewSize.Height * _aspectRatio;
                this.Width = newWidth;
            }

            _isResizing = false;
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            var settingsView = new ApiKeySettingsView { Owner = this };
            settingsView.ShowDialog();
        }

        private async void GenerateCode_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(Settings.Default.ApiKey))
            {
                MessageBox.Show("请先在设置中输入您的API Key。", "需要API Key", MessageBoxButton.OK, MessageBoxImage.Warning);
                SettingsButton_Click(this, new RoutedEventArgs());
                return;
            }

            string prompt = txtPrompt.Text;
            if (string.IsNullOrWhiteSpace(prompt))
            {
                System.Windows.MessageBox.Show("请输入提示词！", "提示");
                return;
            }

            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

            string waitingMessage = "正在执行，请稍候...\n";
            codeEditor.Text = waitingMessage;

            bool firstChunkProcessed = false;
            _headerRemoved = false;

            const string basePrompt = "用C#为CAD编写代码。只输出C#代码，不需要任何解释或分析。代码必须包含完整的using声明、一个公共类以及一个可执行的公共方法。";

#if AC13
            const string apiContext = "你正在为AutoCAD编写代码，请使用正确的Autodesk命名空间，例如 'using Autodesk.AutoCAD.ApplicationServices;'。";
#elif ZW25
            const string apiContext = "你正在为ZWCAD(中望CAD)编写代码，请务必使用正确的 'ZwSoft.ZwCAD' 前缀命名空间，例如 'using ZwSoft.ZwCAD.ApplicationServices;'、'using ZwSoft.ZwCAD.DatabaseServices;' 等。绝对不要使用 'ZWCAD.*'。";
#endif
            string finalSystemPrompt = $"{basePrompt} {apiContext}";

            try
            {
                await GetResponseAsync(
                    finalSystemPrompt,
                    prompt,
                    chunk =>
                    {
                        Dispatcher.Invoke(() =>
                        {
                            if (!firstChunkProcessed)
                            {
                                codeEditor.Text = "";
                                firstChunkProcessed = true;
                            }

                            codeEditor.AppendText(chunk);

                            if (!_headerRemoved)
                            {
                                string currentText = codeEditor.Text;
                                var match = Regex.Match(currentText, @"^```(csharp|C#)?\s*\n");
                                if (match.Success)
                                {
                                    codeEditor.Text = currentText.Substring(match.Length);
                                    _headerRemoved = true;
                                }
                            }

                            codeEditor.ScrollToEnd();
                        });
                    },
                    () =>
                    {
                        Dispatcher.Invoke(() =>
                        {
                            if (!firstChunkProcessed)
                            {
                                codeEditor.Text = "API未返回任何内容。\n";
                            }
                            else
                            {
                                string currentText = codeEditor.Text;
                                if (currentText.EndsWith("```"))
                                {
                                    codeEditor.Text = currentText.Substring(0, currentText.Length - 3).TrimEnd();
                                }
                            }
                        });
                    },
                    errorMessage =>
                    {
                        Dispatcher.Invoke(() =>
                        {
                            string errorDisplay = $"\n错误: {errorMessage}\n";
                            if (!firstChunkProcessed)
                            {
                                codeEditor.Text = errorDisplay;
                            }
                            else
                            {
                                codeEditor.AppendText(errorDisplay);
                            }
                        });
                    }
                );
            }
            catch (System.Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    string errorDisplay = $"\n调用API时发生意外错误: {ex.Message}\n";
                    if (!firstChunkProcessed)
                    {
                        codeEditor.Text = errorDisplay;
                    }
                    else
                    {
                        codeEditor.AppendText(errorDisplay);
                    }
                });
            }
        }

        private void RunCode_Click(object sender, RoutedEventArgs e)
        {
            string userCode = codeEditor.Text;

            if (string.IsNullOrWhiteSpace(userCode))
            {
                System.Windows.MessageBox.Show("请输入要执行的代码！", "提示");
                return;
            }

            try
            {
                string fullCode = userCode;
                SyntaxTree syntaxTree = CSharpSyntaxTree.ParseText(fullCode);
                string assemblyName = $"DynamicAssembly_{Guid.NewGuid():N}";

                var references = new List<MetadataReference>();
                var diagnosticMessages = new List<string>(); // 用于收集诊断消息

                // 添加基础引用
                try
                {
                    references.Add(MetadataReference.CreateFromFile(typeof(object).Assembly.Location));
                    references.Add(MetadataReference.CreateFromFile(typeof(System.Linq.Enumerable).Assembly.Location));
                    references.Add(MetadataReference.CreateFromFile(typeof(System.Diagnostics.Debug).Assembly.Location));
                    references.Add(MetadataReference.CreateFromFile(typeof(System.IO.Path).Assembly.Location));
                    references.Add(MetadataReference.CreateFromFile(typeof(System.Collections.Generic.List<>).Assembly.Location));
                    references.Add(MetadataReference.CreateFromFile(typeof(System.Environment).Assembly.Location));

                    var currentAssembly = Assembly.GetExecutingAssembly();
                    if (!string.IsNullOrEmpty(currentAssembly.Location) && File.Exists(currentAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(currentAssembly.Location));
                        diagnosticMessages.Add($"成功添加当前程序集引用: {currentAssembly.Location}");
                    }
                    else
                    {
                        diagnosticMessages.Add($"当前程序集位置无效或文件不存在: {currentAssembly.Location}");
                    }
                }
                catch (Exception ex)
                {
                    diagnosticMessages.Add($"添加基础引用时出错: {ex.Message}");
                }

                // 添加CAD相关引用 - 支持AutoCAD和中望CAD
                try
                {
#if AC13
                    // AutoCAD引用
                    var acAppAssembly = typeof(Autodesk.AutoCAD.ApplicationServices.Application).Assembly;
                    var acDbAssembly = typeof(Autodesk.AutoCAD.DatabaseServices.Database).Assembly;
                    var acEdAssembly = typeof(Autodesk.AutoCAD.EditorInput.Editor).Assembly;
                    var acGeomAssembly = typeof(Autodesk.AutoCAD.Geometry.Point3d).Assembly;

                    if (!string.IsNullOrEmpty(acAppAssembly.Location) && File.Exists(acAppAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(acAppAssembly.Location));
                       // diagnosticMessages.Add($"成功添加AutoCAD Application引用: {acAppAssembly.Location}");
                    }
                    if (!string.IsNullOrEmpty(acDbAssembly.Location) && File.Exists(acDbAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(acDbAssembly.Location));
                      //  diagnosticMessages.Add($"成功添加AutoCAD Database引用: {acDbAssembly.Location}");
                    }
                    if (!string.IsNullOrEmpty(acEdAssembly.Location) && File.Exists(acEdAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(acEdAssembly.Location));
                      //  diagnosticMessages.Add($"成功添加AutoCAD Editor引用: {acEdAssembly.Location}");
                    }
                    if (!string.IsNullOrEmpty(acGeomAssembly.Location) && File.Exists(acGeomAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(acGeomAssembly.Location));
                      //  diagnosticMessages.Add($"成功添加AutoCAD Geometry引用: {acGeomAssembly.Location}");
                    }
#elif ZW25
                    // 中望CAD引用
                    var zwAppAssembly = typeof(ZwSoft.ZwCAD.ApplicationServices.Application).Assembly;
                    var zwDbAssembly = typeof(ZwSoft.ZwCAD.DatabaseServices.Database).Assembly;
                    var zwEdAssembly = typeof(ZwSoft.ZwCAD.EditorInput.Editor).Assembly;
                    var zwGeomAssembly = typeof(ZwSoft.ZwCAD.Geometry.Point3d).Assembly;
                    
                    if (!string.IsNullOrEmpty(zwAppAssembly.Location) && File.Exists(zwAppAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(zwAppAssembly.Location));
                        diagnosticMessages.Add($"成功添加中望CAD Application引用: {zwAppAssembly.Location}");
                    }
                    if (!string.IsNullOrEmpty(zwDbAssembly.Location) && File.Exists(zwDbAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(zwDbAssembly.Location));
                        diagnosticMessages.Add($"成功添加中望CAD Database引用: {zwDbAssembly.Location}");
                    }
                    if (!string.IsNullOrEmpty(zwEdAssembly.Location) && File.Exists(zwEdAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(zwEdAssembly.Location));
                        diagnosticMessages.Add($"成功添加中望CAD Editor引用: {zwEdAssembly.Location}");
                    }
                    if (!string.IsNullOrEmpty(zwGeomAssembly.Location) && File.Exists(zwGeomAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(zwGeomAssembly.Location));
                        diagnosticMessages.Add($"成功添加中望CAD Geometry引用: {zwGeomAssembly.Location}");
                    }
#endif
                }
                catch (Exception ex)
                {
                    diagnosticMessages.Add($"添加CAD引用时出错: {ex.Message}");
                }

                // 添加WPF和.NET Framework引用
                try
                {
                    var runtimeAssembly = typeof(System.Runtime.CompilerServices.DynamicAttribute).Assembly;
                    var messageBoxAssembly = typeof(System.Windows.MessageBox).Assembly;
                    var dependencyObjAssembly = typeof(System.Windows.DependencyObject).Assembly;
                    var uiElementAssembly = typeof(System.Windows.UIElement).Assembly;
                    var xamlAssembly = typeof(System.Xaml.XamlType).Assembly;

                    if (!string.IsNullOrEmpty(runtimeAssembly.Location) && File.Exists(runtimeAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(runtimeAssembly.Location));
                       // diagnosticMessages.Add($"成功添加Runtime引用: {runtimeAssembly.Location}");
                    }
                    if (!string.IsNullOrEmpty(messageBoxAssembly.Location) && File.Exists(messageBoxAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(messageBoxAssembly.Location));
                       // diagnosticMessages.Add($"成功添加MessageBox引用: {messageBoxAssembly.Location}");
                    }
                    if (!string.IsNullOrEmpty(dependencyObjAssembly.Location) && File.Exists(dependencyObjAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(dependencyObjAssembly.Location));
                      //  diagnosticMessages.Add($"成功添加DependencyObject引用: {dependencyObjAssembly.Location}");
                    }
                    if (!string.IsNullOrEmpty(uiElementAssembly.Location) && File.Exists(uiElementAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(uiElementAssembly.Location));
                      //  diagnosticMessages.Add($"成功添加UIElement引用: {uiElementAssembly.Location}");
                    }
                    if (!string.IsNullOrEmpty(xamlAssembly.Location) && File.Exists(xamlAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(xamlAssembly.Location));
                       // diagnosticMessages.Add($"成功添加XamlType引用: {xamlAssembly.Location}");
                    }

                    try
                    {
                        var systemRuntimeAssembly = Assembly.Load("System.Runtime, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a");
                        if (!string.IsNullOrEmpty(systemRuntimeAssembly.Location) && File.Exists(systemRuntimeAssembly.Location))
                        {
                            references.Add(MetadataReference.CreateFromFile(systemRuntimeAssembly.Location));
                           // diagnosticMessages.Add($"成功添加System.Runtime引用: {systemRuntimeAssembly.Location}");
                        }
                    }
                    catch (Exception sysRuntimeEx)
                    {
                        diagnosticMessages.Add($"加载System.Runtime程序集时出错: {sysRuntimeEx.Message}");
                    }
                }
                catch (Exception ex)
                {
                    diagnosticMessages.Add($"添加WPF引用时出错: {ex.Message}");
                }

                // 添加 Microsoft.Office.Interop.Excel 引用
                try
                {
                    // This requires the project to have a reference to the Excel Interop Assembly.
                    var excelAssembly = typeof(Microsoft.Office.Interop.Excel.ApplicationClass).Assembly;
                    if (!string.IsNullOrEmpty(excelAssembly.Location) && File.Exists(excelAssembly.Location))
                    {
                        references.Add(MetadataReference.CreateFromFile(excelAssembly.Location));
                        //diagnosticMessages.Add($"成功添加 Microsoft.Office.Interop.Excel 引用: {excelAssembly.Location}");
                    }
                }
                catch (Exception ex)
                {
                    diagnosticMessages.Add($"添加 Microsoft.Office.Interop.Excel 引用失败: {ex.Message}。请确保您的项目已引用 'Microsoft.Office.Interop.Excel' COM组件。");
                }

                if (references.Count == 0)
                {
                    string message = "无法加载任何必要的程序集引用，无法编译代码。\n\n" + string.Join("\n", diagnosticMessages);
                    System.Windows.MessageBox.Show(message, "编译错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                CSharpCompilation compilation = CSharpCompilation.Create(
                assemblyName,
                    syntaxTrees: new[] { syntaxTree },
                    references: references,
                    options: new CSharpCompilationOptions(OutputKind.DynamicallyLinkedLibrary));

                using (var ms = new MemoryStream())
                {
                    EmitResult result = compilation.Emit(ms);

                    if (!result.Success)
                    {
                        IEnumerable<Diagnostic> failures = result.Diagnostics.Where(diagnostic =>
                            diagnostic.IsWarningAsError ||
                            diagnostic.Severity == DiagnosticSeverity.Error);

                        string errors = string.Join("\n", failures.Select(f => $"{f.Id}: {f.GetMessage()} (在 {f.Location.GetLineSpan().StartLinePosition.Line + 1} 行)"));
                        string finalMessage = $"代码编译失败：\n{errors}\n\n程序集加载诊断:\n{string.Join("\n", diagnosticMessages)}";
                        System.Windows.MessageBox.Show(finalMessage, "编译错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                    else
                    {
                        ms.Seek(0, SeekOrigin.Begin);
                        var assemblyBytes = ms.ToArray();

                        Action executionAction = () =>
                        {
                            var doc = W.Doc;
                            if (doc == null)
                            {
                                System.Windows.MessageBox.Show("没有活动的文档！", "错误");
                                return;
                            }
                            var ed = W.Ed;
                            var db = W.Db;

                            Assembly assembly = Assembly.Load(assemblyBytes);

                            using (DocumentLock loc = doc.LockDocument())
                            {
                                using (Transaction tr = db.TransactionManager.StartTransaction())
                                {
                                    try
                                    {
                                        string className = "";
                                        string nameSpace = "";
                                        Match match = Regex.Match(fullCode, @"class\s+(\w+)");
                                        if (match.Success)
                                        {
                                            className = match.Groups[1].Value;
                                        }

                                        string pattern = @"namespace\s+([^\s{]+)";
                                        Match matchNameSpace = Regex.Match(fullCode, pattern);
                                        if (matchNameSpace.Success)
                                        {
                                            nameSpace = matchNameSpace.Groups[1].Value;
                                        }

                                        Type type = null;
                                        if (!string.IsNullOrEmpty(nameSpace))
                                        {
                                            type = assembly.GetType(nameSpace + "." + className);
                                        }
                                        else
                                        {
                                            type = assembly.GetType(className);
                                        }

                                        if (type == null)
                                        {
                                            ed.WriteMessage($"\n错误：在编译后的程序集中找不到类 '{className}'。");
                                            tr.Abort();
                                            return;
                                        }

                                        object instance = Activator.CreateInstance(type);
                                        MethodInfo method = type.GetMethods().FirstOrDefault(m => !m.IsSpecialName);
                                        if (method != null)
                                        {
                                            method.Invoke(instance, null);
                                            tr.Commit();
                                            ed.WriteMessage("\n动态代码执行完毕。\n");
                                        }
                                        else
                                        {
                                            ed.WriteMessage($"\n错误：在类 '{className}' 中找不到可执行的公共方法。");
                                            tr.Abort();
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        tr.Abort();
                                        ed.WriteMessage($"\n执行动态代码时发生错误：\n{ex.ToString()}");
                                    }
                                }
                            }
                        };

                        CadTaskScheduler.Enqueue(executionAction);
                        System.Windows.MessageBox.Show("代码已提交执行，请查看AutoCAD命令行了解结果。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"执行代码时发生意外错误: {ex.ToString()}", "运行时错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            Settings.Default.LastCodes = codeEditor.Text;
            Settings.Default.Prompt = txtPrompt.Text;
            Settings.Default.Save();
        }

        public async Task GetResponseAsync(string systemPrompt, string userMessage, Action<string> onChunkReceived, Action onStreamCompleted, Action<string> onErrorOccurred)
        {
            string apiKey = Settings.Default.ApiKey;
            string apiUrl = "https://api.deepseek.com/chat/completions";

            try
            {
                var requestBody = new
                {
                    model = "deepseek-chat",
                    messages = new[]
                    {
                        new { role = "system", content = systemPrompt },
                        new { role = "user", content = userMessage }
                    },
                    stream = true
                };

                string jsonRequestBody = JsonConvert.SerializeObject(requestBody);

                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = new StringContent(jsonRequestBody, Encoding.UTF8, "application/json")
                };
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);

                using (HttpResponseMessage response = await client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        using (var responseStream = await response.Content.ReadAsStreamAsync())
                        using (var reader = new StreamReader(responseStream))
                        {
                            string line;
                            while ((line = await reader.ReadLineAsync()) != null)
                            {
                                if (string.IsNullOrWhiteSpace(line)) continue;

                                if (line.StartsWith("data: "))
                                {
                                    string jsonData = line.Substring("data: ".Length).Trim();
                                    if (jsonData.Equals("[DONE]", StringComparison.OrdinalIgnoreCase))
                                    {
                                        break;
                                    }

                                    try
                                    {
                                        var chunkObject = JsonConvert.DeserializeObject<dynamic>(jsonData);
                                        string content = chunkObject?.choices[0]?.delta?.content;
                                        if (!string.IsNullOrEmpty(content))
                                        {
                                            onChunkReceived?.Invoke(content);
                                        }
                                    }
                                    catch (JsonException jsonEx)
                                    {
                                        onErrorOccurred?.Invoke($"解析数据流时出错: {jsonEx.Message}");
                                    }
                                }
                            }
                        }
                        onStreamCompleted?.Invoke();
                    }
                    else
                    {
                        string errorContent = await response.Content.ReadAsStringAsync();
                        onErrorOccurred?.Invoke($"请求失败: {response.StatusCode} - {errorContent}");
                    }
                }
            }
            catch (HttpRequestException httpEx)
            {
                onErrorOccurred?.Invoke($"API网络请求出错: {httpEx.Message}");
            }
            catch (Exception ex)
            {
                onErrorOccurred?.Invoke($"API调用时发生未知错误: {ex.Message}");
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}