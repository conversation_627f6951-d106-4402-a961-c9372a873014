﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Common.GeneralDuplicate
{
    internal class EqualityBlockName : IEqualityComparer<BlockReference>
    {
        public bool Equals(BlockReference x, BlockReference y)
        {
            return x.Name.Equals(y.Name);
        }

        public int GetHashCode(BlockReference obj)
        {
            return 1;
        }
    }
}
