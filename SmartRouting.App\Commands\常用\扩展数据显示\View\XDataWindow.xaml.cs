﻿using SmartRouting.App.Commands.常用.扩展数据显示.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SmartRouting.App.Commands.常用.扩展数据显示.View
{
    /// <summary>
    /// XDataWindow.xaml 的交互逻辑
    /// </summary>
    public partial class XDataWindow : Window
    {
        public XDataWindow(Dictionary<string, List<Tuple<int, object>>> xDataDict)
        {
            InitializeComponent2();
            LoadData(xDataDict);
        }
        private void LoadData(Dictionary<string, List<Tuple<int, object>>> xDataDict)
        {
            var dataItems = new List<XDataItem>();

            foreach (var app in xDataDict)
            {
                foreach (var item in app.Value)
                {
                    dataItems.Add(new XDataItem(
                        app.Key,
                        item.Item1,
                        item.Item2
                    ));
                }
            }
            dataGrid.ItemsSource = dataItems;
        }

        private void InitializeComponent2()
        {
            this.Title = "实体扩展数据查看器";
            this.Width = 800;
            this.Height = 600;
            this.WindowStartupLocation = WindowStartupLocation.CenterScreen;

            // 创建主布局
            var grid = new Grid();

            // 创建DataGrid
            dataGrid = new DataGrid
            {
                AutoGenerateColumns = false,
                IsReadOnly = true,
                Margin = new Thickness(10)
            };

            // 添加列
            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "应用程序",
                Binding = new System.Windows.Data.Binding("Application"),
                Width = new DataGridLength(1, DataGridLengthUnitType.Star)
            });

            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "类型码",
                Binding = new System.Windows.Data.Binding("TypeCode"),
                Width = 100
            });

            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "值",
                Binding = new System.Windows.Data.Binding("Value"),
                Width = new DataGridLength(2, DataGridLengthUnitType.Star)
            });

            grid.Children.Add(dataGrid);
            this.Content = grid;
        }

        private DataGrid dataGrid;
    }
}
