﻿using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using DataTable = System.Data.DataTable;

namespace SmartRouting.App.Commands.光缆数据.View
{
    /// <summary>
    /// WpfVisualSelect.xaml 的交互逻辑
    /// </summary>
    public partial class WpfVisualSelect : Window
    {
        // 公共属性，用于将结果返回给调用方
        public List<List<string>> SelectedData { get; private set; }
        private DataTable _dataTable;

        public WpfVisualSelect(string filePath, string sheetName)
        {
            InitializeComponent();
            this.Title = $"WPF 可视化选择 - {sheetName}";
            LoadDataIntoGrid(filePath, sheetName);
        }

        private void LoadDataIntoGrid(string filePath, string sheetName)
        {
            _dataTable = new DataTable();
            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                var worksheet = package.Workbook.Worksheets[sheetName];
                if (worksheet == null || worksheet.Dimension == null)
                {
                    MessageBox.Show("工作表为空或无法读取。");
                    return;
                }

                var start = worksheet.Dimension.Start;
                var end = worksheet.Dimension.End;

                // 1. 创建DataTable的列 (使用第一行作为表头)
                for (int col = start.Column; col <= end.Column; col++)
                {
                    // 使用Excel的列字母(A, B, C...)作为列名，确保唯一性
                    _dataTable.Columns.Add(worksheet.Cells[1, col].Address.Substring(0, 1));
                }

                // 2. 填充DataTable的行
                for (int row = start.Row; row <= end.Row; row++)
                {
                    var newRow = _dataTable.NewRow();
                    for (int col = start.Column; col <= end.Column; col++)
                    {
                        newRow[col - start.Column] = worksheet.Cells[row, col].Text;
                    }
                    _dataTable.Rows.Add(newRow);
                }
            }

            // 3. 将DataTable绑定到DataGrid
            dataGrid.ItemsSource = _dataTable.DefaultView;
        }

        private void BtnImport_Click(object sender, RoutedEventArgs e)
        {
            if (dataGrid.SelectedCells.Count == 0)
            {
                MessageBox.Show("请至少选择一个单元格！", "提示");
                return;
            }

            // 1. 找到所有选中单元格的最小和最大行列索引
            var selectedCells = dataGrid.SelectedCells;
            int minRow = selectedCells.Min(c => _dataTable.Rows.IndexOf(((DataRowView)c.Item).Row));
            int maxRow = selectedCells.Max(c => _dataTable.Rows.IndexOf(((DataRowView)c.Item).Row));
            int minCol = selectedCells.Min(c => c.Column.DisplayIndex);
            int maxCol = selectedCells.Max(c => c.Column.DisplayIndex);

            // 2. 根据这个外包矩形，从 DataTable 中提取数据
            SelectedData = new List<List<string>>();
            for (int i = minRow; i <= maxRow; i++)
            {
                var rowData = new List<string>();
                for (int j = minCol; j <= maxCol; j++)
                {
                    rowData.Add(_dataTable.Rows[i][j].ToString());
                }
                SelectedData.Add(rowData);
            }

            this.DialogResult = true; // 表示成功
            this.Close();
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false; // 表示取消
            this.Close();
        }
    }
}
