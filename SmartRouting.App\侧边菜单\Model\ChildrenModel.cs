﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.侧边菜单.Model
{
    public class ChildrenModel : BindableBase
    {
        /// <summary>
        /// 命令名称
        /// </summary>
        public string ChildrenName
        {
            get => GetProperty(() => ChildrenName);
            set => SetProperty(() => ChildrenName, value);
        }

        /// <summary>
        /// 快捷键
        /// </summary>
        public string ShortCut
        {
            get => GetProperty(() => ShortCut);
            set => SetProperty(() => ShortCut, value);
        }


        /// <summary>
        /// 快捷键
        /// </summary>
        public string ToolTip
        {
            get => GetProperty(() => ToolTip);
            set => SetProperty(() => ToolTip, value);
        }


        public ChildrenModel(string childrenName, string shortCut, string toolTip = "")
        {
            this.ChildrenName = childrenName;
            this.ShortCut = shortCut;
            this.ToolTip = toolTip;
        }
    }
}
