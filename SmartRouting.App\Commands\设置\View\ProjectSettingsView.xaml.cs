﻿using SmartRouting.App.Commands.图层管理.Model;
using SmartRouting.App.Commands.线路连接.Model;
using SmartRouting.App.Commands.设置.Model;
using SmartRouting.App.Properties;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SmartRouting.App.Commands.设置.View
{
    ///// <summary>
    ///// ProjectSettingsView.xaml 的交互逻辑
    ///// </summary>
    public partial class ProjectSettingsView : Window
    {
        public ObservableCollection<BlockItem> blockModels = new ObservableCollection<BlockItem>();
        public List<string> allBlockNames = new List<string>();

        public ProjectSettingsView()
        {
            InitializeComponent();
        }

        private void Btn_Cancel_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void Btn_SaveConfig_Click(object sender, RoutedEventArgs e)
        {
            using (W.DocLock)
            {
                W.FocusDoc();

                if (blockModels != null && blockModels.Count > 0)
                {
                    List<string> blockNames = new List<string>();

                    foreach (var item in blockModels)
                    {
                        if (string.IsNullOrEmpty(item.BlockName)) continue;
                        blockNames.Add(item.BlockName);
                    }

                    ProjectSettingsModel projectSettingsModel = new ProjectSettingsModel();
                    projectSettingsModel.BlockNames = blockNames;
                    projectSettingsModel.CurveLayers = Tb_LineLayerName.Text;
                    string toJson = JsonConvert.SerializeObject(projectSettingsModel, Formatting.Indented);
                    File.WriteAllText(GlobalSetting.SaveCurveAndBlockTypeFilePath, toJson, System.Text.Encoding.UTF8);
                    MessageBoxUtil.MessageInformation("保存成功!");
                }
            }
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            using (W.DocLock)
            {
                W.FocusDoc();

                // 获取所有块名称
                allBlockNames = ModelUtil.GetAllBlockNames().Where(x=>!string.IsNullOrEmpty(x)).OrderBy(x => x).ToList();

                // 设置ComboBox的数据源
                Cb_BlockName.ItemsSource = allBlockNames;

                if (File.Exists(GlobalSetting.SaveCurveAndBlockTypeFilePath))
                {
                    // 读取之前的配置信息
                    string json = File.ReadAllText(GlobalSetting.SaveCurveAndBlockTypeFilePath);
                    var infos = JsonConvert.DeserializeObject<ProjectSettingsModel>(json);

                    Tb_LineLayerName.Text = infos.CurveLayers;

                    var data = infos.BlockNames;
                    if (data != null && data.Count > 0)
                    {
                        blockModels.Clear();
                        foreach (var item in data)
                        {
                            if (string.IsNullOrEmpty(item)) continue;
                            blockModels.Add(new BlockItem { BlockName = item });
                        }
                    }
                }
                else
                {
                    // 默认添加"探测点"
                    if (allBlockNames.Contains("探测点"))
                    {
                        blockModels.Add(new BlockItem { BlockName = "探测点" });
                    }
                }

                // 设置DataGrid的数据源
                Dg_BlockInfo.ItemsSource = blockModels;
            }
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {

        }

        private void Btn_SelectLineLayer_Click(object sender, RoutedEventArgs e)
        {
            using (W.DocLock)
            {
                W.FocusDoc();
                SelectEntitiesWithTurnOffLayer<CadDb.Curve>(
                    layerName =>
                    {
                        var totalTexts = Tb_LineLayerName.Text.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                        if (!totalTexts.Any(x => x.Equals(layerName)))
                        {
                            if (string.IsNullOrEmpty(Tb_LineLayerName.Text) || Tb_LineLayerName.Text.EndsWith(","))
                            {
                                Tb_LineLayerName.Text += layerName + ",";
                            }
                            else
                            {
                                Tb_LineLayerName.Text += "," + layerName + ",";
                            }
                        }
                    });
            }
        }

        /// <summary>
        /// 关闭指定图层的实体
        /// </summary>
        /// <param name="message"></param>
        /// <returns></returns>
        public static bool SelectEntitiesWithTurnOffLayer<T>(Action<string> onEachLayerSelected, SelectionFilter selectionFilter = null, string message = "选择图形") where T : Entity
        {
            List<string> layerNameList = new List<string>();
            Editor ed = W.Ed;
            PromptEntityOptions peo = new PromptEntityOptions(message);

            while (true)
            {
                var per = ed.GetEntity(peo);
                if (per.Status == PromptStatus.OK)
                {
                    using (Transaction tran = W.Trans)
                    {
                        Entity entity = (Entity)per.ObjectId.GetObject(OpenMode.ForWrite);
                        if (entity is T e)
                        {
                            if (!layerNameList.Contains(e.Layer))
                            {
                                LayerUtil.TurnLayerIsOff(e.Layer);
                                layerNameList.Add(e.Layer);
                            }
                            onEachLayerSelected?.Invoke(e.Layer);
                        }
                        tran.Commit();
                    }
                }
                else
                {
                    break;
                }
            }

            foreach (var item in layerNameList)
            {
                LayerUtil.TurnLayerIsOn(item);
            }

            return layerNameList.Count > 0;
        }



    }

    // 添加这个辅助类
    public class BlockItem
    {
        public string BlockName { get; set; }
    }
}
