﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace SmartRouting.App.Converters
{
    public class EnumToBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
                return false;

            string enumValue = value.ToString();
            string targetValue = parameter.ToString();

            return enumValue.Equals(targetValue, StringComparison.InvariantCultureIgnoreCase);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && boolValue && parameter != null)
            {
                // Find the correct enum type. The targetType is 'object' when the binding is not direct.
                // We need to get the actual enum type. A simple way is to use the parameter,
                // but for a more robust solution, you might need to pass the type itself.
                // For this case, we assume the parameter is a string name of an enum value,
                // and the binding engine can figure out the target enum type.
                if (targetType.IsEnum)
                {
                    return Enum.Parse(targetType, parameter.ToString());
                }
            }
            return Binding.DoNothing;
        }
    }
}
