﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.数据录入.Model
{
    public class ProbePointModel : BindableBase
    {
        /// <summary>
        /// 序号
        /// </summary>
        public string ProbePointNum
        {
            get => GetProperty(() => ProbePointNum);
            set => SetProperty(() => ProbePointNum, value);
        }


        /// <summary>
        /// 运营商
        /// </summary>
        public TelecomOperatorEnum TelecomOperatorEnum
        {
            get => GetProperty(() => TelecomOperatorEnum);
            set => SetProperty(() => TelecomOperatorEnum, value);
        }

        /// <summary>
        /// 芯数
        /// </summary>
        public int CoreCount
        {
            get => GetProperty(() => CoreCount);
            set => SetProperty(() => CoreCount, value);
        }



        /// <summary>
        /// 所有的芯
        /// </summary>
        public ObservableCollection<int> TotalkCoreCount
        {
            get => GetProperty(() => TotalkCoreCount);
            set => SetProperty(() => TotalkCoreCount, value);
        }



        /// <summary>
        /// 备注
        /// </summary>
        public string Other
        {
            get => GetProperty(() => Other);
            set => SetProperty(() => Other, value);
        }



    }
}
