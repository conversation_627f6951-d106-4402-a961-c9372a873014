﻿<Window
    x:Class="SmartRouting.App.Commands.图层管理.View.DwgSettingView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SmartRouting.App.Commands.图层管理.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="设置"
    Width="auto"
    Height="AUTO"
    MinWidth="250"
    MinHeight="200"
    Loaded="Window_Loaded"
    ResizeMode="NoResize"
    ShowInTaskbar="False"
    SizeToContent="WidthAndHeight"
    SnapsToDevicePixels="True"
    Topmost="True"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/SmartRouting.App;component/Common/WPFUI/CommonUIStyle.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <StackPanel>
        <TabControl
            Grid.Row="1"
            Margin="2"
            TabStripPlacement="Left">
            <TabItem x:Name="TabLayer" Header="图层操作">
                <GroupBox Header="图元信息">
                    <StackPanel Margin="2" Orientation="Vertical">
                        <DataGrid
                            x:Name="Dg_LayerInfo"
                            Width="AUTO"
                            MinHeight="200"
                            MaxHeight="400"
                            HorizontalContentAlignment="Stretch"
                            AutoGenerateColumns="False"
                            CanUserAddRows="True"
                            GridLinesVisibility="All"
                            HeadersVisibility="All">
                            <DataGrid.Columns>
                                <DataGridComboBoxColumn
                                    x:Name="Cb_LayerName"
                                    Width="200"
                                    Header="图层名称"
                                    IsReadOnly="False"
                                    SelectedItemBinding="{Binding LayerName}" />
                                <DataGridTemplateColumn Width="AUTO" Header="操作">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <CheckBox
                                                Width="auto"
                                                Margin="0.2"
                                                Padding="0.5"
                                                HorizontalAlignment="Stretch"
                                                HorizontalContentAlignment="Stretch"
                                                Click="CheckBox_Click"
                                                IsChecked="{Binding IsFunc, UpdateSourceTrigger=PropertyChanged}"
                                                Tag="{Binding}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                        <WrapPanel HorizontalAlignment="Right">
                            <Button
                                x:Name="Btn_DelectLayer"
                                Click="Btn_DelectLayer_Click"
                                Content="删除图层" />
                            <Button
                                x:Name="Btn_CloseLayer"
                                Click="Btn_CloseLayer_Click"
                                Content="关闭图层" />
                            <Button
                                x:Name="Btn_SetColorIndexTo8"
                                Click="Btn_SetColorIndexTo8_Click"
                                Content="修改色号为8" />

                        </WrapPanel>
                    </StackPanel>

                </GroupBox>
            </TabItem>
            <TabItem x:Name="TabLineType" Header="线型字体">
                <StackPanel Orientation="Vertical">
                    <DockPanel LastChildFill="True">
                        <TextBlock DockPanel.Dock="Left" Text="多段线线宽：" />
                        <TextBox
                            x:Name="Tb_LineThinkness"
                            Width="50"
                            Text="0" />
                        <Button
                            x:Name="Btn_LineThinkness"
                            HorizontalAlignment="Right"
                            Click="Btn_LineThinkness_Click"
                            Content="修改" />
                    </DockPanel>

                    <DockPanel LastChildFill="True">
                        <Button
                            x:Name="Btn_TotalEntitiesLineAndLayerChange"
                            Width="auto"
                            HorizontalAlignment="Stretch"
                            Click="Btn_TotalEntitiesLineAndLayerChange_Click"
                            Content="全图元图层及线宽ByLayer" />
                    </DockPanel>

                    <DockPanel LastChildFill="True">
                        <Button
                            x:Name="Btn_FontEdit"
                            Width="auto"
                            HorizontalAlignment="Stretch"
                            Click="Btn_FontEdit_Click"
                            Content="字体修改" />
                    </DockPanel>



                </StackPanel>



            </TabItem>
            <TabItem x:Name="TabBlock" Header="图块操作">
                <StackPanel Orientation="Vertical">
                    <DockPanel LastChildFill="True">
                        <DataGrid
                            x:Name="Dg_BlockInfo"
                            Width="AUTO"
                            MinHeight="200"
                            MaxHeight="400"
                            HorizontalContentAlignment="Stretch"
                            AutoGenerateColumns="False"
                            CanUserAddRows="True"
                            GridLinesVisibility="All"
                            HeadersVisibility="All">
                            <DataGrid.Columns>
                                <DataGridComboBoxColumn
                                    x:Name="Cb_BlockName"
                                    Width="200"
                                    Header="块名称"
                                    IsReadOnly="False"
                                    SelectedItemBinding="{Binding BlockName}" />
                                <DataGridTemplateColumn Width="AUTO" Header="操作">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <CheckBox
                                                x:Name="Cb_BlockName"
                                                Width="auto"
                                                Margin="0.2"
                                                Padding="0.5"
                                                HorizontalAlignment="Stretch"
                                                HorizontalContentAlignment="Stretch"
                                                Click="CheckBox_Click"
                                                IsChecked="{Binding IsFunc, UpdateSourceTrigger=PropertyChanged}"
                                                Tag="{Binding}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </DockPanel>

                    <DockPanel>
                        <Button
                            x:Name="Btn_DelectBlock"
                            HorizontalAlignment="Right"
                            Click="Btn_DelectBlock_Click"
                            Content="图块删除" />
                    </DockPanel>



                </StackPanel>



            </TabItem>


            <TabItem x:Name="TabLayerAndLine" Header="数据特性">
                <StackPanel Orientation="Vertical">
                    <DockPanel LastChildFill="True">
                        <DataGrid
                            x:Name="Dg_LayerAndLine"
                            Width="AUTO"
                            MinHeight="200"
                            MaxHeight="400"
                            HorizontalContentAlignment="Stretch"
                            AutoGenerateColumns="False"
                            CanUserAddRows="True"
                            GridLinesVisibility="All"
                            HeadersVisibility="All">
                            <DataGrid.Columns>
                                <DataGridComboBoxColumn
                                    x:Name="Cb_LayerAndLine"
                                    Width="120"
                                    Header="图层名称"
                                    IsReadOnly="False"
                                    SelectedItemBinding="{Binding LayerName}" />
                                <DataGridTextColumn
                                    Width="50"
                                    Binding="{Binding ColorIndex}"
                                    Header="颜色"
                                    IsReadOnly="False" />

                                <DataGridComboBoxColumn
                                    x:Name="Cb_LineType"
                                    Width="80"
                                    Header="线型"
                                    IsReadOnly="False"
                                    SelectedItemBinding="{Binding LineStyle}" />
                                <DataGridTextColumn
                                    Width="60"
                                    Binding="{Binding LineScale}"
                                    Header="线型比例"
                                    IsReadOnly="False" />
                                <DataGridTemplateColumn Width="AUTO" Header="操作">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <CheckBox
                                                Width="auto"
                                                Margin="0.2"
                                                Padding="0.5"
                                                HorizontalAlignment="Stretch"
                                                HorizontalContentAlignment="Stretch"
                                                Click="CheckBox_Click"
                                                IsChecked="{Binding IsFunc, UpdateSourceTrigger=PropertyChanged}"
                                                Tag="{Binding}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </DockPanel>

                    <DockPanel>
                        <Button
                            x:Name="Btn_LayerAndLine"
                            HorizontalAlignment="Right"
                            Click="Btn_LayerAndLine_Click"
                            Content="特性修改" />
                    </DockPanel>
                </StackPanel>
            </TabItem>
        </TabControl>
        <Grid Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Button
                x:Name="Btn_SaveConfig"
                Grid.Column="0"
                HorizontalAlignment="Right"
                Click="Btn_SaveConfig_Click"
                Content="保存配置" />
            <Button
                x:Name="Btn_ModifyByOneButton"
                Grid.Column="1"
                HorizontalAlignment="Right"
                Click="Btn_ModifyByOneButton_Click"
                Content="一键修改" />
        </Grid>

    </StackPanel>


</Window>

