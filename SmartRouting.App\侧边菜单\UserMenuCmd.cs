﻿

using SmartRouting.App.侧边菜单.View;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms.Integration;



#if AC13 || AC12
using windows = Autodesk.AutoCAD.Windows;
#elif ZW25
using windows= ZwSoft.ZwCAD.Windows;
#endif
namespace GMDI_ForCAD.App.侧边菜单
{
    public class UserMenuCmd : IExtensionApplication
    {

        private static windows.PaletteSet? paletteSet;
        [CommandMethod(nameof(UserMenu))]
        public static void UserMenu()
        {

            #region 新的
            if (paletteSet is null)
            {
                paletteSet = new windows.PaletteSet("邑排模");
                PaletteMenu win = new PaletteMenu();
                var host = new ElementHost() { AutoSize = true, Dock = System.Windows.Forms.DockStyle.Fill, Child = win, };
                paletteSet.Add("读取柱状图信息", host);
                paletteSet.Visible = true;
                paletteSet.Size = new System.Drawing.Size(140, 400);
                paletteSet.MinimumSize = new System.Drawing.Size(140, 400);
                paletteSet.Dock = windows.DockSides.Left;
                paletteSet.RecalculateDockSiteLayout();
                paletteSet.KeepFocus = true;
                paletteSet.Style = windows.PaletteSetStyles.ShowCloseButton | windows.PaletteSetStyles.ShowPropertiesMenu | windows.PaletteSetStyles.ShowAutoHideButton;
                // paletteSet.DockEnabled = ZwSoft.ZwCAD.Windows.DockSides.Left;
                return;
            }
            paletteSet.Visible = !paletteSet.Visible;

            #endregion

        }

        // 定义一个静态变量来持有我们创建的窗格(Pane)
        // 这样我们就可以在其他地方(比如命令中)访问和修改它
      //  public static Pane customPane;
        // DoubleClickEvent doubleClickEvent = new DoubleClickEvent();
        public void Initialize()
        {
        
            UserMenu();
         
            // 通过静态属性 Instance 获取单例并调用其方法
            DoubleClickEvent.AddDoubleClickEvent();
        }

        public void Terminate()
        {
            // 修复第二步：在插件终止时，主动清理UI资源
            if (paletteSet != null && !paletteSet.IsDisposed)
            {
                paletteSet.Dispose();
                paletteSet = null; // 显式地设为 null
            }
            // 通过静态属性 Instance 获取单例并调用其方法
            DoubleClickEvent.RemoveDoubleClickEvent();
        }
    }



    public class PsetArgs
    {
        public PsetArgs()
        {

        }
        public Dictionary<string, string> Dic { get; set; } = new Dictionary<string, string>();
    }
}





