﻿using SmartRouting.App.Properties;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SmartRouting.App.Commands.常用.AI助手.View
{
    /// <summary>
    /// ApiKeySettingsView.xaml 的交互逻辑
    /// </summary>
    public partial class ApiKeySettingsView : Window
    {
        public ApiKeySettingsView()
        {
            InitializeComponent();
            this.Loaded += ApiKeySettingsView_Loaded;
        }

        private void ApiKeySettingsView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                ApiKeyTextBox.Text = Settings.Default.ApiKey;
            }
            catch (System.Configuration.SettingsPropertyNotFoundException)
            {
                // 这是预期的，如果用户还未在项目属性中添加该设置项
                // 留空文本框即可
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Settings.Default.ApiKey = ApiKeyTextBox.Text.Trim();
                Settings.Default.Save();
                this.DialogResult = true; // 设置DialogResult以表示成功
                this.Close();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"未能保存API Key。错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
