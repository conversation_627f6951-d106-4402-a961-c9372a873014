﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Common.GeneralDuplicate
{
    internal class EqualityPoint2d : IEqualityComparer<Point2d>
    {
        public bool Equals(Point2d x, Point2d y)
        {
            return x.GetDistanceTo(y) < 0.01;
        }

        public int GetHashCode(Point2d obj)
        {
            return 1;
        }
    }
}
