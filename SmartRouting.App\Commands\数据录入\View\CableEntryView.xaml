﻿<Window
    x:Class="SmartRouting.App.Commands.数据录入.View.CableEntryView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:SmartRouting.App.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SmartRouting.App.Commands.数据录入.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:SmartRouting.App.Commands.数据录入.Model"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    Title="光缆信息录入********-18-25"
    Width="Auto"
    Height="auto"
    Loaded="Window_Loaded"
    ResizeMode="NoResize"
    SizeToContent="WidthAndHeight"
    Topmost="True"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <Window.Resources>
        <converters:EnumToBooleanConverter x:Key="EnumToBooleanConverter" />
        <converters:IsJointOperationConverter x:Key="IsJointOperationConverter" />
        <ObjectDataProvider
            x:Key="CableEntryEnumValues"
            MethodName="GetValues"
            ObjectType="{x:Type sys:Enum}">
            <ObjectDataProvider.MethodParameters>
                <x:Type TypeName="model:CableEntryEnum" />
            </ObjectDataProvider.MethodParameters>
        </ObjectDataProvider>

        <ObjectDataProvider
            x:Key="OperationEnumValues"
            MethodName="GetValues"
            ObjectType="{x:Type sys:Enum}">
            <ObjectDataProvider.MethodParameters>
                <x:Type TypeName="model:OperationEnum" />
            </ObjectDataProvider.MethodParameters>
        </ObjectDataProvider>





        <ObjectDataProvider
            x:Key="TelecomOperatorEnumValues"
            MethodName="GetValues"
            ObjectType="{x:Type sys:Enum}">
            <ObjectDataProvider.MethodParameters>
                <x:Type TypeName="model:TelecomOperatorEnum" />
            </ObjectDataProvider.MethodParameters>
        </ObjectDataProvider>
        <Style TargetType="DataGrid">
            <Setter Property="Background" Value="White" />
            <Setter Property="BorderBrush" Value="#D6D6D6" />
            <Setter Property="HorizontalGridLinesBrush" Value="#D6D6D6" />
            <Setter Property="VerticalGridLinesBrush" Value="#D6D6D6" />
            <Setter Property="RowBackground" Value="White" />
            <Setter Property="AlternatingRowBackground" Value="#F2F2F2" />
            <Setter Property="CanUserAddRows" Value="False" />
            <Setter Property="AutoGenerateColumns" Value="False" />
            <Setter Property="HeadersVisibility" Value="Column" />
            <Setter Property="SelectionMode" Value="Single" />
            <Setter Property="GridLinesVisibility" Value="All" />
        </Style>
        <Style TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#F5F5F5" />
            <Setter Property="Foreground" Value="#333" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Padding" Value="10,5" />
            <Setter Property="HorizontalContentAlignment" Value="Center" />
        </Style>
        <Style TargetType="DataGridCell">
            <Setter Property="Padding" Value="10,5" />
            <Setter Property="HorizontalContentAlignment" Value="Center" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DataGridCell">
                        <Border
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
                            <ContentPresenter
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style
            x:Key="ComboBoxDataGridCellStyle"
            BasedOn="{StaticResource {x:Type DataGridCell}}"
            TargetType="DataGridCell">
            <Setter Property="Padding" Value="0" />
            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        </Style>
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4A90E2" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="12,6" />
            <Setter Property="MinWidth" Value="70" />
            <Setter Property="Margin" Value="5,0" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border
                            x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="3"
                            SnapsToDevicePixels="True">
                            <ContentPresenter
                                x:Name="contentPresenter"
                                Margin="{TemplateBinding Padding}"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Focusable="False"
                                RecognizesAccessKey="True"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#357ABD" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#2A5C9A" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.7" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    <StackPanel>
        <DockPanel>
            <GroupBox Header="工井信息">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="AUTO" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <DockPanel Grid.Row="0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="AUTO" />
                                <ColumnDefinition Width="80" />
                                <ColumnDefinition Width="AUTO" />
                                <ColumnDefinition Width="80" />
                            </Grid.ColumnDefinitions>
                            <TextBlock
                                Grid.Column="0"
                                VerticalAlignment="Center"
                                FontSize="14"
                                Foreground="#FF333333"
                                Text="井号：" />
                            <TextBlock
                                x:Name="Tb_WellNum"
                                Grid.Column="1"
                                VerticalAlignment="Center"
                                FontSize="20"
                                Text="{Binding WellName}" />
                            <TextBlock
                                Grid.Column="2"
                                VerticalAlignment="Center"
                                FontSize="14"
                                Foreground="#FF333333"
                                Text="权属：" />
                            <TextBlock
                                x:Name="Tb_TelecomOperator"
                                Grid.Column="3"
                                VerticalAlignment="Center"
                                FontSize="20"
                                Text="{Binding WellTelecomOperator}" />
                        </Grid>

                        <WrapPanel HorizontalAlignment="Right" VerticalAlignment="Center">
                            <TextBlock
                                x:Name="Tb_Pic"
                                VerticalAlignment="Center"
                                Foreground="Green"
                                Text="{Binding WellImage}" />
                            <Button
                                x:Name="Btn_UpLoadPic"
                                Click="Btn_UpLoadPic_Click"
                                Content="上传照片"
                                Style="{StaticResource PrimaryButtonStyle}" />
                        </WrapPanel>
                    </DockPanel>
                </Grid>
            </GroupBox>
        </DockPanel>
        <DockPanel>
            <GroupBox Header="线缆信息">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="auto" />
                    </Grid.RowDefinitions>
                    <DataGrid
                        x:Name="CablesGrid"
                        Grid.Row="0"
                        Height="200"
                        CanUserDeleteRows="False"
                        HorizontalScrollBarVisibility="Auto"
                        ItemsSource="{Binding CableModels}"
                        VerticalScrollBarVisibility="Auto">
                        <DataGrid.Columns>
                            <DataGridTextColumn
                                Width="Auto"
                                Binding="{Binding CableEntryNumber}"
                                Header="编号" />
                            <DataGridTemplateColumn
                                Width="Auto"
                                CellStyle="{StaticResource ComboBoxDataGridCellStyle}"
                                Header="类型">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <ComboBox ItemsSource="{Binding Source={StaticResource CableEntryEnumValues}}" SelectedItem="{Binding CableEntryEnum, UpdateSourceTrigger=PropertyChanged}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn
                                Width="60"
                                CellStyle="{StaticResource ComboBoxDataGridCellStyle}"
                                Header="规格">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <ComboBox
                                            IsEditable="True"
                                            ItemsSource="{Binding SpecificationOptions}"
                                            Text="{Binding Specification, UpdateSourceTrigger=PropertyChanged}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn
                                Width="Auto"
                                CellStyle="{StaticResource ComboBoxDataGridCellStyle}"
                                Header="权属">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <ComboBox ItemsSource="{Binding Source={StaticResource TelecomOperatorEnumValues}}" SelectedItem="{Binding TelecomOperatorEnum, UpdateSourceTrigger=PropertyChanged}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn
                                Width="110"
                                CellStyle="{StaticResource ComboBoxDataGridCellStyle}"
                                Header="操作">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <ComboBox ItemsSource="{Binding Source={StaticResource OperationEnumValues}}" SelectedItem="{Binding OperationEnum, UpdateSourceTrigger=PropertyChanged}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding Remarks}"
                                Header="备注" />

                        </DataGrid.Columns>
                    </DataGrid>

                    <Grid Grid.Row="1" Margin="0,10,0,0">
                        <Button
                            x:Name="Btn_AddRow"
                            MinWidth="40"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Click="Btn_AddRow_Click"
                            Content="+"
                            FontWeight="Bold"
                            Style="{StaticResource PrimaryButtonStyle}" />

                        <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                            <Button
                                x:Name="Btn_ImportData"
                                VerticalAlignment="Center"
                                Click="Btn_ImportData_Click"
                                Content="线缆录入"
                                Style="{StaticResource PrimaryButtonStyle}" />
                            <Button
                                x:Name="Btn_Cancel"
                                VerticalAlignment="Center"
                                Click="Btn_Cancel_Click"
                                Content="取消"
                                Style="{StaticResource PrimaryButtonStyle}" />
                        </StackPanel>
                    </Grid>
                </Grid>

            </GroupBox>

        </DockPanel>

    </StackPanel>

</Window>


