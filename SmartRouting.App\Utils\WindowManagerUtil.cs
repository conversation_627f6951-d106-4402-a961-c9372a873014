﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace SmartRouting.App.Utils
{
    /// <summary>
    /// 提供管理WPF窗口单例模式的通用方法。
    /// </summary>
    public static class WindowManagerUtil
    {
        // 使用字典来存储已打开的窗口实例，键为窗口类型，值为窗口实例。
        private static readonly Dictionary<Type, Window> _openWindows = new Dictionary<Type, Window>();

        /// <summary>
        /// 以单例模式显示一个无模式窗口（适用于无参构造函数）。
        /// </summary>
        /// <typeparam name="T">要显示的窗口类型，必须是Window的子类且具有无参构造函数。</typeparam>
        public static void Show<T>() where T : Window, new()
        {
            Show(() => new T());
        }

        /// <summary>
        /// 以单例模式显示一个无模式窗口（适用于需要参数的构造函数）。
        /// </summary>
        /// <typeparam name="T">要显示的窗口类型，必须是Window的子类。</typeparam>
        /// <param name="windowFactory">一个用于创建窗口实例的工厂函数。</param>
        public static void Show<T>(Func<T> windowFactory) where T : Window
        {
            Type windowType = typeof(T);

            // 检查窗口是否已经打开
            if (_openWindows.TryGetValue(windowType, out Window existingWindow) && existingWindow != null)
            {
                // 如果窗口已存在但可能被隐藏或最小化，则激活它
                existingWindow.Activate();
                return;
            }

            // 如果窗口不存在，则使用工厂函数创建新实例
            T newWindow = windowFactory();

            // 将新窗口实例添加到字典中
            _openWindows[windowType] = newWindow;

            // 注册Closed事件，当窗口关闭时，从字典中移除它
            newWindow.Closed += (sender, args) =>
            {
                _openWindows.Remove(windowType);
            };

            // 使用CAD的应用服务来显示无模式窗口
            CadApp.ShowModelessWindow(newWindow);
        }
    }
}
