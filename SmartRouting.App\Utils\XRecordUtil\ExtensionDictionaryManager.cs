﻿using GMDI_ForCAD;
using GMDI_ForCAD.App;
using SmartRouting.App.Commands.数据录入.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using static OfficeOpenXml.ExcelErrorValue;

namespace SmartRouting.App.Utils.XRecordUtil
{









    public static class ExtensionDictionaryManager
    {

        /// <summary>
        /// 在整个图纸中查找其扩展数据指向特定目标工井名称 ("ToWell.WellName") 的第一条管道 (Polyline)。
        /// </summary>
        /// <param name="targetWellName">要查找的目标工井的名称，例如 "AAA"</param>
        /// <returns>找到的第一个 Polyline 的 ObjectId，如果未找到则返回 ObjectId.Null</returns>
        public static List<ObjectId> FindAllPipelinesIdToWellName(string targetWellName)
        {
            var ids=new List<ObjectId>();
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return new List<ObjectId>();

            var db = doc.Database;
            string recordName = typeof(PipelineModel).Name;

            // 开启一个事务来安全地读取数据库
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var btr = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    // 遍历模型空间中的所有实体
                    foreach (ObjectId objId in btr)
                    {
                        // 首先检查实体是不是一个 Polyline (多段线)
                        if (objId.ObjectClass.IsDerivedFrom(RXObject.GetClass(typeof(Polyline))))
                        {
                            // 1. 读取扩展字典数据
                            var typedValueList = objId.GetObjXrecord(tr, recordName);

                            // 如果没有这个扩展记录，就跳过
                            if (typedValueList == null) continue;

                            // 2. 将数据反序列化为 PipelineModel 对象
                            var pipelineModel = GenericExtensionDictionaryManager.TypeValueListToObject<PipelineModel>(typedValueList);

                            // 3. 【核心检查】检查 ToWell 属性及其 WellName 是否匹配
                            //    必须进行 null 检查以防止崩溃
                            if (pipelineModel?.ToWell?.WellName == targetWellName)
                            {
                                ids.Add(objId);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 在开发过程中，最好能看到详细的错误信息
                    doc.Editor.WriteMessage($"\n查找管道时出错: {ex.Message}");
                    tr.Abort(); // 发生错误时中止事务
                }

                // 如果循环结束了还没找到，也提交事务并返回Null
                tr.Commit();
                return ids;
            }
        }








        /// 在整个图纸中查找其扩展数据指向特定目标工井名称 ("ToWell.WellName") 的第一条管道 (Polyline)。
        /// </summary>
        /// <param name="targetWellName">要查找的目标工井的名称，例如 "AAA"</param>
        /// <returns>找到的第一个 Polyline 的 ObjectId，如果未找到则返回 ObjectId.Null</returns>
        public static ObjectId FindFirstPipelineIdToWellName(string targetWellName)
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return ObjectId.Null;

            var db = doc.Database;
            string recordName = typeof(PipelineModel).Name;

            // 开启一个事务来安全地读取数据库
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var btr = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    // 遍历模型空间中的所有实体
                    foreach (ObjectId objId in btr)
                    {
                        // 首先检查实体是不是一个 Polyline (多段线)
                        if (objId.ObjectClass.IsDerivedFrom(RXObject.GetClass(typeof(Polyline))))
                        {
                            // 1. 读取扩展字典数据
                            var typedValueList = objId.GetObjXrecord(tr, recordName);

                            // 如果没有这个扩展记录，就跳过
                            if (typedValueList == null) continue;

                            // 2. 将数据反序列化为 PipelineModel 对象
                            var pipelineModel = GenericExtensionDictionaryManager.TypeValueListToObject<PipelineModel>(typedValueList);

                            // 3. 【核心检查】检查 ToWell 属性及其 WellName 是否匹配
                            //    必须进行 null 检查以防止崩溃
                            if (pipelineModel?.ToWell?.WellName == targetWellName)
                            {
                                tr.Commit(); // 提交事务
                                return objId; // 找到了，立即返回结果
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 在开发过程中，最好能看到详细的错误信息
                    doc.Editor.WriteMessage($"\n查找管道时出错: {ex.Message}");
                    tr.Abort(); // 发生错误时中止事务
                }

                // 如果循环结束了还没找到，也提交事务并返回Null
                tr.Commit();
                return ObjectId.Null;
            }
        }
































        /// <summary>
        /// 在整个图纸中查找其扩展数据指向特定目标工井名称 ("ToWell.WellName") 的第一条管道 (Polyline)。
        /// </summary>
        /// <param name="targetWellName">要查找的目标工井的名称，例如 "AAA"</param>
        /// <returns>找到的第一个 Polyline 的 ObjectId，如果未找到则返回 ObjectId.Null</returns>
        public static PipelineModel FindFirstPipelineToWellName(string targetWellName)
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return null;

            var db = doc.Database;
            string recordName = typeof(PipelineModel).Name;

            // 开启一个事务来安全地读取数据库
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var btr = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    // 遍历模型空间中的所有实体
                    foreach (ObjectId objId in btr)
                    {
                        // 首先检查实体是不是一个 Polyline (多段线)
                        if (objId.ObjectClass.IsDerivedFrom(RXObject.GetClass(typeof(Polyline))))
                        {
                            // 1. 读取扩展字典数据
                            var typedValueList = objId.GetObjXrecord(tr, recordName);

                            // 如果没有这个扩展记录，就跳过
                            if (typedValueList == null) continue;

                            // 2. 将数据反序列化为 PipelineModel 对象
                            var pipelineModel = GenericExtensionDictionaryManager.TypeValueListToObject<PipelineModel>(typedValueList);

                            // 3. 【核心检查】检查 ToWell 属性及其 WellName 是否匹配
                            //    必须进行 null 检查以防止崩溃
                            if (pipelineModel?.ToWell?.WellName == targetWellName)
                            {
                                tr.Commit(); // 提交事务
                                return pipelineModel; // 找到了，立即返回结果
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 在开发过程中，最好能看到详细的错误信息
                    doc.Editor.WriteMessage($"\n查找管道时出错: {ex.Message}");
                    tr.Abort(); // 发生错误时中止事务
                }

                // 如果循环结束了还没找到，也提交事务并返回Null
                tr.Commit();
                return null;
            }
        }















        /// <summary>
        /// 导出单个选定实体的 WellModel 和 PipelineModel 扩展字典数据到 TXT 文件。
        /// </summary>
        /// <param name="entityId">要导出数据的实体的 ObjectId。</param>
        /// <param name="outputFilePath">要保存的 .txt 文件的完整路径。</param>
        public static void ExportSingleEntityDataAsJson(ObjectId entityId, string outputFilePath)
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null || entityId.IsNull)
            {
                Application.ShowAlertDialog("没有活动的文档或未选择有效的实体。");
                return;
            }

            var db = doc.Database;
            var contentBuilder = new StringBuilder();
            string wellRecordName = typeof(WellModel).Name;
            string pipelineRecordName = typeof(PipelineModel).Name;
            bool dataFound = false;

            // 开启一个只读事务来安全地读取数据
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var entity = tr.GetObject(entityId, OpenMode.ForRead) as Entity;
                    if (entity == null)
                    {
                        Application.ShowAlertDialog("无法打开所选对象。");
                        tr.Commit();
                        return;
                    }

                    contentBuilder.AppendLine($"--- 单个实体数据导出报告 ---");
                    contentBuilder.AppendLine($"时间: {DateTime.Now}");
                    contentBuilder.AppendLine($"实体句柄: {entity.Handle}");
                    contentBuilder.AppendLine($"实体类型: {entity.GetType().Name}");
                    contentBuilder.AppendLine("==================================================");

                    if (entity.ExtensionDictionary.IsNull)
                    {
                        contentBuilder.AppendLine("\n此实体没有扩展字典。");
                    }
                    else
                    {
                        // 检查实体是否包含 WellModel 数据
                        var wellDataList = entityId.GetObjXrecord(tr, wellRecordName);
                        if (wellDataList != null)
                        {
                            dataFound = true;
                            var wellModel = GenericExtensionDictionaryManager.TypeValueListToObject<WellModel>(wellDataList);
                            contentBuilder.AppendLine("\n--- 找到 WellModel 数据 ---");
                            contentBuilder.AppendLine(JsonConvert.SerializeObject(wellModel, Formatting.Indented));
                            contentBuilder.AppendLine("--------------------------------------------------");
                        }

                        // 检查实体是否包含 PipelineModel 数据
                        var pipelineDataList = entityId.GetObjXrecord(tr, pipelineRecordName);
                        if (pipelineDataList != null)
                        {
                            dataFound = true;
                            var pipelineModel = GenericExtensionDictionaryManager.TypeValueListToObject<PipelineModel>(pipelineDataList);
                            contentBuilder.AppendLine("\n--- 找到 PipelineModel 数据 ---");
                            contentBuilder.AppendLine(JsonConvert.SerializeObject(pipelineModel, Formatting.Indented));
                            contentBuilder.AppendLine("--------------------------------------------------");
                        }

                        if (!dataFound)
                        {
                            contentBuilder.AppendLine("\n在扩展字典中未找到 'WellModel' 或 'PipelineModel' 记录。");
                        }
                    }

                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    Application.ShowAlertDialog($"读取数据时发生错误: {ex.Message}");
                    tr.Abort();
                    return;
                }
            }

            // 将收集到的所有信息写入文件
            try
            {
                File.WriteAllText(outputFilePath, contentBuilder.ToString());
                Application.ShowAlertDialog($"数据已成功导出到:\n{outputFilePath}");
            }
            catch (System.Exception ex)
            {
                Application.ShowAlertDialog($"写入文件时发生错误: {ex.Message}");
            }
        }
        /// <summary>
        /// 导出一个包含图纸中所有 WellModel 和 PipelineModel 扩展字典数据的 TXT 文件。
        /// 数据将以易于阅读的 JSON 格式呈现。
        /// </summary>
        /// <param name="outputFilePath">要保存的 .txt 文件的完整路径。</param>
        public static void ExportAllExtensionDataAsJson(string outputFilePath)
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null)
            {
                Application.ShowAlertDialog("没有活动的文档。");
                return;
            }

            var db = doc.Database;
            var contentBuilder = new StringBuilder();
            string wellRecordName = typeof(WellModel).Name;
            string pipelineRecordName = typeof(PipelineModel).Name;

            // 开启一个只读事务来安全地读取所有数据
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var btr = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    contentBuilder.AppendLine($"--- 数据导出报告 ---");
                    contentBuilder.AppendLine($"时间: {DateTime.Now}");
                    contentBuilder.AppendLine($"图纸: {db.Filename}");
                    contentBuilder.AppendLine("==================================================");
                    contentBuilder.AppendLine();

                    // 遍历模型空间中的所有实体
                    foreach (ObjectId objId in btr)
                    {
                        var entity = tr.GetObject(objId, OpenMode.ForRead) as Entity;
                        if (entity == null || entity.ExtensionDictionary.IsNull)
                        {
                            continue; // 跳过没有扩展字典的实体
                        }

                        // 检查实体是否包含 WellModel 数据
                        var wellDataList = objId.GetObjXrecord(tr, wellRecordName);
                        if (wellDataList != null)
                        {
                            var wellModel = GenericExtensionDictionaryManager.TypeValueListToObject<WellModel>(wellDataList);
                            if (wellModel != null)
                            {
                                contentBuilder.AppendLine($"实体句柄: {entity.Handle}");
                                contentBuilder.AppendLine($"实体类型: {entity.GetType().Name}");
                                contentBuilder.AppendLine($"扩展数据类型: WellModel");
                                // 使用 Newtonsoft.Json 格式化输出
                                contentBuilder.AppendLine(JsonConvert.SerializeObject(wellModel, Formatting.Indented));
                                contentBuilder.AppendLine("--------------------------------------------------");
                            }
                        }

                        // 检查实体是否包含 PipelineModel 数据
                        var pipelineDataList = objId.GetObjXrecord(tr, pipelineRecordName);
                        if (pipelineDataList != null)
                        {
                            var pipelineModel = GenericExtensionDictionaryManager.TypeValueListToObject<PipelineModel>(pipelineDataList);
                            if (pipelineModel != null)
                            {
                                contentBuilder.AppendLine($"实体句柄: {entity.Handle}");
                                contentBuilder.AppendLine($"实体类型: {entity.GetType().Name}");
                                contentBuilder.AppendLine($"扩展数据类型: PipelineModel");
                                // 使用 Newtonsoft.Json 格式化输出
                                contentBuilder.AppendLine(JsonConvert.SerializeObject(pipelineModel, Formatting.Indented));
                                contentBuilder.AppendLine("--------------------------------------------------");
                            }
                        }
                    }

                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    Application.ShowAlertDialog($"导出数据时发生错误: {ex.Message}");
                    tr.Abort();
                    return;
                }
            }

            // 将收集到的所有信息写入文件
            try
            {
                File.WriteAllText(outputFilePath, contentBuilder.ToString());
                Application.ShowAlertDialog($"数据已成功导出到:\n{outputFilePath}");
            }
            catch (System.Exception ex)
            {
                Application.ShowAlertDialog($"写入文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 在整个图纸中查找扩展字典中WellName属性为特定值的第一个工井（BlockReference）。
        /// </summary>
        /// <param name="wellNameToFind">要查找的工井名称，例如 "AAA"</param>
        /// <returns>找到的第一个 BlockReference 的 ObjectId，如果未找到则返回 ObjectId.Null</returns>
        public static ObjectId FindFirstWellByName(string wellNameToFind)
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return ObjectId.Null;

            var db = doc.Database;
            string recordName = typeof(WellModel).Name;

            // 开启一个事务来安全地读取数据库
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var btr = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    // 遍历模型空间中的所有实体
                    foreach (ObjectId objId in btr)
                    {
                        // 首先检查实体是不是一个块参照
                        if (objId.ObjectClass.IsDerivedFrom(RXObject.GetClass(typeof(BlockReference))))
                        {
                            // 1. 读取扩展字典数据
                            var typedValueList = objId.GetObjXrecord(tr, recordName);

                            // 如果没有这个扩展记录，就跳过
                            if (typedValueList == null) continue;

                            // 2. 将数据反序列化为 WellModel 对象
                            var wellModel = GenericExtensionDictionaryManager.TypeValueListToObject<WellModel>(typedValueList);

                            // 3. 检查 WellName 属性是否匹配
                            if (wellModel != null && wellModel.WellName == wellNameToFind)
                            {
                                tr.Commit(); // 提交事务
                                return objId; // 找到了，立即返回结果
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 最好能记录下错误信息
                  //  System.Diagnostics.Debug.WriteLine($"查找工井时出错: {ex.Message}");
                    tr.Abort(); // 发生错误时中止事务
                }

                // 如果循环结束了还没找到，也提交事务并返回Null
                tr.Commit();
                return ObjectId.Null;
            }
        }

        /// <summary>
        /// 在整个图纸中查找扩展字典中WellName属性为特定值的所有工井（BlockReference）。
        /// </summary>
        /// <param name="wellNameToFind">要查找的工井名称，例如 "AAA"</param>
        /// <returns>一个包含所有匹配的 BlockReference 的 ObjectId 的列表</returns>
        public static List<ObjectId> FindAllWellsByName(string wellNameToFind)
        {
            var foundWells = new List<ObjectId>();
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return foundWells;

            var db = doc.Database;
            string recordName = typeof(WellModel).Name;

            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var btr = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    foreach (ObjectId objId in btr)
                    {
                        if (objId.ObjectClass.IsDerivedFrom(RXObject.GetClass(typeof(BlockReference))))
                        {
                            var typedValueList = objId.GetObjXrecord(tr, recordName);
                            if (typedValueList == null) continue;

                            var wellModel = GenericExtensionDictionaryManager.TypeValueListToObject<WellModel>(typedValueList);

                            if (wellModel != null && wellModel.WellName == wellNameToFind)
                            {
                                foundWells.Add(objId); // 找到了，添加到列表
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"查找工井时出错: {ex.Message}");
                    tr.Abort();
                }

                tr.Commit();
                return foundWells;
            }
        }


        #region 对象的扩展记录的添加、删除、修改
        /// <summary>
        ///  添加扩展记录，如果没有扩展字典，那就创建扩展字典
        /// </summary>
        /// <param name="objId">对象的objectid</param>
        /// <param name="xRecordSearchKey">扩展记录名称</param>
        /// <param name="values">扩展记录的内容</param>
        /// <returns></returns>
        public static bool AddXRecordToObj(this ObjectId objId, string xRecordSearchKey, TypedValueList values)
        {
            if (values == null)
            {
                throw new ArgumentNullException(nameof(values));
            }

            using (Transaction tr = objId.Database.TransactionManager.StartTransaction())
            {
                DBObject obj = tr.GetObject(objId, OpenMode.ForRead);
                if (obj.ExtensionDictionary.IsNull)
                {
                    obj.UpgradeOpen();
                    obj.CreateExtensionDictionary();
                }

                DBDictionary dict = tr.GetObject(obj.ExtensionDictionary, OpenMode.ForWrite) as DBDictionary;
                if (dict.Contains(xRecordSearchKey))
                {
                    return false;
                }

                Xrecord xrec = new Xrecord { Data = values };
                dict.SetAt(xRecordSearchKey, xrec);
                tr.AddNewlyCreatedDBObject(xrec, true);
                tr.Commit();
                return true;
            }
        }

        /// <summary>
        /// 用于替换扩展字典中的整个一条扩展记录
        /// </summary>
        /// <param name="objId">对象id</param>
        /// <param name="xRecordSearchKey">扩展记录的名称</param>
        /// <param name="values">扩展记录的内容</param>
        public static bool ModObjXrecord(this ObjectId objId, string xRecordSearchKey, TypedValueList values)
        {
            DBObject obj = objId.GetObject(OpenMode.ForRead);//以读的方式打开对象
            ObjectId dictId = obj.ExtensionDictionary;//获取对象的扩展字典id
            if (dictId.IsNull)
            {
                return false;//若对象没有扩展字典，则返回 
            }
            //如果对象有扩展字典，则以读的方式打开
            DBDictionary dict = dictId.GetObject(OpenMode.ForRead) as DBDictionary;
            if (!dict.Contains(xRecordSearchKey))
            {
                return false;//如果扩展字典中没有包含指定关键字的扩展记录，则返回 ；
            }
            ObjectId xrecordId = dict.GetAt(xRecordSearchKey);//获取扩展记录的id
            Xrecord xrecord = xrecordId.GetObject(OpenMode.ForWrite) as Xrecord;
            xrecord.Data = values;//覆盖原来的数据，因为values有了新的指向
            xrecord.DowngradeOpen();//将扩展记录切换为读的状态
            return true;
        }





        /// <summary>
        /// 【已修正】在指定的事务中，修改或添加对象的扩展记录
        /// </summary>
        /// <param name="objId">对象id</param>
        /// <param name="tr">一个活动的事务</param> /// <--- 新增参数
        /// <param name="xRecordSearchKey">扩展记录的名称</param>
        /// <param name="values">扩展记录的内容</param>
        public static bool ModObjXrecord(this ObjectId objId, Transaction tr, string xRecordSearchKey, TypedValueList values)
        {
            // 使用传入的事务 tr 来安全地打开对象
            DBObject obj = tr.GetObject(objId, OpenMode.ForRead);

            // 检查是否存在扩展字典，如果不存在则创建
            if (obj.ExtensionDictionary.IsNull)
            {
                obj.UpgradeOpen();
                obj.CreateExtensionDictionary();
            }

            // 以写模式打开扩展字典
            DBDictionary dict = tr.GetObject(obj.ExtensionDictionary, OpenMode.ForWrite) as DBDictionary;

            // 检查记录是否已存在
            if (dict.Contains(xRecordSearchKey))
            {
                // 如果存在，则更新它
                ObjectId xrecordId = dict.GetAt(xRecordSearchKey);
                Xrecord xrecord = tr.GetObject(xrecordId, OpenMode.ForWrite) as Xrecord;
                xrecord.Data = values;
            }
            else
            {
                // 如果不存在，则创建新的记录
                Xrecord xrecord = new Xrecord { Data = values };
                dict.SetAt(xRecordSearchKey, xrecord);
                tr.AddNewlyCreatedDBObject(xrecord, true);
            }

            return true; // 注意：这里不提交事务，由调用方统一管理
        }















        /// <summary>
        /// 【已修正】在指定的事务中，获取对象的扩展记录。
        /// </summary>
        /// <param name="objId">对象的id</param>
        /// <param name="tr">一个活动的事务</param>
        /// <param name="xRecordSearchKey">扩展记录名称</param>
        /// <returns>扩展记录数据，如果找不到则返回null</returns>
        public static TypedValueList GetObjXrecord(this ObjectId objId, Transaction tr, string xRecordSearchKey)
        {
            // 安全性检查
            if (objId.IsNull || objId.IsErased) return null;

            // 使用传入的事务 tr 来安全地打开对象
            DBObject obj = tr.GetObject(objId, OpenMode.ForRead);

            ObjectId dictId = obj.ExtensionDictionary;
            if (dictId.IsNull) return null;

            DBDictionary dict = tr.GetObject(dictId, OpenMode.ForRead) as DBDictionary;
            if (!dict.Contains(xRecordSearchKey)) return null;

            ObjectId xrecordId = dict.GetAt(xRecordSearchKey);
            Xrecord xrecord = tr.GetObject(xrecordId, OpenMode.ForRead) as Xrecord;

            return xrecord.Data;
        }

        /// <summary>
        /// 获取对象的扩展字典中的扩展记录
        /// </summary>
        /// <param name="objId">对象的id</param>
        /// <param name="xRecordSearchKey">扩展记录名称</param>
        /// <returns></returns>
        public static TypedValueList GetObjXrecordWithOutTrans(this ObjectId objId, string xRecordSearchKey)
        {

            DBObject obj = objId.GetObject(OpenMode.ForRead);//以读的方式打开对象

            ObjectId dictId = obj.ExtensionDictionary;//获取对象的扩展字典的id

            if (dictId.IsNull)
            {
                //MessageBox.Show("没有扩展字典");
                return null;//若对象没有扩展字典，则返回null
            }

            DBDictionary dict = dictId.GetObject(OpenMode.ForRead) as DBDictionary;//获取对象的扩展字典

            if (!dict.Contains(xRecordSearchKey))
            {
                return null;//如果扩展字典中没有包含指定关键字的扩展记录，则返回null；
            }

            //先要获取对象的扩展字典或图形中的有名对象字典，然后才能在字典中获取要查询的扩展记录
            ObjectId xrecordId = dict.GetAt(xRecordSearchKey);//获取扩展记录对象的id

            Xrecord xrecord = xrecordId.GetObject(OpenMode.ForRead) as Xrecord;//根据id获取扩展记录对象

            TypedValueList values = xrecord.Data;

            return values;//values 数组应该是有先后顺序的
        }

        public static TypedValueList GetObjXrecordWithTrans(this ObjectId objId, string xRecordSearchKey)
        {
            if (objId.IsNull)
                return null;

            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null)
                return null;

            using (var locker = doc.LockDocument())
            {
                using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
                {
                    try
                    {
                        DBObject obj = trans.GetObject(objId, OpenMode.ForRead);

                        ObjectId dictId = obj.ExtensionDictionary;

                        if (dictId.IsNull)
                        {
                            return null;
                        }

                        DBDictionary dict = trans.GetObject(dictId, OpenMode.ForRead) as DBDictionary;

                        if (!dict.Contains(xRecordSearchKey))
                        {
                            return null;
                        }

                        ObjectId xrecordId = dict.GetAt(xRecordSearchKey);
                        Xrecord xrecord = trans.GetObject(xrecordId, OpenMode.ForRead) as Xrecord;

                        TypedValueList values = xrecord.Data;

                        trans.Commit();
                        return values;
                    }
                    catch (Exception ex)
                    {
                        trans.Abort();
                        System.Diagnostics.Debug.WriteLine($"获取扩展记录失败: {ex.Message}");
                        return null;
                    }
                }
            }
        }







        /// <summary>
        ///用于删除对象扩展字典中的指定的扩展记录
        /// </summary>
        /// <param name="objId">对象id</param>
        /// <param name="xRecordSearchKey"> 扩展记录名称</param>
        public static bool DelObjXrecord(this ObjectId objId, string xRecordSearchKey)
        {
            DBObject obj = objId.GetObject(OpenMode.ForRead);//以读的方式打开对象
            ObjectId dictId = obj.ExtensionDictionary;//获取对象的扩展字典id
            if (dictId.IsNull)
            {
                return false;//若对象没有扩展字典，则返回 
            }
            //如果对象有扩展字典，则以读的方式打开
            DBDictionary dict = dictId.GetObject(OpenMode.ForRead) as DBDictionary;
            if (dict.Contains(xRecordSearchKey))//如果扩展字典中包含指定关键字的扩展记录，则删除；
            {
                dict.UpgradeOpen();//切换为写的状态
                dict.Remove(xRecordSearchKey);//删除扩展记录
                dict.DowngradeOpen();//切换为读的状态
            }
            return true;
        }       /// <summary>
                /// 删除对象的扩展字典下的所有的扩展记录
                /// 2018年4月7日09:44:12
                /// </summary>
                /// <param name="objId"></param>
        public static bool DelObjAllXrecords(this ObjectId objId)
        {
            DBObject obj = objId.GetObject(OpenMode.ForRead);//以读的方式打开对象
            ObjectId dictId = obj.ExtensionDictionary;//获取对象的扩展字典id
            if (dictId.IsNull)
            {
                return false;//若对象没有扩展字典，则返回 
            }
            //如果对象有扩展字典，则以读的方式打开
            DBDictionary dict = dictId.GetObject(OpenMode.ForWrite) as DBDictionary;
            //获取扩展字典下的所有扩展记录名称集合 Keys 
            List<string> listKeys = new List<string>();
            foreach (var item in dict)//获取扩展字典中的所有条目，也就是所有的扩展记录的key
            {
                listKeys.Add(item.Key);
            }
            foreach (var key in listKeys)//根据key,删除扩展字典中的每一个条目（也就是扩展记录）
            {
                dict.Remove(key);
            }
            dict.DowngradeOpen();//切换为读的状态 
            return true;
        }


        /// <summary>
        /// 删除对象的扩展字典，因为每个对象只能拥有一个扩展字典，所以给定对象的objectID就好了
        ///  2018年4月7日09:17:44 
        /// </summary>
        /// <param name="objId"></param>
        public static bool DeleteObjExtensionDictionary(this ObjectId objId)
        {
            DBObject obj = objId.GetObject(OpenMode.ForRead);//以读的方式打开对象
            ObjectId dictId = obj.ExtensionDictionary;//获取对象的扩展字典的ID
            if (dictId.IsNull)
            {
                return false;    //没有扩展字典
            }
            //有扩展字典 161             obj.UpgradeOpen();//切换对象为写的状态
            objId.DelObjAllXrecords(); //调用上面的方法，在删除扩展字典之前要先删除扩展记录
            obj.ReleaseExtensionDictionary();//移除对象的扩展字典，一个对象只能拥有一个扩展字典
            obj.DowngradeOpen();//将对象切换为读的状态 
            return true;
        }

        #endregion



        #region 泛型查找扩展字典功能

        /// <summary>
        /// 查找指定类型且包含指定应用名称扩展字典的实体
        /// </summary>
        /// <typeparam name="T">实体类型，如BlockReference, DBText, Line等</typeparam>
        /// <param name="applicationName">应用名称</param>
        /// <returns>符合条件的实体列表</returns>
        //public static List<T> FindEntitiesWithExtensionDictionary<T>(string applicationName)
        //    where T : Entity
        //{
        //    var results = new List<T>();
        //    var doc = Application.DocumentManager.MdiActiveDocument;
        //    var db = doc.Database;

        //    using (var trans = db.TransactionManager.StartTransaction())
        //    {
        //        try
        //        {
        //            var allSpaces = GetAllSpaces(db, trans);

        //            foreach (var spaceId in allSpaces)
        //            {
        //                var space = trans.GetObject(spaceId, OpenMode.ForRead) as BlockTableRecord;
        //                foreach (ObjectId objId in space)
        //                {
        //                    var obj = trans.GetObject(objId, OpenMode.ForRead);
        //                    if (obj is T entity)
        //                    {
        //                        if (objId.HasExtensionDictionaryWithApp(applicationName))
        //                        {
        //                            results.Add(entity);
        //                        }
        //                    }
        //                }
        //            }

        //            trans.Commit();
        //        }
        //        catch (Exception ex)
        //        {
        //            trans.Abort();
        //            throw new Exception($"查找扩展字典失败: {ex.Message}");
        //        }
        //    }

        //    return results;
        //}




        /// <summary>
        /// 【已修正】在指定事务中，查找包含指定记录名称的实体
        /// </summary>
        public static List<T> FindEntitiesWithExtensionDictionary<T>(Transaction tr, string recordName) where T : Entity
        {
            var results = new List<T>();
            var db = W.Db;
            var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
            var btr = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

            foreach (ObjectId objId in btr)
            {
                // 检查对象是否是我们需要的类型
                if (objId.ObjectClass.IsDerivedFrom(RXObject.GetClass(typeof(T))))
                {
                    var data = objId.GetObjXrecord(tr, recordName); // 使用我们新的、安全的方法
                    if (data != null)
                    {
                        // 如果找到了，就将实体添加到结果列表
                        results.Add((T)tr.GetObject(objId, OpenMode.ForRead));
                    }
                }
            }
            return results;
        }














        /// <summary>
        /// 查找指定类型且包含指定记录名称的实体
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="recordName">记录名称</param>
        /// <returns>符合条件的实体信息列表</returns>
        public static List<ExtensionDictionaryInfo<T>> FindEntitiesWithRecord<T>(string recordName)
            where T : Entity
        {
            var results = new List<ExtensionDictionaryInfo<T>>();
            var doc = Application.DocumentManager.MdiActiveDocument;
            var db = doc.Database;

            using (var trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var allSpaces = GetAllSpaces(db, trans);

                    foreach (var spaceId in allSpaces)
                    {
                        var space = trans.GetObject(spaceId, OpenMode.ForRead) as BlockTableRecord;
                        foreach (ObjectId objId in space)
                        {
                            var obj = trans.GetObject(objId, OpenMode.ForRead);
                            if (obj is T entity)
                            {
                                var typeValueList = objId.GetObjXrecordWithOutTrans(recordName);
                                if (typeValueList != null)
                                {
                                    results.Add(new ExtensionDictionaryInfo<T>
                                    {
                                        Entity = entity,
                                        EntityId = objId,
                                        RecordName = recordName,
                                        Handle = objId.Handle.ToString(),
                                        Data = typeValueList
                                    });
                                }
                            }
                        }
                    }

                    trans.Commit();
                }
                catch (Exception ex)
                {
                    trans.Abort();
                    throw new Exception($"查找记录失败: {ex.Message}");
                }
            }

            return results;
        }

        /// <summary>
        /// 获取指定类型的所有包含扩展字典的实体
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <returns>扩展字典信息列表</returns>
        public static List<ExtensionDictionaryInfo<T>> GetAllExtensionDictionaries<T>()
            where T : Entity
        {
            var results = new List<ExtensionDictionaryInfo<T>>();
            var doc = Application.DocumentManager.MdiActiveDocument;
            var db = doc.Database;

            using (var trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var allSpaces = GetAllSpaces(db, trans);

                    foreach (var spaceId in allSpaces)
                    {
                        var space = trans.GetObject(spaceId, OpenMode.ForRead) as BlockTableRecord;
                        foreach (ObjectId objId in space)
                        {
                            var obj = trans.GetObject(objId, OpenMode.ForRead);
                            if (obj is T entity)
                            {
                                var infos = objId.GetAllExtensionDictionaryInfos<T>();
                                results.AddRange(infos);
                            }
                        }
                    }

                    trans.Commit();
                }
                catch (Exception ex)
                {
                    trans.Abort();
                    throw new Exception($"获取扩展字典失败: {ex.Message}");
                }
            }

            return results;
        }

        /// <summary>
        /// 检查对象是否包含指定应用名称的扩展字典
        /// </summary>
        /// <param name="objId">对象ID</param>
        /// <param name="applicationName">应用名称</param>
        /// <returns>是否包含</returns>
        public static bool HasExtensionDictionaryWithApp(this ObjectId objId, string applicationName)
        {
            try
            {
                using (var trans = objId.Database.TransactionManager.StartTransaction())
                {
                    var obj = trans.GetObject(objId, OpenMode.ForRead);
                    if (obj.ExtensionDictionary.IsNull)
                        return false;

                    var extDict = trans.GetObject(obj.ExtensionDictionary, OpenMode.ForRead) as DBDictionary;
                    bool result = extDict.Contains(applicationName);
                    trans.Commit();
                    return result;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取对象的所有扩展字典记录信息
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="objId">对象ID</param>
        /// <returns>扩展字典信息列表</returns>
        public static List<ExtensionDictionaryInfo<T>> GetAllExtensionDictionaryInfos<T>(this ObjectId objId)
            where T : Entity
        {
            var results = new List<ExtensionDictionaryInfo<T>>();

            try
            {
                using (var trans = objId.Database.TransactionManager.StartTransaction())
                {
                    var obj = trans.GetObject(objId, OpenMode.ForRead);
                    if (obj.ExtensionDictionary.IsNull || !(obj is T entity))
                    {
                        trans.Commit();
                        return results;
                    }

                    var extDict = trans.GetObject(obj.ExtensionDictionary, OpenMode.ForRead) as DBDictionary;

                    foreach (var entry in extDict)
                    {
                        var typeValueList = objId.GetObjXrecordWithOutTrans(entry.Key);
                        results.Add(new ExtensionDictionaryInfo<T>
                        {
                            Entity = entity,
                            EntityId = objId,
                            RecordName = entry.Key,
                            Handle = objId.Handle.ToString(),
                            Data = typeValueList
                        });
                    }

                    trans.Commit();
                }
            }
            catch
            {
                // 忽略错误，继续处理其他对象
            }

            return results;
        }

        /// <summary>
        /// 获取所有空间（模型空间和图纸空间）
        /// </summary>
        /// <param name="db">数据库</param>
        /// <param name="trans">事务</param>
        /// <returns>空间ID列表</returns>
        private static List<ObjectId> GetAllSpaces(Database db, Transaction trans)
        {
            var spaces = new List<ObjectId>();
            var blockTable = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;

            foreach (ObjectId blockId in blockTable)
            {
                var block = trans.GetObject(blockId, OpenMode.ForRead) as BlockTableRecord;
                if (block.IsLayout)
                {
                    spaces.Add(blockId);
                }
            }

            return spaces;
        }

        /// <summary>
        /// 获取对象扩展字典中所有记录的名称
        /// </summary>
        /// <param name="objId">对象ID</param>
        /// <returns>记录名称列表</returns>
        public static List<string> GetExtensionDictionaryKeys(this ObjectId objId)
        {
            var keys = new List<string>();

            try
            {
                using (var trans = objId.Database.TransactionManager.StartTransaction())
                {
                    var obj = trans.GetObject(objId, OpenMode.ForRead);
                    if (obj.ExtensionDictionary.IsNull)
                    {
                        trans.Commit();
                        return keys;
                    }

                    var extDict = trans.GetObject(obj.ExtensionDictionary, OpenMode.ForRead) as DBDictionary;
                    foreach (var entry in extDict)
                    {
                        keys.Add(entry.Key);
                    }

                    trans.Commit();
                }
            }
            catch
            {
                // 忽略错误
            }

            return keys;
        }

        #endregion
    }
    /// <summary>
    /// 泛型扩展字典信息类
    /// </summary>
    public class ExtensionDictionaryInfo<T> where T : Entity
    {
        public T Entity { get; set; }
        public ObjectId EntityId { get; set; }
        public string RecordName { get; set; }
        public string Handle { get; set; }
        public TypedValueList Data { get; set; }

        public override string ToString()
        {
            return $"{typeof(T).Name} (Handle: {Handle}) - Record: {RecordName}";
        }
    }
}
