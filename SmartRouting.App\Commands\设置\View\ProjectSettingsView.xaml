﻿<Window
    x:Class="SmartRouting.App.Commands.设置.View.ProjectSettingsView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SmartRouting.App.Commands.设置.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="项目参数设置 V20250808"
    Width="250"
    Height="300"
    Closing="Window_Closing"
    Loaded="Window_Loaded"
    ResizeMode="NoResize"
    ShowInTaskbar="False"
    SizeToContent="Manual"
    SnapsToDevicePixels="True"
    Topmost="True"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/SmartRouting.App;component/Common/WPFUI/CommonUIStyle.xaml" />
                <!--<ResourceDictionary Source="pack://application:,,,/SmartRouting.App;component/Common/WPFUI/MetroWindow.xaml" />-->
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <GroupBox
            Grid.Row="0"
            Width="AUTO"
            Height="auto"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            Header="探测点设置">
            <Grid Margin="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="AUTO" />
                </Grid.RowDefinitions>
                <DataGrid
                    x:Name="Dg_BlockInfo"
                    Grid.Row="0"
                    Width="AUTO"
                    MinHeight="150"
                    HorizontalContentAlignment="Stretch"
                    AutoGenerateColumns="False"
                    CanUserAddRows="True"
                    GridLinesVisibility="All"
                    HeadersVisibility="All">
                    <DataGrid.Columns>
                        <DataGridComboBoxColumn
                            x:Name="Cb_BlockName"
                            Width="*"
                            Header="块名称"
                            IsReadOnly="False"
                            SelectedItemBinding="{Binding BlockName, UpdateSourceTrigger=PropertyChanged}" />
                    </DataGrid.Columns>
                </DataGrid>

                <Grid Grid.Row="2" Margin="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="auto" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="线图层" />
                    <TextBox
                        x:Name="Tb_LineLayerName"
                        Grid.Column="1"
                        Width="AUTO"
                        HorizontalAlignment="Stretch"
                        ToolTip="{Binding ElementName=Tb_LineLayerName, Path=Text}" />
                    <Button
                        x:Name="Btn_SelectLineLayer"
                        Grid.Column="2"
                        Click="Btn_SelectLineLayer_Click"
                        Content="选" />
                </Grid>
            </Grid>

        </GroupBox>
        <TextBlock
            Grid.Row="1"
            Margin="5"
            Padding="5"
            HorizontalAlignment="Left"
            Foreground="Blue"
            Text="*以上指定的块,均会被定义为探测点*" />
        <WrapPanel
            Grid.Row="2"
            Grid.Column="0"
            HorizontalAlignment="Right"
            VerticalAlignment="Center">
            <Button
                x:Name="Btn_SaveConfig"
                Click="Btn_SaveConfig_Click"
                Content="保存" />
            <Button
                x:Name="Btn_Cancel"
                Click="Btn_Cancel_Click"
                Content="取消" />
        </WrapPanel>
    </Grid>
</Window>
