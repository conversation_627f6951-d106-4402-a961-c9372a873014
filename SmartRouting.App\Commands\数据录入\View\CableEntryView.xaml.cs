using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using SmartRouting.App.Commands.数据录入.Model;
using System.Windows.Controls;
using GMDI_ForCAD.App.侧边菜单;
using SmartRouting.App.Utils.XRecordUtil;

namespace SmartRouting.App.Commands.数据录入.View
{

    public partial class CableEntryView : Window
    {
        public static readonly string ProjectName = "XXX项目";//这里是否做个开发的接口？
        public WellModel m_WellModel { get; set; }
        public int m_CurrentMaxCount { get; set; }
        public ObjectId m_ObjectId { get; set; }
        public List<PipelineModel> m_PipeModels { get; set; }


        public CableEntryView(ObjectId objectId, WellModel wellModel, int currentMaxCount)
        {
            m_CurrentMaxCount = currentMaxCount;
            m_ObjectId = objectId;
            m_WellModel = wellModel;

            InitializeComponent();
            InitializeData();
        }

        public CableEntryView(ObjectId objectId, WellModel wellModel, List<PipelineModel> pipeModels, int currentMaxCount)
        {
            m_WellModel = wellModel;
            m_CurrentMaxCount = currentMaxCount;
            m_ObjectId = objectId;
            m_PipeModels = pipeModels;
            InitializeComponent();
            InitializeData_ForExisitingPipes();
        }


        /// <summary>
        /// 存在导入的管道，直接读取管道里面的线缆数据
        /// </summary>
        private void InitializeData_ForExisitingPipes()
        {

            ObservableCollection<CableModel> cableModels = new();
            foreach (var pipelineModel in m_PipeModels)
            {
                var cableModel = pipelineModel.CableModels;
                foreach (var cable in cableModel)
                {
                    cable.CableFrom = pipelineModel.PipelineNumber;
                    cable.CableTo = "未分配";
                    cable.Used = false;
                    cableModels.Add(cable);
                }
            }

            m_WellModel.CableModels = cableModels;
            this.DataContext = m_WellModel;
        }

        /// <summary>
        /// 没有关联的管道，属于端头工井，无线缆数据，就默认新增一个
        /// </summary>
        private void InitializeData()
        {
            if (m_WellModel.CableModels == null || m_WellModel.CableModels.Count == 0)
            {
                //如果没有线缆数据，就默认新增一个
                ObservableCollection<CableModel> cableModels = new();
                CableModel cableModel = new CableModel();
                cableModel.CableEntryNumber = $"{ProjectName}-{m_CurrentMaxCount + 1}";
                cableModel.CableTo = "未分配";
                cableModel.CableFrom = "端头工井";
                cableModels.Add(cableModel);
                m_WellModel.CableModels = cableModels;
            }

            this.DataContext = m_WellModel;
        }

        private void Btn_AddRow_Click(object sender, RoutedEventArgs e)
        {
            // 优先使用选中行的数据，如果没有选中则使用最后一行
            CableModel cable = null;
            int insertIndex = -1;

            // 检查是否有选中的行
            if (CablesGrid.SelectedItem is CableModel selectedCable)
            {
                cable = selectedCable;
                // 获取选中行的索引，新行将插入到选中行之后
                insertIndex = m_WellModel.CableModels.IndexOf(selectedCable) + 1;
            }
            else
            {
                // 如果没有选中行，则使用最后一行数据
                cable = m_WellModel.CableModels.LastOrDefault();
                insertIndex = m_WellModel.CableModels.Count; // 添加到末尾
            }

            if (cable != null)
            {
                var nextCableNumber = GetNextCableNumber(cable.CableEntryNumber);
                CableModel nextCableModel = new CableModel();
                nextCableModel.Remarks = "";
                nextCableModel.CableTo = "未分配";
                nextCableModel.CableFrom = cable.CableFrom;
                nextCableModel.CableEntryNumber = nextCableNumber;
                nextCableModel.CableEntryEnum = cable.CableEntryEnum;
                nextCableModel.Specification = cable.Specification;
                nextCableModel.TelecomOperatorEnum = cable.TelecomOperatorEnum;
                nextCableModel.OperationEnum = cable.OperationEnum;

                // 在指定位置插入新行
                m_WellModel.CableModels.Insert(insertIndex, nextCableModel);

                // 选中新添加的行
                CablesGrid.SelectedItem = nextCableModel;
                CablesGrid.ScrollIntoView(nextCableModel);
            }
        }

        /// <summary>
        /// 用于编号的递增算法
        /// </summary>
        /// <param name="currentNumber"></param>
        /// <returns></returns>
        private string GetNextCableNumber(string currentNumber)
        {
            if (string.IsNullOrEmpty(currentNumber)) return $"{ProjectName}-{m_CurrentMaxCount + 1}";

            var parts = currentNumber.Split('-');
            if (parts.Length > 1 && int.TryParse(parts.Last(), out int number))
            {
                var prefix = string.Join("-", parts.Take(parts.Length - 1));
                return $"{prefix}-{(number > m_CurrentMaxCount ? number : m_CurrentMaxCount) + 1}";
            }
            return $"{ProjectName}-{m_CurrentMaxCount + 1}";    // Fallback if parsing fails
        }

        private void JointRadioButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is RadioButton radioButton && radioButton.DataContext is CableModel cable)
            {
                // 切换接头操作类型
                if (cable.OperationEnum == OperationEnum.标记接头_总)
                {
                    cable.OperationEnum = OperationEnum.标记接头_分支;
                }
                else
                {
                    cable.OperationEnum = OperationEnum.标记接头_总;
                }
            }
        }


        /// <summary>
        /// 往工井中写入线缆数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_ImportData_Click(object sender, RoutedEventArgs e)
        {
            using (var locker = W.DocLock)
            {
                using (var tr = W.Trans)
                {
                    W.FocusDoc();
                    if (m_WellModel != null && m_WellModel.CableModels != null && m_WellModel.CableModels.Count > 0)
                    {

                        var valueList = GenericExtensionDictionaryManager.ObjectToTypeValueList<WellModel>(m_WellModel);
                        try
                        {
                            var success = m_ObjectId.ModObjXrecord(typeof(WellModel).Name, valueList);
                            if (success)
                            {
                                //更新全局最大的函数编号
                                var cables = m_WellModel.CableModels.OrderByDescending(c => c.CableEntryNumber).FirstOrDefault();
                                var numeber = cables.CableEntryNumber;

                                var parts = numeber.Split('-');
                                if (parts.Length > 1 && int.TryParse(parts.Last(), out int number))
                                {
                                    bool updataMaxNumber = CustomDataStorage.SetCustomData("MaxNumber", number.ToString());//设置全局编号
                                }
                                //气泡弹窗显示
                                GlobalSetting.ShowBalloonTip();
                            }
                        }
                        catch (Exception ex)
                        {
                            return;
                        }

                    }

                    tr.Commit();
                }

            }

        }

        private void Btn_Cancel_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void Btn_UpLoadPic_Click(object sender, RoutedEventArgs e)
        {
            var destDir = "C:\\Plugin_ForCAD\\Config\\图片\\" + ProjectName;
            // 使用WPF原生的OpenFileDialog，以避免在AutoCAD中与WinForms的焦点冲突问题
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "选择图片文件",
                Filter = "图片文件|*.jpg;*.png"
            };

            // WPF的ShowDialog返回一个可空的布尔值 (bool?)
            if (openFileDialog.ShowDialog() == true)
            {
                if (!Directory.Exists(destDir))
                {
                    Directory.CreateDirectory(destDir);
                }
                try
                {
                    string picPath = openFileDialog.FileName;
                    // 修正文件复制逻辑，确保目标是一个完整的文件路径
                    string destPath = Path.Combine(destDir, Path.GetFileName(picPath));

                    File.Copy(picPath, destPath, true);
                    m_WellModel.WellImage = destPath;
                    Tb_Pic.Text = Path.GetFileName(picPath);
                }
                catch (Exception ex)
                {
                    MessageBoxUtil.Message_Error($"上传照片时出错: {ex.Message}");
                }
            }
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {

        }
    }
}