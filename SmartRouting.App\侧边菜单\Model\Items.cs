﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.侧边菜单.Model
{
    public class Items : ObservableCollection<Item>
    {
        public static Items GetItems()
        {
            Items items = new Items();
            var userCmds = ReflectionArrtibute();
            foreach (var usercmd in userCmds)
            {
                //这里需要故意把type放到des的位置才可以显示正确
                Item item = new Item() { Type = "", Des = usercmd.Type, Command = "", ToolTip = "" };
                item.SetChildrenDefaultValue(item.Des);
                if (!items.Any(S => S.Des == item.Des))
                {
                    items.Add(item);
                }
            }
            return items;
        }



        /// <summary>
        /// 获取方法上所有的标签
        /// </summary>
        /// <typeparam name="T"></typeparam>
        public static List<UserCmd> ReflectionArrtibute()
        {
            List<UserCmd> cmdList = new List<UserCmd>();
            ///循环获取所有方法上标记的特性，调用特性中的元素

            var types = Assembly.GetExecutingAssembly().GetTypes().ToList();

            foreach (var type in types)
            {
                foreach (MethodInfo method in type.GetMethods())
                {
                    if (method.IsDefined(typeof(CommandTagAttribute), false))
                    {
                        foreach (var attribute in method.GetCustomAttributes()) //把所有标记的特性都实例化了
                        {
                            if (attribute is CommandTagAttribute commandTagAttribute)
                            {
                                var type2 = commandTagAttribute.Type;
                                var cmd = commandTagAttribute.Cmd;
                                var description = commandTagAttribute.Description;
                                var toolTip = commandTagAttribute.ToolTip;

                                UserCmd userCmd = new UserCmd();
                                userCmd.Command = cmd;
                                userCmd.Des = description;
                                userCmd.Type = type2;
                                userCmd.ToolTip = toolTip;
                                cmdList.Add(userCmd);
                            }
                        }
                    }
                }
            }



            cmdList = cmdList.OrderBy(x => x.Des).ToList();

            return cmdList;
        }




    }


}
