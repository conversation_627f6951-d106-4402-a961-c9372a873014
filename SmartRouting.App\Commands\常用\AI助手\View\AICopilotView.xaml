﻿<Window
    x:Class="SmartRouting.App.Commands.常用.AI助手.View.AICopilotView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:avalonedit="http://icsharpcode.net/sharpdevelop/avalonedit"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SmartRouting.App.Commands.常用.AI助手.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="AI助手ForCAD V0619"
    Width="700"
    Height="500"
    MinWidth="600"
    MinHeight="400"
    Closing="Window_Closing"
    FontFamily="Segoe UI"
    Loaded="Window_Loaded"
    ResizeMode="CanResizeWithGrip"
    Topmost="True"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">
    <Window.InputBindings>
        <KeyBinding
            Key="C"
            Command="ApplicationCommands.Copy"
            Modifiers="Control" />
        <KeyBinding
            Key="V"
            Command="ApplicationCommands.Paste"
            Modifiers="Control" />
    </Window.InputBindings>
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/SmartRouting.App;component/Common/WPFUI/CommonUIStyle.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  颜色定义  -->
            <SolidColorBrush x:Key="WindowBackgroundColor" Color="#FF1E1E1E" />
            <SolidColorBrush x:Key="ContentBackgroundColor" Color="#FF2D2D30" />
            <SolidColorBrush x:Key="ControlBackgroundBrush" Color="#FF3C3C3C" />
            <SolidColorBrush x:Key="ControlForegroundBrush" Color="#FFCCCCCC" />
            <SolidColorBrush x:Key="ControlBorderBrush" Color="#FF555555" />
            <SolidColorBrush x:Key="AccentBrush" Color="#FF007ACC" />
            <SolidColorBrush x:Key="AccentHoverBrush" Color="#FF0099FF" />
            <SolidColorBrush x:Key="AccentPressedBrush" Color="#FF005C99" />
            <SolidColorBrush x:Key="SecondaryButtonBackgroundBrush" Color="#FF4A4A4A" />
            <SolidColorBrush x:Key="SecondaryButtonHoverBrush" Color="#FF5A5A5A" />
            <SolidColorBrush x:Key="SecondaryButtonPressedBrush" Color="#FF3A3A3A" />
            <SolidColorBrush x:Key="TextPlaceholderBrush" Color="#FF888888" />

            <!--  按钮样式  -->
            <Style x:Key="StandardButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="{StaticResource AccentBrush}" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Padding" Value="15,8" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" CornerRadius="4">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{StaticResource AccentHoverBrush}" />
                    </Trigger>
                    <Trigger Property="IsPressed" Value="True">
                        <Setter Property="Background" Value="{StaticResource AccentPressedBrush}" />
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!--  次要按钮样式  -->
            <Style x:Key="SecondaryButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="{StaticResource SecondaryButtonBackgroundBrush}" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Padding" Value="15,8" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" CornerRadius="4">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{StaticResource SecondaryButtonHoverBrush}" />
                    </Trigger>
                    <Trigger Property="IsPressed" Value="True">
                        <Setter Property="Background" Value="{StaticResource SecondaryButtonPressedBrush}" />
                    </Trigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </Window.Resources>

    <!--  内容区域  -->
    <Grid Margin="15,10,15,10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="0.15*" MinHeight="60" />
            <RowDefinition Height="30" />
            <RowDefinition Height="0.75*" MinHeight="150" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <TextBlock
            Grid.Row="0"
            Margin="0,0,0,5"
            HorizontalAlignment="Left"
            Foreground="{StaticResource ControlForegroundBrush}"
            Text="请输入提示词" />

        <TextBox
            x:Name="txtPrompt"
            Grid.Row="1"
            Margin="0,0,0,8"
            VerticalContentAlignment="Top"
            AcceptsReturn="True"
            Background="{StaticResource ControlBackgroundBrush}"
            BorderBrush="{StaticResource ControlBorderBrush}"
            CaretBrush="{StaticResource ControlForegroundBrush}"
            Foreground="{StaticResource ControlForegroundBrush}"
            TextWrapping="Wrap" />

        <Button
            x:Name="GenerateCode"
            Grid.Row="2"
            Width="75"
            Margin="0,0,0,8"
            HorizontalAlignment="Right"
            Click="GenerateCode_Click"
            Content="提交问题"
            Style="{StaticResource StandardButtonStyle}" />

        <!--  代码编辑器 - 使用兼容版本的 AvalonEdit  -->
        <Border
            Grid.Row="3"
            Margin="0,0,0,10"
            BorderBrush="{StaticResource ControlBorderBrush}"
            BorderThickness="1">
            <avalonedit:TextEditor
                x:Name="codeEditor"
                Background="{StaticResource ContentBackgroundColor}"
                FontFamily="Consolas"
                Foreground="{StaticResource ControlForegroundBrush}"
                ShowLineNumbers="True"
                SyntaxHighlighting="C#" />
        </Border>

        <!--  底部按钮  -->
        <Grid Grid.Row="4">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <Button
                x:Name="SettingsButton"
                Grid.Column="0"
                Width="32"
                Height="32"
                Padding="0"
                HorizontalAlignment="Left"
                Click="SettingsButton_Click"
                Content="⚙️"
                FontSize="16"
                Style="{StaticResource SecondaryButtonStyle}" />

            <Button
                x:Name="RunCode"
                Grid.Column="2"
                Width="75"
                Height="25"
                Margin="0,0,8,0"
                Click="RunCode_Click"
                Content="运行"
                Style="{StaticResource StandardButtonStyle}" />
            <Button
                x:Name="Cancel"
                Grid.Column="3"
                Width="75"
                Height="25"
                Click="Cancel_Click"
                Content="取消"
                Style="{StaticResource SecondaryButtonStyle}" />
        </Grid>
    </Grid>
</Window>
