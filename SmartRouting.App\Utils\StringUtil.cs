﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    public class StringUtil
    {


        /// <summary>
        /// 根据索引替换逗号分隔字符串中的内容。从0开始，修改索引编号后面的元素。
        /// </summary>
        /// <param name="input">原始输入字符串，例如 "XXX项目-3,光缆,4,信息,复制,未分配"</param>
        /// <param name="index">要替换的元素的索引（从0开始）</param>
        /// <param name="replacement">新的字符串</param>
        /// <returns>替换后的新字符串</returns>
        public static string ReplacePartAtIndex(string input, int index, string replacement)
        {
            if (string.IsNullOrEmpty(input))
            {
                return string.Empty;
            }

            // 1. 使用逗号分割字符串，并转换为列表以便修改
            List<string> parts = input.Split(',').ToList();

            // 2. 检查索引是否在有效范围内
            if (index >= 0 && index < parts.Count)
            {
                // 3. 替换指定索引的元素
                parts[index] = replacement;
            }
            else
            {
                // 如果索引无效，可以选择抛出异常或如此处一样返回原字符串
                Console.WriteLine($"警告: 索引 {index} 超出范围。未进行任何替换。");
                return input;
            }

            // 4. 将列表重新用逗号连接成字符串并返回
            return string.Join(",", parts);
        }



        /// <summary>
        /// 根据分隔符出现的位置提取子字符串。
        /// </summary>
        /// <param name="str">要处理的字符串，例如 "part1,part2,part3"。</param>
        /// <param name="start">起始分隔符的序号（1-based）。例如，1代表第一个逗号。使用0代表从字符串开头开始。</param>
        /// <param name="end">结束分隔符的序号（1-based）。例如，2代表第二个逗号。使用大于分隔符总数的序号代表到字符串末尾。</param>
        /// <returns>提取的文本。如果索引无效则返回null；如果起始分隔符在结束分隔符之后，则返回空字符串。</returns>
        public static string GetText(string str, int start, int end)
        {
            if (string.IsNullOrEmpty(str) || start < 0 || end <= start)
            {
                return null; // 索引无效
            }

            char delimiter = ',';

            List<int> delimiterIndices = new List<int>();
            for (int i = 0; i < str.Length; i++)
            {
                if (str[i] == delimiter)
                {
                    delimiterIndices.Add(i);
                }
            }

            int startIndex;
            if (start == 0)
            {
                startIndex = 0; // 从字符串开头
            }
            else if (start > 0 && start <= delimiterIndices.Count)
            {
                startIndex = delimiterIndices[start - 1] + 1;
            }
            else
            {
                return null; // 起始分隔符未找到
            }

            int endIndex;
            if (end > delimiterIndices.Count)
            {
                endIndex = str.Length; // 到字符串末尾
            }
            else
            {
                // 'end' 是 1-based, 所以我们访问 'end - 1'
                endIndex = delimiterIndices[end - 1];
            }

            if (startIndex > str.Length)
            {
                return string.Empty; // 如果字符串以起始分隔符结尾，则出现这种情况
            }

            if (startIndex > endIndex)
            {
                return string.Empty; // 起始分隔符在结束分隔符之后
            }

            return str.Substring(startIndex, endIndex - startIndex);
        }
    }
}
