﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    public static class LineTypeUtil
    {
        public static List<string> GetTotalLineTypeWithTransactions(this Database db)
        {

            List<string> strings = new List<string>();
            using (W.DocLock)
            {
                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    LinetypeTable linetypeTable = tr.GetObject(db.LinetypeTableId, OpenMode.ForRead) as LinetypeTable;

                    foreach (ObjectId linetypeId in linetypeTable)
                    {
                        LinetypeTableRecord linetype = tr.GetObject(linetypeId, OpenMode.ForRead) as LinetypeTableRecord;
                        strings.Add(linetype.Name);
                    }

                    tr.Commit();
                }
            }


            return strings;
        }
    }
}
