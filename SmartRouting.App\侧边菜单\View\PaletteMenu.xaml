﻿<UserControl
    x:Class="SmartRouting.App.侧边菜单.View.PaletteMenu"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SmartRouting.App.侧边菜单.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="500"
    d:DesignWidth="120"
    Loaded="UserControl_Loaded"
    mc:Ignorable="d">
    <ScrollViewer Margin="0" VerticalScrollBarVisibility="Hidden">
        <ScrollViewer.Content>
            <ItemsControl x:Name="itemsControl" ItemsSource="{Binding XPath=Group}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Expander
                            Background="{Binding XPath=@Background}"
                            Expanded="Expander_Expanded"
                            FontSize="14"
                            Foreground="#000000"
                            Header="{Binding XPath=@Title}">
                            <StackPanel Background="#222933">
                                <ItemsControl ItemsSource="{Binding XPath=Item}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Button
                                                Margin="0,1"
                                                HorizontalContentAlignment="Left"
                                                Background="{Binding XPath=@Background}"
                                                Click="Button_Click"
                                                FontSize="14"
                                                Foreground="{Binding XPath=@Foreground}"
                                                Tag="{Binding XPath=@Cmd}"
                                                ToolTip="{Binding XPath=@ToolTip}">
                                                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                                                    <!--  图标  -->
                                                    <Image
                                                        Width="16"
                                                        Height="16"
                                                        Margin="0,0,5,0"
                                                        Source="/SmartRouting.App;component/Resources/98功能x32.png" />
                                                    <!--  按钮文本  -->
                                                    <TextBlock Text="{Binding XPath=@Text}" />
                                                </StackPanel>
                                            </Button>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Expander>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer.Content>
    </ScrollViewer>
</UserControl>





