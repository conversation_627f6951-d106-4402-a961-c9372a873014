﻿//using Autodesk.AutoCAD.StatusBar;
using GMDI_ForCAD.App.侧边菜单;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    public class PaneTextUtil
    {
        ///// <summary>
        ///// 公共方法：获取状态栏窗格的当前文本，供UI或其他类调用
        ///// </summary>
        //public static string GetPaneText()
        //{
        //    if (UserMenuCmd.customPane != null)
        //    {
        //        return UserMenuCmd.customPane.Text;
        //    }
        //    return string.Empty; // 如果窗格不存在，返回空字符串
        //}

        /// <summary>
        /// 公共的辅助方法，用来更新窗格的文本
        /// </summary>
        /// <param name="text">要显示的新文本</param>
        //public static void UpdatePaneText(string text)
        //{
        //    if (UserMenuCmd.customPane != null)
        //    {
        //        UserMenuCmd.customPane.Text = text;
        //    }
        //}

        /// <summary>
        /// 辅助方法，在更新文本的同时在命令行显示消息
        /// </summary>
        /// <param name="text">要显示的新文本</param>
        //public static void UpdatePaneTextWithCommandMessage(string text)
        //{
        //    UpdatePaneText(text); // 调用公共方法来更新文本
        //                          // W.Ed($"\n状态已更新为: {text}");
        //}
    }
}
