﻿using SmartRouting.App.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SmartRouting.App.Commands.图层管理.View
{
    public partial class InpuDwgFilesView : System.Windows.Window
    {
        public InpuDwgFilesView()
        {
            InitializeComponent();
        }
        public List<string> dwgFilePaths = new List<string>();
        private void Btn_OpenDWG_Click(object sender, RoutedEventArgs e)
        {

            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Title = "请选择DWG文件";
            openFileDialog.Filter = "DWG文件(*.DWG)|*.DWG";
            openFileDialog.FilterIndex = 1;
            openFileDialog.Multiselect = true;
            if (openFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                var paths = openFileDialog.FileNames;
                foreach (var item in paths)
                {
                    dwgFilePaths.Add(item);
                }
                MessageBoxUtil.MessageInformation("数据读取成功!");
            }

        }

        private void Btn_Creation_Click(object sender, RoutedEventArgs e)
        {
            Close();
            if (!dwgFilePaths.Any())
            {
                MessageBoxUtil.Message_Error("请先选择dwg文件!");
                return;
            }
            using (W.DocLock)
            {
                //W.FocusDoc();
                foreach (var dwgFilePath in dwgFilePaths)
                {

                    try
                    {
                        var brf = ModelUtil.InsertBlockRef2(W.Doc, dwgFilePath, "*", Point3d.Origin, 0.0, new Scale3d(1, 1, 1));
                        if (brf != null)
                        {
                            using (Transaction curtr = W.Trans)
                            {

                                BlockReference curbt = (BlockReference)curtr.GetObject(brf.Id, OpenMode.ForWrite);
                                curbt.ExplodeToOwnerSpace();
                                curbt.EraseEntity<BlockReference>();
                                curtr.Commit();
                            }
                        }

                    }
                    catch (System.Exception ex)
                    {
                        continue;
                    }


                }

            }




        }



        private void Btn_Setting_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
            DwgSettingView win = new DwgSettingView();
           CadApp.ShowModelessWindow(win);
        }














    }
}
