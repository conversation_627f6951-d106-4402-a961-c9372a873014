﻿
using Microsoft.CodeAnalysis;
using SmartRouting.App.Commands.常用.AI助手.View;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace SmartRouting.App.Commands.常用.AI助手
{
    public class AICopilot
    {
     
        [CommandTag("常    用", nameof(AISmartCopilotCmd), "AI助手")]
        [CommandMethod(nameof(AISmartCopilotCmd))]
        public void AISmartCopilotCmd()
        {
            #region 判断是否过了使用期限
            if (GlobalSetting.SetDeadLine(2025,11,30))
            {
                MessageBoxUtil.MessageInformation("软件已过使用期限或未联网，请联系开发者!");
                return;
            }
            #endregion


            var platform = CadIdentifierUtil.GetCadPlatform();
            string versionInfo = "Unknown";
            if (platform== CadIdentifierUtil.CadPlatform.AutoCAD)
            {
                versionInfo = CadIdentifierUtil.GetAutoCADVersion();
                if (versionInfo.StringToInt() <= 2022)
                {
                    W.Ed.WriteMessage($"\nVersion Info: {versionInfo}");
                    MessageBoxUtil.Message_Error("当前AUTOCAD版本过低，此功能暂时无法使用!");
                    return;
                }
            }
            WindowManagerUtil.Show<AICopilotView>();//单例模式
        }
    }
}
