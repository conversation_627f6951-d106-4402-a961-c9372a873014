﻿using SmartRouting.App;
using SmartRouting.App.侧边菜单.Model;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Xml;
using System.Xml.Serialization;

namespace SmartRouting.App.侧边菜单.View
{
    /// <summary>
    /// UserControl1.xaml 的交互逻辑
    /// </summary>
    public partial class PaletteMenu : UserControl
    {
        public static string xmlPath = "C:\\Plugin_ForCAD\\Config\\UserMenu.xml";
        public PaletteMenu()
        {
            InitializeComponent();
            if (!Directory.Exists("C:\\Plugin_ForCAD\\Config"))
            {
                Directory.CreateDirectory("C:\\Plugin_ForCAD\\Config");
            }
            WriteCmd2Xml();
            var xml = new XmlDocument();
            xml.Load(xmlPath);
            var xmld = new XmlDataProvider();
            xmld.Document = xml;
            xmld.XPath = @"User_Menu";
            itemsControl.DataContext = xmld;
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            var btn = (Button)sender;
            W.FocusDoc();
            if (btn.Tag is XmlAttribute cmd)
            {
                RunLispCmd(cmd.Value.ToString());
            }



        }
        public static void RunLispCmd(string cmd)
        {
            var doc = W.Doc;
            StringBuilder sb = new StringBuilder();
            var cmdActive = CadApp.GetSystemVariable("CMDACTIVE");
            if (null != cmdActive)
            {
                int n = Convert.ToInt32(cmdActive);
                if (n != 0)
                {
                    char c = (char)0x1b;
                    sb.Append(c);
                    sb.Append(c);
                    sb.Append(c);
                    sb.Append(cmd);
                }
            }
            if (sb.Length == 0)
                sb.Append(cmd);
            sb.Append(" ");

            CadApp.DocumentManager.MdiActiveDocument.SendStringToExecute(sb.ToString(), true, false, false);


        }

        private void Expander_Expanded(object sender, RoutedEventArgs e)
        {
            var exp = sender as Expander;
            itemsControl.FindVisualChilds<Expander>().ToList().ForEach(x =>
      {
          if (x.IsExpanded && x != exp)
          {
              x.IsExpanded = false;
          }
      }
            );
        }

        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {

        }

        public static void WriteCmd2Xml()
        {
            //定义元组集合
            var tuples = new List<Tuple<string, string, string, string>>();//依次为：父节点名称、自身快捷建、自身命令名称、提示说明
            List<UserCmd> cmds = Items.ReflectionArrtibute();//得到项目设置了特性的快捷键
            var groups = cmds.GroupBy(s => s.Type).ToList();//根据类型来分组
            var count = groups.Count;
            for (int i = 0; i < count; i++)
            {
                var childs = new ObservableCollection<ChildrenModel>();
                foreach (var item in groups[i])
                {
                    tuples.Add(Tuple.Create(groups[i].First().Type, item.Command, item.Des, item.ToolTip));
                }
            }
            // 将元组集合转换为GMDIMenu对象
            UserMenu menu = MenuData2Xml.ConvertTuplesToGMDIMenu(tuples);
            // 序列化对象到XML文件
            XmlSerializer serializer = new XmlSerializer(typeof(UserMenu));
            using (StreamWriter writer = new StreamWriter(xmlPath, false, System.Text.Encoding.UTF8))
            {
                serializer.Serialize(writer, menu);
            }


        }
    }


    public static class VisualTreeHelperExtensions
    {
        public static IEnumerable<T> FindVisualChilds<T>(this DependencyObject depObj)
            where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                if (child is T tChild)
                    yield return tChild;

                foreach (T foundChild in FindVisualChilds<T>(child))
                    yield return foundChild;
            }
        }
    }





}
