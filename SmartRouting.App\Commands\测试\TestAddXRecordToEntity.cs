﻿using SmartRouting.App.Commands.测试.View;
using SmartRouting.App.Commands.线路连接.View;
using SmartRouting.App.Utils.XRecordUtil;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartRouting.App.Commands.测试
{
    public class TestAddXRecordToEntity
    {
        [CommandTag("排    模", nameof(TestAddXRecordToEntityCmd), "测试")]
        [CommandMethod(nameof(TestAddXRecordToEntityCmd))]
        public void TestAddXRecordToEntityCmd()
        {


            var doc = W.Doc;
            if (doc == null) return;
            var ed = doc.Editor;

            // 1. 提示用户选择一个实体
            var peo = new PromptEntityOptions("\n请选择一个要导出扩展数据的实体:");
            var per = ed.GetEntity(peo);

            if (per.Status != PromptStatus.OK)
            {
                return; // 如果用户取消了选择，则退出
            }

            ObjectId selectedId = per.ObjectId;

            // 2. 弹出文件保存对话框
            var saveDialog = new SaveFileDialog
            {
                Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*",
                Title = "请选择要保存该实体扩展数据的文件",
                FileName = $"Entity_{selectedId.Handle}.txt" // 建议一个默认文件名
            };

            // 3. 如果用户选择了路径并点击了 "OK"
            if (saveDialog.ShowDialog() == DialogResult.OK)
            {
                // 4. 调用我们刚刚创建的导出函数
                ExtensionDictionaryManager.ExportSingleEntityDataAsJson(selectedId, saveDialog.FileName);
            }
        }




    }
}
