﻿<Window
    x:Class="SmartRouting.App.Commands.常用.AI助手.View.ApiKeySettingsView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    Title="API密钥设置"
    Width="500"
    Height="150"
    MinWidth="500"
    Background="#FF1E1E1E"
    FontFamily="Segoe UI"
    Foreground="#FFCCCCCC"
    ResizeMode="NoResize"
    SizeToContent="WidthAndHeight"
    WindowStartupLocation="CenterOwner">
    <Grid Margin="4">
        <Grid.RowDefinitions>
            <RowDefinition Height="30" />
            <RowDefinition Height="*" />
            <RowDefinition Height="30" />
        </Grid.RowDefinitions>

        <TextBlock Margin="2" Text="请输入您的 Deepseek API Key:" />

        <TextBox
            x:Name="ApiKeyTextBox"
            Grid.Row="1"
            Width="AUTO"
            Padding="5"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Center"
            Background="#FF3C3C3C"
            BorderBrush="#FF555555"
            CaretBrush="#FFCCCCCC"
            Foreground="#FFCCCCCC" />

        <Button
            x:Name="SaveButton"
            Grid.Row="2"
            Width="75"
            Padding="2"
            HorizontalAlignment="Right"
            VerticalAlignment="Bottom"
            Background="#FF007ACC"
            BorderThickness="0"
            Click="SaveButton_Click"
            Content="保存并关闭"
            Foreground="White" />
    </Grid>
</Window>
