﻿using SmartRouting.App.Commands.图层管理.View;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.数据导出
{
    public class DataExport
    {
        [CommandTag("排    模", nameof(DataExportCmd), "数据导出")]
        [CommandMethod(nameof(DataExportCmd))]
        public void DataExportCmd()
        {
            #region 判断是否过了使用期限
            if (GlobalSetting.SetDeadLine())
            {
                MessageBoxUtil.MessageInformation("软件已过使用期限或未联网，请联系开发者!");
                return;
            }
            #endregion

        }

    }
}
