﻿<Window
    x:Class="SmartRouting.App.Commands.测试.View.CableEntryManagerWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SmartRouting.App.Commands.测试.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="光缆录入数据管理器"
    Width="1200"
    Height="800"
    WindowStartupLocation="CenterScreen">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  实体选择区域  -->
        <GroupBox
            Grid.Row="0"
            Margin="0,0,0,10"
            Header="实体选择">
            <Grid Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <Label
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Content="选中实体:" />
                <TextBox
                    x:Name="TxtSelectedEntity"
                    Grid.Column="1"
                    Margin="5,0"
                    VerticalAlignment="Center"
                    Background="LightGray"
                    IsReadOnly="True" />
                <Button
                    x:Name="BtnSelectEntity"
                    Grid.Column="2"
                    Width="80"
                    Height="30"
                    Click="BtnSelectEntity_Click"
                    Content="选择实体" />
            </Grid>
        </GroupBox>

        <!--  数据编辑区域  -->
        <GroupBox
            Grid.Row="1"
            Margin="0,0,0,10"
            Header="光缆录入数据编辑">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="150" />
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="150" />
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <!--  第一行  -->
                <Label
                    Grid.Row="0"
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Content="序号:" />
                <TextBox
                    x:Name="TxtCableEntryNumber"
                    Grid.Row="0"
                    Grid.Column="1"
                    Height="25"
                    Margin="0,2"
                    VerticalAlignment="Center" />

                <Label
                    Grid.Row="0"
                    Grid.Column="2"
                    VerticalAlignment="Center"
                    Content="权属:" />
                <ComboBox
                    x:Name="CmbTelecomOperator"
                    Grid.Row="0"
                    Grid.Column="3"
                    Height="25"
                    Margin="0,2"
                    VerticalAlignment="Center" />

                <Label
                    Grid.Row="0"
                    Grid.Column="4"
                    VerticalAlignment="Center"
                    Content="选择:" />
                <CheckBox
                    x:Name="ChkIsSelected"
                    Grid.Row="0"
                    Grid.Column="5"
                    Margin="5,0"
                    VerticalAlignment="Center" />

                <!--  第二行  -->
                <Label
                    Grid.Row="1"
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Content="光缆类型:" />
                <ComboBox
                    x:Name="CmbCableEntryEnum"
                    Grid.Row="1"
                    Grid.Column="1"
                    Height="25"
                    Margin="0,2"
                    VerticalAlignment="Center"
                    SelectionChanged="CmbCableEntryEnum_SelectionChanged" />

                <Label
                    Grid.Row="1"
                    Grid.Column="2"
                    VerticalAlignment="Center"
                    Content="规格:" />
                <ComboBox
                    x:Name="CmbSpecification"
                    Grid.Row="1"
                    Grid.Column="3"
                    Height="25"
                    Margin="0,2"
                    VerticalAlignment="Center" />

                <Label
                    Grid.Row="1"
                    Grid.Column="4"
                    VerticalAlignment="Center"
                    Content="操作:" />
                <ComboBox
                    x:Name="CmbOperationEnum"
                    Grid.Row="1"
                    Grid.Column="5"
                    Height="25"
                    Margin="0,2"
                    VerticalAlignment="Center" />

                <!--  第三行  -->
                <Label
                    Grid.Row="2"
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Content="备注:" />
                <TextBox
                    x:Name="TxtRemarks"
                    Grid.Row="2"
                    Grid.Column="1"
                    Grid.ColumnSpan="3"
                    Height="25"
                    Margin="0,2"
                    VerticalAlignment="Center" />

                <Label
                    Grid.Row="2"
                    Grid.Column="4"
                    VerticalAlignment="Center"
                    Content="状态:" />
                <TextBox
                    x:Name="TxtState"
                    Grid.Row="2"
                    Grid.Column="5"
                    Height="25"
                    Margin="0,2"
                    VerticalAlignment="Center" />

                <!--  第四行 - 操作按钮  -->
                <StackPanel
                    Grid.Row="3"
                    Grid.Column="0"
                    Grid.ColumnSpan="6"
                    Margin="0,15,0,0"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        x:Name="BtnAdd"
                        Width="100"
                        Height="30"
                        Margin="5,0"
                        Click="BtnAdd_Click"
                        Content="添加扩展记录" />
                    <Button
                        x:Name="BtnUpdate"
                        Width="100"
                        Height="30"
                        Margin="5,0"
                        Click="BtnUpdate_Click"
                        Content="更新扩展记录" />
                    <Button
                        x:Name="BtnDelete"
                        Width="100"
                        Height="30"
                        Margin="5,0"
                        Click="BtnDelete_Click"
                        Content="删除扩展记录" />
                    <Button
                        x:Name="BtnClear"
                        Width="80"
                        Height="30"
                        Margin="5,0"
                        Click="BtnClear_Click"
                        Content="清空界面" />
                    <Button
                        x:Name="BtnRefresh"
                        Width="60"
                        Height="30"
                        Margin="5,0"
                        Click="BtnRefresh_Click"
                        Content="刷新" />
                </StackPanel>
            </Grid>
        </GroupBox>

        <!--  扩展记录显示区域  -->
        <GroupBox
            Grid.Row="2"
            Margin="0,0,0,10"
            Header="扩展记录列表">
            <DataGrid
                x:Name="DgRecords"
                Margin="5"
                AutoGenerateColumns="False"
                CanUserAddRows="False"
                SelectionChanged="DgRecords_SelectionChanged"
                SelectionMode="Single">



                <DataGrid.Columns>
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding CableEntryName}"
                        Header="工井名称" />
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding CableEntryTelecomOperator}"
                        Header="工井权属" />
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding CableEntryImage}"
                        Header="工井照片" />
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding CableEntryNumber}"
                        Header="序号" />
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding TelecomOperatorEnum}"
                        Header="权属" />
                    <DataGridTextColumn
                        Width="100"
                        Binding="{Binding CableEntryEnum}"
                        Header="光缆类型" />
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding Specification}"
                        Header="规格" />
                    <DataGridTextColumn
                        Width="120"
                        Binding="{Binding OperationEnum}"
                        Header="操作" />
                    <DataGridCheckBoxColumn
                        Width="60"
                        Binding="{Binding IsSelected}"
                        Header="选择" />
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding State}"
                        Header="状态" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Remarks}"
                        Header="备注" />
                    <DataGridTextColumn
                        Width="120"
                        Binding="{Binding JointOperationDisplayName}"
                        Header="接头操作显示" />
                </DataGrid.Columns>
            </DataGrid>
        </GroupBox>

        <!--  状态栏  -->
        <StatusBar Grid.Row="3" Height="25">
            <StatusBarItem>
                <TextBlock x:Name="TxtStatus" Text="就绪" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
