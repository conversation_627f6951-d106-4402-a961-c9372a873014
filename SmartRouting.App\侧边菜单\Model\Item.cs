﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.侧边菜单.Model
{
    public class Item
    {

        public string Type { get; set; }//类型，如结构 建筑等
        public string Des { get; set; }//描述
        public string Command { get; set; }//快捷建

        public string ToolTip { get; set; }//ToolTip

        public Items Children { get; set; }//快捷建

        public void SetChildrenDefaultValue(string type)
        {

            this.Children = new Items();

            var userCmds = Items.ReflectionArrtibute();

            foreach (var userCmd in userCmds)
            {
                Item item = new Item() { Type = userCmd.Type, Des = userCmd.Des, Command = userCmd.Command, ToolTip = userCmd.ToolTip };
                if (item.Type == type)
                {
                    this.Children.Add(item);
                }
            }
        }

    }

}
