﻿using SmartRouting.App.Commands.常用.扩展数据显示.View;
using SmartRouting.App.Commands.线路连接.View;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.常用.扩展数据显示
{
    public class ShowXData
    {
        [CommandTag("常    用", nameof(ReadXDataCmd), "读取扩展数据")]
        [CommandMethod(nameof(ReadXDataCmd))]
        public void ReadXDataCmd()
        {
            #region 判断是否过了使用期限
            if (GlobalSetting.SetDeadLine())
            {
                MessageBoxUtil.MessageInformation("软件已过使用期限或未联网，请联系开发者!");
                return;
            }
            #endregion
            using (Transaction tr = W.Trans)
            {
                try
                {
                    PromptEntityOptions opt = new PromptEntityOptions("\n选择要读取扩展数据的实体:");
                    PromptEntityResult res = W.Ed.GetEntity(opt);
                    if (res.Status != PromptStatus.OK) return;
                    Entity ent = tr.GetObject(res.ObjectId, OpenMode.ForRead) as Entity;
                    var xDataDict = XDataUtil.GetEntityXData(ent);

                    ////// 创建WPF窗口
                    //var dataWindow = new XDataWindow(xDataDict);
                    //Cad.ApplicationServices.Core.Application.ShowModelessWindow(dataWindow);
                    WindowManagerUtil.Show(() => new XDataWindow(xDataDict));//单例模式
                
                }
                catch (Exception ex)
                {
                    W.Ed.WriteMessage($"\n错误: {ex.Message}");
                }
                finally
                {
                    tr.Commit();
                }
            }
        }



      
    }
}
