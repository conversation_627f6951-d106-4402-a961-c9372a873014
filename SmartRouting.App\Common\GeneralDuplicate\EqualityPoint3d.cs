﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Common.GeneralDuplicate
{
    internal class EqualityPoint3d : IEqualityComparer<Point3d>
    {
        public bool Equals(Point3d x, Point3d y)
        {
            return x.SetZ(0).DistanceTo(y.SetZ(0)) <= 0.001;
        }
        public int GetHashCode(Point3d obj)
        {
            return 1;
        }
    }
}
