﻿global using SmartRouting.App.Utils;
global using SmartRouting.App.Extensions;
global using SmartRouting.App.Attributes;
global using SmartRouting.App.Common.GeneralDuplicate;
global using SmartRouting.App.MVVM;
global using SmartRouting.App.Utils.UserDefClass;
global using SmartRouting.App.Utils.Algorithms;
global using Exception = System.Exception;
global using Newtonsoft.Json;
global using System.Linq;
global using System;
global using System.IO;
global using System.Windows.Controls;

global using System.Collections.Generic;

#if AC12||AC13
global using Autodesk.AutoCAD.ApplicationServices;
global using Autodesk.AutoCAD.DatabaseServices;
global using Autodesk.AutoCAD.EditorInput;
global using Autodesk.AutoCAD.Geometry;
global using Autodesk.AutoCAD.Runtime;
global using CadApp = Autodesk.AutoCAD.ApplicationServices.Application;
global using Autodesk.AutoCAD.Colors;
global using CadDb = Autodesk.AutoCAD.DatabaseServices;
global using Cad = Autodesk.AutoCAD;
#elif ZW25
global using ZwSoft.ZwCAD.ApplicationServices;
global using ZwSoft.ZwCAD.DatabaseServices;
global using ZwSoft.ZwCAD.EditorInput;
global using ZwSoft.ZwCAD.Geometry;
global using ZwSoft.ZwCAD.Runtime;
global using CadApp = ZwSoft.ZwCAD.ApplicationServices.Application;
global using ZwSoft.ZwCAD.Colors;
global using CadDb = ZwSoft.ZwCAD.DatabaseServices;
global using Cad = ZwSoft.ZwCAD;
#endif