﻿using SmartRouting.App.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils.UserDefClass
{
    public class 线
    {
        public Line Line;
        public 节点 StartNode, EndNode;
        public double Weight;
        public 线(Line line)
        {
            this.Line = line;
            Weight = line.Length;
        }
        public 节点 GetOther(节点 one)
        {
            if (one == StartNode)
            {
                return EndNode;
            }
            else if (one == EndNode)
            {
                return StartNode;
            }
            else throw new System.Exception("节点不属于线");
        }

    }

}
