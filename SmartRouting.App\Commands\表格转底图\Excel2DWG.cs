﻿using SmartRouting.App.Commands.表格转底图.View;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.表格转底图
{
    public class Excel2DWG
    {
        [CommandTag("排    模", nameof(Excel2DWGCmd), "表格转图")]
        [CommandMethod(nameof(Excel2DWGCmd))]
        public void Excel2DWGCmd()
        {
            #region 判断是否过了使用期限
            if (GlobalSetting.SetDeadLine())
            {
                MessageBoxUtil.MessageInformation("软件已过使用期限或未联网，请联系开发者!");
                return;
            }
            #endregion
            Excel2DWGView win = new Excel2DWGView();
            CadApp.ShowModalWindow(win);
        }

    }
}
