﻿using SmartRouting.App.Commands.线路连接.View;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.线路连接
{
    public class LinesConnection
    {
        [CommandTag("排    模", nameof(LinesConnectionCmd), "线路连接")]
        [CommandMethod(nameof(LinesConnectionCmd))]
        public void LinesConnectionCmd()
        {
            #region 判断是否过了使用期限
            if (GlobalSetting.SetDeadLine())
            {
                MessageBoxUtil.MessageInformation("软件已过使用期限或未联网，请联系开发者!");
                return;
            }
            #endregion
            WindowManagerUtil.Show<JoinTotalLinesView>();//单例模式
        }
    }
}
