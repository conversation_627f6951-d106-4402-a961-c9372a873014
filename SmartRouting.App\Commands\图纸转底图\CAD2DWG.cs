﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.图纸转底图
{
    public class CAD2DWG
    {
        [CommandTag("排    模", nameof(CAD2DWGCmd), "图纸转图")]
        [CommandMethod(nameof(CAD2DWGCmd))]
        public void CAD2DWGCmd()
        {
            
            #region 判断是否过了使用期限
            if (GlobalSetting.SetDeadLine())
            {
                MessageBoxUtil.MessageInformation("软件已过使用期限或未联网，请联系开发者!");
                return;
            }
            #endregion

            //1. 先选择原始图纸的工井与管道，为工井匹配到一个合适的工井名称。再利用线处理功能，合并的情况
            var flag = SelectUtil.SelectAllEntities<Entity>();

         

        }

    }
}
