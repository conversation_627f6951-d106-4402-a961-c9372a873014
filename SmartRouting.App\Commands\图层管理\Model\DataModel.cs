﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.图层管理.Model
{
    public class DataModel : BindableBase
    {
        
        public string LayerName
        {
            get => GetProperty(() => LayerName);
            set => SetProperty(() => LayerName, value);
        }

        public int ColorIndex
        {
            get => GetProperty(() => ColorIndex);
            set => SetProperty(() => ColorIndex, value);
        }

        public string LineStyle
        {
            get => GetProperty(() => LineStyle);
            set => SetProperty(() => LineStyle, value);
        }

        public int LineScale
        {
            get => GetProperty(() => LineScale);
            set => SetProperty(() => LineScale, value);
        }

        public bool IsFunc
        {
            get => GetProperty(() => IsFunc);
            set => SetProperty(() => IsFunc, value);
        }
    }
}
