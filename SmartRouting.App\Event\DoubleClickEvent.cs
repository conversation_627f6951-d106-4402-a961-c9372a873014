﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Runtime.Remoting;
using System.Windows.Documents;
using System.Windows.Input;
using Autodesk.AutoCAD.DatabaseServices;
using SmartRouting.App;
using SmartRouting.App.Commands.常用.AI助手.View;
using SmartRouting.App.Commands.常用.扩展数据显示.View;
using SmartRouting.App.Commands.数据录入.Model;
using SmartRouting.App.Commands.数据录入.View;
using SmartRouting.App.Utils;
using SmartRouting.App.Utils.XRecordUtil;


public class DoubleClickEvent
{
    #region Fields and Properties

    // 将所有字段和属性改为静态，以确保它们在多次加载后保持唯一性
    private static List<string> breakBlocks = new List<string> { "非普", "人孔1", "人孔2", "手孔1", "手孔2", "接线箱2", "上杆" };
    private static List<string> pipeLineLayers = new List<string> { "管_信息", "管_标志", "管_线" };
    private static bool _shouldVetoNextCommand = false;
    public static ObjectId m_ObjectId { get; set; }
    public static Point3d m_LastSelectPoint { get; set; }

    // 添加标志位来跟踪事件是否已经注册
    private static bool _eventsRegistered = false;
    private static readonly object _lockObject = new object();

    // 添加防重复执行的机制
    private static DateTime _lastEventTime = DateTime.MinValue;
    private static ObjectId _lastObjectId = ObjectId.Null;
    private static int _eventCounter = 0;

    // 添加唯一标识符，确保只有当前活跃的处理程序才能执行
    private static string _currentHandlerId = "";
    private static readonly Dictionary<string, bool> _activeHandlers = new Dictionary<string, bool>();

    // 添加窗口显示状态跟踪
    private static bool _windowAlreadyShown = false;

    [DllImport("USER32.DLL")]
    public static extern void keybd_event(byte bVk, byte bScan, int dwFlags, int dwExtraInfo);

    #endregion

    #region Helper Methods

    /// <summary>
    /// 安全地向AutoCAD命令行输出调试信息
    /// </summary>
    private static void WriteDebugMessage(string message)
    {
        try
        {
            var doc = CadApp.DocumentManager.MdiActiveDocument;
            if (doc?.Editor != null)
            {
                doc.Editor.WriteMessage($"\n[DoubleClickEvent] {message} - {DateTime.Now:HH:mm:ss}");
            }
        }
        catch
        {
            // 如果无法输出到编辑器，就静默忽略
        }
    }

    /// <summary>
    /// 生成新的处理程序标识符
    /// </summary>
    private static string GenerateNewHandlerId()
    {
        return $"Handler_{DateTime.Now.Ticks}_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// 注册双击事件监听。
    /// 此方法现在是幂等的：可以安全地多次调用，而不会重复注册。
    /// </summary>
    public static void AddDoubleClickEvent()
    {
        lock (_lockObject)
        {
            try
            {
                // 生成新的处理程序ID
                var newHandlerId = GenerateNewHandlerId();

                // 停用所有旧的处理程序
                foreach (var key in new List<string>(_activeHandlers.Keys))
                {
                    _activeHandlers[key] = false;
                }
                _activeHandlers.Clear();

                // 激活新的处理程序
                _currentHandlerId = newHandlerId;
                _activeHandlers[newHandlerId] = true;

                // 彻底移除旧的事件处理程序
                RemoveDoubleClickEventInternal();

                // 注册事件处理程序
                CadApp.DocumentManager.DocumentLockModeChanged += DocumentManager_DocumentLockModeChanged;
                CadApp.BeginDoubleClick += Application_BeginDoubleClick;
                _eventsRegistered = true;

                // 重置计数器
                _eventCounter = 0;
                _lastEventTime = DateTime.MinValue;
                _lastObjectId = ObjectId.Null;

                // 重置窗口显示状态
                _windowAlreadyShown = false;

                // 调试信息：记录事件注册
                //  WriteDebugMessage($"事件已注册，处理程序ID: {newHandlerId}");
            }
            catch (Exception ex)
            {
                WriteDebugMessage($"注册事件时出错: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 注销双击事件监听。
    /// </summary>
    public static void RemoveDoubleClickEvent()
    {
        lock (_lockObject)
        {
            // 停用当前处理程序
            if (!string.IsNullOrEmpty(_currentHandlerId) && _activeHandlers.ContainsKey(_currentHandlerId))
            {
                _activeHandlers[_currentHandlerId] = false;
                //  WriteDebugMessage($"停用处理程序: {_currentHandlerId}");
            }

            RemoveDoubleClickEventInternal();
        }
    }

    /// <summary>
    /// 内部的事件移除方法
    /// </summary>
    private static void RemoveDoubleClickEventInternal()
    {
        try
        {
            // 移除事件处理程序 - 多次尝试确保移除
            for (int i = 0; i < 5; i++)
            {
                CadApp.DocumentManager.DocumentLockModeChanged -= DocumentManager_DocumentLockModeChanged;
                CadApp.BeginDoubleClick -= Application_BeginDoubleClick;
            }
            _eventsRegistered = false;

            // 调试信息：记录事件移除
            // WriteDebugMessage("事件已移除");
        }
        catch (Exception ex)
        {
            //  WriteDebugMessage($"移除事件时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查当前处理程序是否处于活跃状态
    /// </summary>
    private static bool IsCurrentHandlerActive()
    {
        if (string.IsNullOrEmpty(_currentHandlerId))
        {
            // WriteDebugMessage("处理程序ID为空，跳过执行");
            return false;
        }

        if (!_activeHandlers.ContainsKey(_currentHandlerId) || !_activeHandlers[_currentHandlerId])
        {
            //  WriteDebugMessage($"处理程序 {_currentHandlerId} 已停用，跳过执行");
            return false;
        }

        return true;
    }

    #endregion

    #region Event Handlers

    // 事件处理程序也必须是静态的
    private static void Application_BeginDoubleClick(object sender, BeginDoubleClickEventArgs e)
    {
        lock (_lockObject)
        {
            try
            {
                // 首先检查当前处理程序是否活跃
                if (!IsCurrentHandlerActive())
                {
                    return;
                }
                _eventCounter++;
                var now = DateTime.Now;
                // 防重复执行检查：如果是同一个对象在很短时间内再次触发，则忽略
                var timeDiff = now - _lastEventTime;
                if (timeDiff.TotalMilliseconds < 500) // 500毫秒内的重复事件忽略
                {
                    return;
                }

                _lastEventTime = now;
                _shouldVetoNextCommand = false;

                PromptSelectionResult res = W.Ed.SelectImplied();
                if (res.Status != PromptStatus.OK || res.Value.Count != 1)
                {
                    return;
                }

                ObjectId objectId = res.Value.GetObjectIds()[0];
                Point3d wcsDoubleClickPoint = e.Location;
                m_LastSelectPoint = wcsDoubleClickPoint;

                // 如果是同一个对象，也忽略
                if (objectId == _lastObjectId && timeDiff.TotalMilliseconds < 1000)
                {
                    //  WriteDebugMessage("忽略同一对象的重复事件");
                    return;
                }

                _lastObjectId = objectId;
                m_ObjectId = objectId;


                using (var trans = W.Trans)
                {
                    if (trans.GetObject(objectId, OpenMode.ForRead) is BlockReference reference)
                    {
                        string userVisibleBlockName = reference.IsDynamicBlock
                            ? (trans.GetObject(reference.DynamicBlockTableRecord, OpenMode.ForRead) as BlockTableRecord)?.Name
                            : reference.Name;
                        if (!string.IsNullOrEmpty(userVisibleBlockName) && breakBlocks.Contains(userVisibleBlockName))
                        {
                            _shouldVetoNextCommand = true;
                            //  WriteDebugMessage($"设置拦截标志 for 块: {userVisibleBlockName}");
                            // 重置窗口显示状态，确保在Veto事件中显示
                            _windowAlreadyShown = false;
                        }
                    }
                    else if (trans.GetObject(objectId, OpenMode.ForRead) is Polyline polyline)
                    {


                        if (pipeLineLayers.Contains(polyline.Layer))
                        {
                            _shouldVetoNextCommand = true;
                            //  WriteDebugMessage($"设置拦截标志 for 块: {userVisibleBlockName}");
                            // 重置窗口显示状态，确保在Veto事件中显示
                            _windowAlreadyShown = false;
                        }

                    }


                    trans.Commit();
                }
            }
            catch (Exception ex)
            {
                WriteDebugMessage($"BeginDoubleClick处理时出错: {ex.Message}");
            }
        }
    }

    private static void DocumentManager_DocumentLockModeChanged(object sender, DocumentLockModeChangedEventArgs e)
    {
        SelectEntityEnum selectEntityEnum = SelectEntityEnum.未知;
        lock (_lockObject)
        {
            try
            {
                // 首先检查当前处理程序是否活跃
                if (!IsCurrentHandlerActive())
                {
                    return;
                }

                // 开启一个贯穿整个方法生命周期的事务
                using (var tr = W.Trans)
                {
                    string commandName = e.GlobalCommandName.ToUpperInvariant();
                    string recordNameForWell = typeof(WellModel).Name;
                    string recordNameForPipe = typeof(PipelineModel).Name;
                    var wellsWithXRecords = ExtensionDictionaryManager.FindEntitiesWithExtensionDictionary<BlockReference>(tr, recordNameForWell);//所有带扩展字典的工井块
                    var pipesWithXRecords = ExtensionDictionaryManager.FindEntitiesWithExtensionDictionary<Polyline>(tr, recordNameForPipe);//所有带扩展字典的管道
                    string currnetMaxNum = CustomDataStorage.GetCustomData("MaxNumber");//得到当前全局的最大编号
                    if (tr.GetObject(m_ObjectId, OpenMode.ForRead) is BlockReference reference)
                    {
                        var data = m_ObjectId.GetObjXrecord(tr, recordNameForWell);
                        if (data != null)
                        {
                            selectEntityEnum = SelectEntityEnum.工井;
                        }

                    }
                    else if (tr.GetObject(m_ObjectId, OpenMode.ForRead) is Polyline polyline)
                    {
                        var data = m_ObjectId.GetObjXrecord(tr, recordNameForPipe);

                        if (data != null)
                        {

                            selectEntityEnum = SelectEntityEnum.管道;
                        }
                    }



                    if (selectEntityEnum == SelectEntityEnum.管道)
                    {

                        var polyline = tr.GetObject(m_ObjectId, OpenMode.ForRead) as Polyline;
                        var pipeData = m_ObjectId.GetObjXrecord(tr, recordNameForPipe);//当前管道的扩展信息
                        PipelineModel model = GenericExtensionDictionaryManager.TypeValueListToObject<PipelineModel>(pipeData);
                        WellModel fromWell = null;
                        WellModel toWell = null;
                        PipelineModel pipelineModel = null;
                        if (model != null && model.CableModels != null && model.CableModels.Count > 0)
                        {
                            Point3d p1 = polyline.StartPoint.SetZ(0);//管道线的起点
                            Point3d p2 = polyline.EndPoint.SetZ(0);//管道线的终点

                            foreach (BlockReference well in wellsWithXRecords)
                            {
                                var wellPosition = well.Position.SetZ(0);//工井的位置
                                if (wellPosition.DistanceTo(p1) <= 0.0001 || wellPosition.DistanceTo(p2) <= 0.0001)
                                {
                                    var wellData = well.Id.GetObjXrecord(tr, recordNameForWell);//当前管道连接的工井的扩展信息
                                    var wellModel = GenericExtensionDictionaryManager.TypeValueListToObject<WellModel>(wellData);
                                    //  这里留下了有个问题，就是当一个管道2端的工井都有数据的时候，这时需要利用工井的分配属性来判断了。
                                    if (wellModel != null)
                                    {
                                        if (wellModel.CableModels != null && wellModel.CableModels.Count > 0)
                                        {
                                            fromWell = wellModel;//这个管道的线缆来自这个工井
                                        }
                                        else
                                        {
                                            toWell = wellModel;//这个管道的线缆去往这个工井
                                        }

                                    }

                                }
                            }

                            //当前管道本身有数据
                            pipelineModel = model;
                            pipelineModel.FromWell = fromWell;
                            pipelineModel.ToWell = toWell;
                        }

                        else
                        {

                          //  当前管道本身没有数据
                            Point3d p1 = polyline.StartPoint.SetZ(0);//管道线的起点
                            Point3d p2 = polyline.EndPoint.SetZ(0);//管道线的终点

                            foreach (BlockReference well in wellsWithXRecords)
                            {
                                var wellPosition = well.Position.SetZ(0);//工井的位置
                                if (wellPosition.DistanceTo(p1) <= 0.0001 || wellPosition.DistanceTo(p2) <= 0.0001)
                                {
                                    var wellData = well.Id.GetObjXrecord(tr, recordNameForWell);//当前管道连接的工井的扩展信息
                                    var wellModel = GenericExtensionDictionaryManager.TypeValueListToObject<WellModel>(wellData);
                                  //  这里留下了有个问题，就是当一个管道2端的工井都有数据的时候，这时需要利用工井的分配属性来判断了。
                                    if (wellModel != null)
                                    {
                                        if (wellModel.CableModels != null && wellModel.CableModels.Count > 0)
                                        {
                                            fromWell = wellModel;//这个管道的线缆来自这个工井
                                        }
                                        else
                                        {
                                            toWell = wellModel;//这个管道的线缆去往这个工井
                                        }

                                    }

                                }
                            }


                        }




                        if (_shouldVetoNextCommand && (commandName == "PEDIT" || commandName == "EATTEDIT" || commandName == "PROPERTIES"))
                        {
                            e.Veto();
                            _shouldVetoNextCommand = false;
                            W.Ed.SetImpliedSelection(new ObjectId[0]);

                            // 只有当窗口还没有显示过时才显示
                            if (!_windowAlreadyShown)
                            {
                                _windowAlreadyShown = true;
                                if (pipelineModel != null)
                                {

                                    // 显示窗口(显示已经存在的数据)
                                    WindowManagerUtil.Show(() => new PipelineView(m_ObjectId, pipelineModel, m_LastSelectPoint));//单例模式
                                }

                                else
                                {
                               
                                    if (fromWell != null && toWell != null)
                                    {
                                        //有来自的工井，有去往的工井。
                                        WindowManagerUtil.Show(() => new PipelineView(m_ObjectId, fromWell, toWell, m_LastSelectPoint));//单例模式
                                    }

                                    if (toWell == null && fromWell == null)
                                    {
                                        //没来自的工井且没去往的工井
                                        MessageBoxUtil.Message_Error("请检查当前管道两端是否连接已埋入数据的工井!");
                                        return;
                                    }




                                }

                            }
                            ClearCommandLine();
                        }
                    }
                    if (selectEntityEnum == SelectEntityEnum.工井)
                    {
                        var flag = true;
                        List<PipelineModel> neaerPipesToWell = new List<PipelineModel>();//与当前工井相关联的管道(这个工井的前一个管道，管道的线传入这个工井)
                        WellModel wellModel = null;
                        //1.0 先判断当前的工井有没有线缆信息，如果有，则显示自身的数据到窗口。没有的话，就读取与它连接的管道，如果是起始的就给个默认值，不是起始的就读取管道

                        var wellData = m_ObjectId.GetObjXrecord(tr, recordNameForWell);//当前工井的扩展信息
                        var wellModelData = GenericExtensionDictionaryManager.TypeValueListToObject<WellModel>(wellData);
                        var pipeModel = ExtensionDictionaryManager.FindFirstPipelineToWellName(wellModelData.WellName);//读取ToWell属性
                        if (wellModelData != null && wellModelData.CableModels != null && wellModelData.CableModels.Count > 0)
                        {
                            //当前工井本身有数据
                            wellModel = wellModelData;
                        }

                        else
                        {
                            //当前工井没数据，需要找到连接它的管道
                         
                            if (pipeModel != null)
                            {
                                //针对不是起始工井的情况
                                neaerPipesToWell.Add(pipeModel);
                            }
                            else
                            {
                                //没有指向这个工井的管道。这里有3种情况：1.这个工井是起始工井，2.这个工井是一个孤立的工井，3.这个工井的两端的管道均无数据。
                                Point3d wellPosition = new Point3d();
                                if (tr.GetObject(m_ObjectId, OpenMode.ForRead) is BlockReference reference2) wellPosition = reference2.Position.SetZ(0);
                                List<Polyline> plines = new List<Polyline>();
                                foreach (Polyline pipe in pipesWithXRecords)
                                {
                                    var startPoint = pipe.StartPoint.SetZ(0);//管道的起点
                                    var endPoint = pipe.EndPoint.SetZ(0);//管道的终点
                                    if (wellPosition.DistanceTo(startPoint) <= 0.0001 || wellPosition.DistanceTo(endPoint) <= 0.0001) plines.Add(pipe);
                                }

                                if (plines.Count>1||plines.Count==0)
                                {
                                    flag = false;
                                    return;
                                }
                            }
                        }


                        if (_shouldVetoNextCommand && (commandName == "BEDIT" || commandName == "EATTEDIT" || commandName == "PROPERTIES"))
                        {

                            e.Veto();
                            _shouldVetoNextCommand = false;

                            W.Ed.SetImpliedSelection(new ObjectId[0]);

                            // 只有当窗口还没有显示过时才显示
                            if (!_windowAlreadyShown)
                            {
                                _windowAlreadyShown = true;
                                if (wellModel != null)
                                {
                                    //var cables = wellModel.CableModels;
                                    //if (pipeModel!=null)
                                    //{
                                    //    foreach (var item in cables)
                                    //    {
                                    //        item.CableFrom = pipeModel.PipelineNumber;
                                    //    }
                                    //}
                                  
                                    
                                    WindowManagerUtil.Show(() => new CableEntryView(m_ObjectId, wellModel, currnetMaxNum.StringToInt()));
                                }
                                else
                                {
                                    if (neaerPipesToWell==null|| neaerPipesToWell.Count== 0&& flag)
                                    {
                                    
                                        //这种一般是端头的工井,同时它也没什么数据
                                        TypedValueList typeValueList = m_ObjectId.GetObjXrecord(tr, recordNameForWell);
                                        if (typeValueList != null)
                                        {
                                            var model = GenericExtensionDictionaryManager.TypeValueListToObject<WellModel>(typeValueList);
                                            if (model != null)
                                            {
                                                WindowManagerUtil.Show(() => new CableEntryView(m_ObjectId, model, currnetMaxNum.StringToInt()));
                                            }
                                        }

                                    }
                                    else
                                    {
                                     
                                        //如果明确存在指向了这个工井的管道，就只读取这个管道的信息
                                        WindowManagerUtil.Show(() => new CableEntryView(m_ObjectId, wellModelData, neaerPipesToWell, currnetMaxNum.StringToInt()));

                                    }

                                }

                            }
                            ClearCommandLine();

                        }
                    }

                    tr.Commit();
                }








            }
            catch (Exception ex)
            {
                //  WriteDebugMessage($"DocumentLockModeChanged处理时出错: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 清理命令行显示
    /// </summary>
    private static void ClearCommandLine()
    {
        try
        {
            var doc = CadApp.DocumentManager.MdiActiveDocument;
            if (doc?.Editor != null)
            {
                // 立即写入覆盖信息
                doc.Editor.WriteMessage("\r命令: ");

                // 使用异步进一步清理
                System.Threading.Tasks.Task.Run(async () =>
                {
                    await System.Threading.Tasks.Task.Delay(100);

                    try
                    {
                        // 发送一个简单的命令来刷新命令行状态
                        doc.SendStringToExecute("", true, false, false);
                        await System.Threading.Tasks.Task.Delay(50);
                        doc.Editor.WriteMessage("\r命令: ");
                    }
                    catch
                    {
                        // 如果失败，尝试ESC键
                        try
                        {
                            keybd_event(0x1B, 0, 0, 0);
                            keybd_event(0x1B, 0, 2, 0);
                        }
                        catch { }
                    }
                });
            }
        }
        catch
        {
            // 如果清理失败，忽略错误
        }
    }

    #endregion
}
public enum SelectEntityEnum
{
    未知,
    管道,
    工井



}
