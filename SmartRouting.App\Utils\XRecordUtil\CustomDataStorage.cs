﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils.XRecordUtil
{
    public static class CustomDataStorage
    {
        private const string DictName = "MyApp_GlobalData";

        /// <summary>
        /// 设置全局自定义数据
        /// </summary>
        public static bool SetCustomData(string key, string value)
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var db = doc.Database;

            using (var trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var nod = trans.GetObject(db.NamedObjectsDictionaryId, OpenMode.ForWrite) as DBDictionary;

                    DBDictionary appDict;
                    if (nod.Contains(DictName))
                    {
                        appDict = trans.GetObject(nod.GetAt(DictName), OpenMode.ForWrite) as DBDictionary;
                    }
                    else
                    {
                        appDict = new DBDictionary();
                        nod.SetAt(DictName, appDict);
                        trans.AddNewlyCreatedDBObject(appDict, true);
                    }

                    // 创建或更新Xrecord
                    if (appDict.Contains(key))
                    {
                        var existingXrec = trans.GetObject(appDict.GetAt(key), OpenMode.ForWrite) as Xrecord;
                        existingXrec.Data = new ResultBuffer(new TypedValue(1, value ?? ""));
                    }
                    else
                    {
                        var xrec = new Xrecord();
                        xrec.Data = new ResultBuffer(new TypedValue(1, value ?? ""));
                        appDict.SetAt(key, xrec);
                        trans.AddNewlyCreatedDBObject(xrec, true);
                    }

                    trans.Commit();
                    return true;
                }
                catch
                {
                    trans.Abort();
                    return false;
                }
            }
        }

        /// <summary>
        /// 获取全局自定义数据
        /// </summary>
        public static string GetCustomData(string key)
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var db = doc.Database;

            using (var trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var nod = trans.GetObject(db.NamedObjectsDictionaryId, OpenMode.ForRead) as DBDictionary;

                    if (!nod.Contains(DictName))
                    {
                        trans.Commit();
                        return null;
                    }

                    var appDict = trans.GetObject(nod.GetAt(DictName), OpenMode.ForRead) as DBDictionary;
                    if (!appDict.Contains(key))
                    {
                        trans.Commit();
                        return null;
                    }

                    var xrec = trans.GetObject(appDict.GetAt(key), OpenMode.ForRead) as Xrecord;
                    var data = xrec.Data;

                    if (data != null)
                    {
                        foreach (TypedValue tv in data)
                        {
                            if (tv.TypeCode == 1) // 字符串类型
                            {
                                trans.Commit();
                                return tv.Value.ToString();
                            }
                        }
                    }

                    trans.Commit();
                    return null;
                }
                catch
                {
                    trans.Abort();
                    return null;
                }
            }
        }

        /// <summary>
        /// 删除指定的全局数据
        /// </summary>
        public static bool RemoveCustomData(string key)
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var db = doc.Database;

            using (var trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var nod = trans.GetObject(db.NamedObjectsDictionaryId, OpenMode.ForRead) as DBDictionary;

                    if (!nod.Contains(DictName))
                    {
                        trans.Commit();
                        return false;
                    }

                    var appDict = trans.GetObject(nod.GetAt(DictName), OpenMode.ForWrite) as DBDictionary;
                    if (!appDict.Contains(key))
                    {
                        trans.Commit();
                        return false;
                    }

                    appDict.Remove(key);
                    trans.Commit();
                    return true;
                }
                catch
                {
                    trans.Abort();
                    return false;
                }
            }
        }

        /// <summary>
        /// 获取所有全局数据
        /// </summary>
        public static Dictionary<string, string> GetAllCustomData()
        {
            var result = new Dictionary<string, string>();
            var doc = Application.DocumentManager.MdiActiveDocument;
            var db = doc.Database;

            using (var trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var nod = trans.GetObject(db.NamedObjectsDictionaryId, OpenMode.ForRead) as DBDictionary;

                    if (!nod.Contains(DictName))
                    {
                        trans.Commit();
                        return result;
                    }

                    var appDict = trans.GetObject(nod.GetAt(DictName), OpenMode.ForRead) as DBDictionary;

                    // 遍历所有数据
                    foreach (DBDictionaryEntry entry in appDict)
                    {
                        var xrec = trans.GetObject(entry.Value, OpenMode.ForRead) as Xrecord;
                        var data = xrec.Data;

                        if (data != null)
                        {
                            foreach (TypedValue tv in data)
                            {
                                if (tv.TypeCode == 1) // 字符串类型
                                {
                                    result[entry.Key] = tv.Value.ToString();
                                    break;
                                }
                            }
                        }
                    }

                    trans.Commit();
                }
                catch
                {
                    trans.Abort();
                }
            }

            return result;
        }

        /// <summary>
        /// 检查指定键是否存在
        /// </summary>
        public static bool HasCustomData(string key)
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var db = doc.Database;

            using (var trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var nod = trans.GetObject(db.NamedObjectsDictionaryId, OpenMode.ForRead) as DBDictionary;

                    if (!nod.Contains(DictName))
                    {
                        trans.Commit();
                        return false;
                    }

                    var appDict = trans.GetObject(nod.GetAt(DictName), OpenMode.ForRead) as DBDictionary;
                    bool exists = appDict.Contains(key);
                    trans.Commit();
                    return exists;
                }
                catch
                {
                    trans.Abort();
                    return false;
                }
            }
        }

        /// <summary>
        /// 清空所有全局数据
        /// </summary>
        public static bool ClearAllCustomData()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var db = doc.Database;

            using (var trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var nod = trans.GetObject(db.NamedObjectsDictionaryId, OpenMode.ForWrite) as DBDictionary;

                    if (nod.Contains(DictName))
                    {
                        nod.Remove(DictName);
                    }

                    trans.Commit();
                    return true;
                }
                catch
                {
                    trans.Abort();
                    return false;
                }
            }
        }
    }
}
