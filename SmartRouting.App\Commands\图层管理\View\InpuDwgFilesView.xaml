﻿<Window
    x:Class="SmartRouting.App.Commands.图层管理.View.InpuDwgFilesView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SmartRouting.App.Commands.图层管理.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="DWG导入"
    Width="auto"
    Height="AUTO"
    MinWidth="300"
    MinHeight="50"
    MaxHeight="600"
    ResizeMode="NoResize"
    ShowInTaskbar="True"
    SizeToContent="WidthAndHeight"
    SnapsToDevicePixels="True"
    Topmost="True"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/SmartRouting.App;component/Common/WPFUI/CommonUIStyle.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <GroupBox Margin="2" Header="参数信息">
        <Grid Margin="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="AUTO" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Button
                x:Name="Btn_OpenDWG"
                Grid.Row="0"
                Grid.Column="0"
                Width="AUTO"
                HorizontalAlignment="Stretch"
                Click="Btn_OpenDWG_Click"
                Content="选择DWG文件" />
            <DockPanel Grid.ColumnSpan="2" HorizontalAlignment="Right">
                <Button
                    x:Name="Btn_Creation"
                    HorizontalAlignment="Right"
                    Click="Btn_Creation_Click"
                    Content="生成" />
                <Button
                    x:Name="Btn_Setting"
                    HorizontalAlignment="Right"
                    Click="Btn_Setting_Click"
                    Content="设置" />

            </DockPanel>



        </Grid>
    </GroupBox>

</Window>
