﻿<Window
    x:Class="SmartRouting.App.Commands.数据录入.View.PipelineView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:SmartRouting.App.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SmartRouting.App.Commands.数据录入.View;assembly=SmartRouting.App"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:SmartRouting.App.Commands.数据录入.Model;assembly=SmartRouting.App"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    Title="管道信息录入"
    Background="#FFFFFF"
    ResizeMode="NoResize"
    SizeToContent="WidthAndHeight"
    Topmost="True"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <Window.Resources>
        <converters:EnumToBooleanConverter x:Key="EnumToBooleanConverter" />
        <Style TargetType="Label">
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Foreground" Value="#333" />
            <Setter Property="Background" Value="#F5F5F5" />
            <Setter Property="Padding" Value="10,8" />
            <Setter Property="BorderBrush" Value="#D6D6D6" />
            <Setter Property="BorderThickness" Value="0,0,1,0" />
        </Style>

        <Style TargetType="Border">
            <Setter Property="BorderBrush" Value="#D6D6D6" />
            <Setter Property="BorderThickness" Value="0,0,0,1" />
        </Style>

        <Style TargetType="TextBox">
            <Setter Property="Padding" Value="5" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Setter Property="BorderThickness" Value="0,0,1,0" />
            <Setter Property="BorderBrush" Value="#D6D6D6" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Background" Value="Transparent" />
        </Style>

        <Style TargetType="ComboBox">
            <Setter Property="Padding" Value="5,3" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Setter Property="BorderThickness" Value="0,0,1,0" />
            <Setter Property="BorderBrush" Value="#D6D6D6" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Background" Value="Transparent" />
        </Style>
        <Style
            x:Key="ComboBoxDataGridCellStyle"
            BasedOn="{StaticResource {x:Type DataGridCell}}"
            TargetType="DataGridCell">
            <Setter Property="Padding" Value="0" />
            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        </Style>
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4A90E2" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="12,6" />
            <Setter Property="MinWidth" Value="70" />
            <Setter Property="Margin" Value="5,0" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border
                            x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="3"
                            SnapsToDevicePixels="True">
                            <ContentPresenter
                                Margin="{TemplateBinding Padding}"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                RecognizesAccessKey="True" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#357ABD" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#2A5C9A" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.7" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    <StackPanel>
        <DockPanel>
            <GroupBox Header="管道信息">
                <Grid Margin="5" Grid.IsSharedSizeScope="True">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" BorderThickness="1,1,1,0">
                        <Grid x:Name="HeaderGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="100" SharedSizeGroup="Col1" />
                                <ColumnDefinition Width="100" SharedSizeGroup="Col2" />
                                <ColumnDefinition Width="100" SharedSizeGroup="Col3" />
                                <ColumnDefinition Width="100" SharedSizeGroup="Col4" />
                                <ColumnDefinition Width="150" SharedSizeGroup="Col5" />
                            </Grid.ColumnDefinitions>
                            <TextBlock
                                Grid.Column="0"
                                HorizontalAlignment="Center"
                                Text="管道编号" />
                            <TextBlock
                                Grid.Column="1"
                                HorizontalAlignment="Center"
                                Text="权属" />
                            <TextBlock
                                Grid.Column="2"
                                HorizontalAlignment="Center"
                                Text="材质" />
                            <TextBlock
                                Grid.Column="3"
                                HorizontalAlignment="Center"
                                Text="孔数" />
                            <TextBlock
                                Grid.Column="4"
                                HorizontalAlignment="Center"
                                Text="备注" />
                        </Grid>
                    </Border>

                    <Border Grid.Row="1" BorderThickness="1,0,1,1">
                        <Grid x:Name="DataEntryGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition SharedSizeGroup="Col1" />
                                <ColumnDefinition SharedSizeGroup="Col2" />
                                <ColumnDefinition SharedSizeGroup="Col3" />
                                <ColumnDefinition SharedSizeGroup="Col4" />
                                <ColumnDefinition SharedSizeGroup="Col5" />
                            </Grid.ColumnDefinitions>
                            <TextBox
                                x:Name="Tb_PipelineNumberTextBox"
                                Grid.Column="0"
                                FontSize="14"
                                IsReadOnly="True"
                                Text="{Binding PipelineNumber}" />
                            <ComboBox
                                x:Name="Cb_Ownership"
                                Grid.Column="1"
                                ItemsSource="{Binding TelecomOperatorEnumValues, RelativeSource={RelativeSource AncestorType=Window}}"
                                SelectedItem="{Binding TelecomOperatorEnum}" />
                            <ComboBox
                                x:Name="Cb_Material"
                                Grid.Column="2"
                                ItemsSource="{Binding MaterialEnumValues, RelativeSource={RelativeSource AncestorType=Window}}"
                                SelectedItem="{Binding MaterialEnum}" />
                            <TextBox
                                x:Name="Tb_HoleCountTextBox"
                                Grid.Column="3"
                                IsEnabled="True"
                                Text="{Binding HoleCount}" />
                            <!--  ItemsSource="{Binding DataContext.HoleCountOptions, RelativeSource={RelativeSource AncestorType=Window}}"  -->

                            <TextBox
                                x:Name="Tb_RemarksTextBox"
                                Grid.Column="4"
                                BorderThickness="0"
                                Text="{Binding Remarks}" />
                        </Grid>
                    </Border>
                </Grid>

            </GroupBox>
        </DockPanel>
        <DockPanel>
            <GroupBox Margin="0,10,0,0" Header="线缆信息">
                <Grid Margin="5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="auto" />
                    </Grid.RowDefinitions>
                    <DataGrid
                        x:Name="CablesGrid"
                        Grid.Row="0"
                        AutoGenerateColumns="False"
                        HorizontalScrollBarVisibility="Auto"
                        IsReadOnly="True"
                        ItemsSource="{Binding CableModels}"
                        VerticalScrollBarVisibility="Auto">
                        <DataGrid.Columns>
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding CableEntryNumber}"
                                Header="编号" />
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding CableEntryEnum}"
                                Header="类型" />
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding Specification}"
                                Header="规格" />
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding TelecomOperatorEnum}"
                                Header="权属" />
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding Remarks}"
                                Header="备注" />
                            <DataGridTemplateColumn Width="50" Header="操作">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <CheckBox
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                    <Grid Grid.Row="1" Margin="0,10,0,0">
                        <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                            <Button
                                x:Name="Btn_ImportData"
                                VerticalAlignment="Center"
                                Click="Btn_ImportData_Click"
                                Content="分配线缆"
                                Style="{StaticResource PrimaryButtonStyle}" />

                            <Button
                                x:Name="Btn_ReImportData"
                                VerticalAlignment="Center"
                                Click="Btn_ReImportData_Click"
                                Content="重置"
                                Style="{StaticResource PrimaryButtonStyle}" />
                            <Button
                                x:Name="Btn_Cancel"
                                VerticalAlignment="Center"
                                Click="Btn_Cancel_Click"
                                Content="取消"
                                Style="{StaticResource PrimaryButtonStyle}" />
                        </StackPanel>
                    </Grid>
                </Grid>
            </GroupBox>

        </DockPanel>

    </StackPanel>

</Window> 