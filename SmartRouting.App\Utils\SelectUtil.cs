﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    public class SelectUtil
    {
        /// <summary>
        /// 返回指定类型的List<T>
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="message"></param>
        /// <returns></returns>
        public static bool SelectEntities<T>(out List<T> result, SelectionFilter selectionFilter = null, string message = "选择图形") where T : Entity
        {
            result = new List<T>();
            Editor ed = W.Ed;
            PromptSelectionOptions pso = new PromptSelectionOptions();
            Database db = HostApplicationServices.WorkingDatabase;
            pso.MessageForAdding = "\n" + message;
            PromptSelectionResult psr = ed.GetSelection(pso, selectionFilter);
            if (psr.Status == PromptStatus.OK)
            {
                ObjectId[] ids = psr.Value.GetObjectIds();
                using (Transaction tran = db.TransactionManager.StartTransaction())
                {
                    for (int i = 0; i < ids.Length; i++)
                    {
                        Entity entity = (Entity)ids[i].GetObject(OpenMode.ForRead);
                        if (entity is T e)
                        {

                            result.Add(e);

                        }
                    }
                    tran.Commit();
                }

            }
            return result.Count() > 0;
        }

        public static bool SelectCrossingWindow<T>(Point3d p1, Point3d p2, out List<T> result) where T : Entity
        {
            result = new List<T>();
            Editor editor = W.Ed;
            PromptSelectionResult psr = editor.SelectCrossingWindow(p1, p2);
            if (psr.Status == PromptStatus.OK)
            {
                ObjectId[] objectids = psr.Value.GetObjectIds();
                Database database = HostApplicationServices.WorkingDatabase;
                using (Transaction tran = database.TransactionManager.StartTransaction())
                {
                    foreach (var item in objectids)
                    {
                        Entity entity = (Entity)item.GetObject(OpenMode.ForRead);
                        if (entity is T)
                        {
                            result.Add(entity as T);
                        }

                    }
                }
            }
            return result.Count() > 0;
        }



        public static bool SelectWindow<T>(Point3d p1, Point3d p2, out List<T> result) where T : Entity
        {
            result = new List<T>();
            Editor editor = W.Ed;
            PromptSelectionResult psr = editor.SelectWindow(p1, p2);
            if (psr.Status == PromptStatus.OK)
            {
                ObjectId[] objectids = psr.Value.GetObjectIds();
                Database database = HostApplicationServices.WorkingDatabase;
                using (Transaction tran = database.TransactionManager.StartTransaction())
                {
                    foreach (var item in objectids)
                    {
                        Entity entity = (Entity)item.GetObject(OpenMode.ForRead);
                        if (entity is T)
                        {
                            result.Add(entity as T);
                        }

                    }
                }
            }
            return result.Count() > 0;
        }







        public static bool SelectFence<T>(List<Point3d> points, out List<T> result) where T : Entity
        {
            result = new List<T>();
            Point3dCollection pos = new Point3dCollection();
            points.ForEach(x => pos.Add(x));
            PromptSelectionResult psr = W.Ed.SelectFence(pos);
            if (psr.Status == PromptStatus.OK)
            {
                ObjectId[] objectids = psr.Value.GetObjectIds();
                Database database = HostApplicationServices.WorkingDatabase;
                using (Transaction tran = database.TransactionManager.StartTransaction())
                {
                    foreach (var item in objectids)
                    {
                        Entity entity = (Entity)item.GetObject(OpenMode.ForRead);
                        if (entity is T)
                        {
                            result.Add(entity as T);
                        }

                    }
                }
            }
            return result.Count() > 0;
        }

        /// <summary>
        /// 获得所有指定类型Entity
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static List<T> SelectAllEntities<T>() where T : Entity
        {
            List<T> result = new List<T>();
            Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction tran = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = (BlockTableRecord)bt[BlockTableRecord.ModelSpace].GetObject(OpenMode.ForRead);
                foreach (ObjectId item in btr)
                {
                    Entity entity = (Entity)item.GetObject(OpenMode.ForRead);
                    if (entity is T)
                    {
                        result.Add(entity as T);
                    }
                }
            }

            return result;
        }
        /// <summary>
        /// 获得所有指定类型Entity（带过滤器）
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="func">自定义过滤条件</param>
        /// <param name="filter">SelectionFilter过滤器</param>
        /// <returns></returns>
        public static List<T> SelectAllEntities<T>(SelectionFilter filter = null,Func<T, bool> func = null ) where T : Entity
        {
            List<T> result = new List<T>();
            Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction tran = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = (BlockTableRecord)bt[BlockTableRecord.ModelSpace].GetObject(OpenMode.ForRead);

                foreach (ObjectId item in btr)
                {
                    Entity entity = (Entity)item.GetObject(OpenMode.ForRead);
                    if (entity is T)
                    {
                        // 应用SelectionFilter过滤
                        if (filter != null && !PassesFilter(entity, filter))
                            continue;

                        result.Add(entity as T);
                    }
                }
            }

            // 应用自定义过滤条件
            if (func != null)
                result = result.Where(func).ToList();

            return result;
        }


        private static bool PassesFilter(Entity entity, SelectionFilter filter)
        {
            if (filter == null) return true;

            var filterList = filter.GetFilter();
            if (filterList == null) return true;

            for (int i = 0; i < filterList.Length; i++)
            {
                var dxfCode = (DxfCode)filterList[i].TypeCode;
                var value = filterList[i].Value;

                switch (dxfCode)
                {
                    case DxfCode.Start:
                        if (!entity.GetRXClass().Name.Equals(value.ToString(), StringComparison.OrdinalIgnoreCase))
                            return false;
                        break;
                    case DxfCode.LayerName:
                        if (!entity.Layer.Equals(value.ToString(), StringComparison.OrdinalIgnoreCase))
                            return false;
                        break;
                        // 添加更多DXF代码处理...
                }
            }
            return true;
        }

        /// <summary>
        /// 获得所有指定类型Entity
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static List<T> SelectAllEntities<T>(Func<T, bool> func) where T : Entity
        {
            List<T> result = new List<T>();
            Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction tran = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)db.BlockTableId.GetObject(OpenMode.ForRead);
                BlockTableRecord btr = (BlockTableRecord)bt[BlockTableRecord.ModelSpace].GetObject(OpenMode.ForRead);
                foreach (ObjectId item in btr)
                {
                    Entity entity = (Entity)item.GetObject(OpenMode.ForRead);
                    if (entity is T)
                    {
                        result.Add(entity as T);
                    }
                }
            }
            result = result.Where(func).ToList();
            return result;
        }

        /// <summary>
        /// 获取一个Entity
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="message">提示词</param>
        /// <param name="entity">传出的Entity</param>
        /// <param name="point">点击坐标</param>
        /// <returns></returns>
        public static bool SelectEntity<T>(out T entity, out Point3d point, string message = "") where T : Entity
        {
            entity = null;
            point = new Point3d();
            Editor editor = W.Ed;
            PromptEntityResult per = editor.GetEntity("\n" + message);
            Database database = HostApplicationServices.WorkingDatabase;
            using (W.Doc.LockDocument())
            {
                using (Transaction tran = database.TransactionManager.StartTransaction())
                {
                    if (per.Status == PromptStatus.OK)
                    {
                        ObjectId objectid = per.ObjectId;
                        point = per.PickedPoint;


                        entity = objectid.GetObject(OpenMode.ForWrite) as T;
                        if (entity == null) return false;
                        else return true;
                    }
                    tran.Commit();
                }
            }
            return false;
        }

        /// <summary>
        /// 获取一个Entity
        /// </summary>
        /// <param name="message">提示词</param>
        /// <param name="entity">传出的Entity</param>
        /// <returns></returns>
        public static bool SelectEntity<T>(out T entity, string message = "选择图形") where T : Entity
        {
            return SelectEntity(out entity, out Point3d point, message);
        }
        /// <summary>
        /// 获取块中的一个Entity
        /// </summary>
        /// <param name="message"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static bool SelectNestedEntity<T>(out T entity, string message = "") where T : Entity
        {
            entity = null;
            Editor editor = W.Ed;
            PromptNestedEntityResult pner = editor.GetNestedEntity("\n" + message);
            if (pner.Status == PromptStatus.OK)
            {
                Database db = HostApplicationServices.WorkingDatabase;
                using (Transaction tran = db.TransactionManager.StartTransaction())
                {
                    entity = (T)pner.ObjectId.GetObject(OpenMode.ForRead);
                    return true;
                }
            }
            return false;
        }


        public static bool SelectCrossingPolygon<T>(List<Point3d> points, out List<T> result) where T : Entity
        {
            result = new List<T>();
            Editor editor = W.Ed;
            Point3dCollection pos = new Point3dCollection();
            points.ForEach(x => pos.Add(x));
            PromptSelectionResult psr = editor.SelectCrossingPolygon(pos);
            if (psr.Status == PromptStatus.OK)
            {
                ObjectId[] objectids = psr.Value.GetObjectIds();
                Database database = HostApplicationServices.WorkingDatabase;
                using (Transaction tran = database.TransactionManager.StartTransaction())
                {
                    foreach (var item in objectids)
                    {
                        Entity entity = (Entity)item.GetObject(OpenMode.ForRead);
                        if (entity is T)
                        {
                            result.Add(entity as T);
                        }

                    }
                }
            }
            return result.Count() > 0;
        }
        public static bool SelectWindowPolygon<T>(List<Point3d> points, out List<T> result) where T : Entity
        {
            result = new List<T>();
            Editor editor = W.Ed;
            Point3dCollection pos = new Point3dCollection();
            points.ForEach(x => pos.Add(x));
            PromptSelectionResult psr = editor.SelectWindowPolygon(pos);
            if (psr.Status == PromptStatus.OK)
            {
             
                ObjectId[] objectids = psr.Value.GetObjectIds();
                Database database = HostApplicationServices.WorkingDatabase;
                using (Transaction tran = database.TransactionManager.StartTransaction())
                {
                    foreach (var item in objectids)
                    {

                        Entity entity = (Entity)item.GetObject(OpenMode.ForRead);
                        if (entity is T)
                        {
                            result.Add(entity as T);
                        }

                    }
                }
            }
            return result.Count() > 0;
        }
    }
}
