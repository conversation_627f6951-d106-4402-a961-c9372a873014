﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    public class FilterUtil
    {
        public static SelectionFilter OrFilter(string type, string layer = "*")
        {
            TypedValue[] values = {
     new TypedValue((int) DxfCode.Start, type),
          new TypedValue((int) DxfCode.LayerName, layer),
            };
            SelectionFilter filter = new SelectionFilter(values);
            return filter;

        }

        public static SelectionFilter AndFilter(string type, string layer = "*")
        {

            TypedValue[] values = {
     new TypedValue((int) DxfCode.Operator, "<and"),
     new TypedValue((int) DxfCode.Start, type),
     new TypedValue((int) DxfCode.LayerName, layer),
     new TypedValue((int) DxfCode.Operator, "and>"),
            };
            SelectionFilter filter = new SelectionFilter(values);
            return filter;
        }


    }
}
