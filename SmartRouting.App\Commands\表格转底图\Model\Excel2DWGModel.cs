﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.表格转底图.Model
{
    public class Excel2DWGModel
    {

        /// <summary>
        /// 管种名称
        /// </summary>
        public string PipeName { get; set; }
        //{
        //    get => GetProperty(() => PipeName);
        //    set => SetProperty(() => PipeName, value);
        //}
        /// <summary>
        /// 图上点号
        /// </summary>
        public string PointNum { get; set; }
        //{
        //    get => GetProperty(() => PointNum);
        //    set => SetProperty(() => PointNum, value);
        //}

        ///// <summary>
        /// 方向图上点号
        /// </summary>
        public string DirPointNum { get; set; }
        //{
        //    get => GetProperty(() => DirPointNum);
        //    set => SetProperty(() => DirPointNum, value);
        //}

        /// <summary>
        /// 特征点名
        /// </summary>
        public string FeaturePointName { get; set; }
        //{ 
        //    get => GetProperty(() => FeaturePointName);
        //    set => SetProperty(() => FeaturePointName, value);
        //}


        /// <summary>
        /// 管径（孔、根数）
        /// </summary>
        public string PipeDiameter { get; set; }
        //{
        //    get => GetProperty(() => PipeDiameter);
        //    set => SetProperty(() => PipeDiameter, value);
        //}
        /// <summary>
        /// 材质
        /// </summary>
        public string Material { get; set; }
        //{
        //    get => GetProperty(() => Material);
        //    set => SetProperty(() => Material, value);
        //}


        /// <summary>
        /// X坐标
        /// </summary>
        public double X { get; set; }
        //{
        //    get => GetProperty(() => X);
        //    set => SetProperty(() => X, value);
        //}

        /// <summary>
        /// Y坐标
        /// </summary>
        public double Y { get; set; }
        //{
        //    get => GetProperty(() => Y);
        //    set => SetProperty(() => Y, value);
        //}


        /// <summary>
        /// Z坐标(地面高程)
        /// </summary>
        public double Z { get; set; }
        //{
        //    get => GetProperty(() => Z);
        //    set => SetProperty(() => Z, value);
        //}


        /// <summary>
        /// 管点埋深
        /// </summary>
        public double BuriedDepth { get; set; }
        //{
        //    get => GetProperty(() => BuriedDepth);
        //    set => SetProperty(() => BuriedDepth, value);
        //}
        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; }
        //{
        //    get => GetProperty(() => Remarks);
        //    set => SetProperty(() => Remarks, value);
        //}


    }
}
