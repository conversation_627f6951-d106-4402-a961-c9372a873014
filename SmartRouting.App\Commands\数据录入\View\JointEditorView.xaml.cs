using System.Windows;
using SmartRouting.App.Commands.数据录入.Model;

namespace SmartRouting.App.Commands.数据录入.View
{
    public partial class JointEditorView : Window
    {
        public JointEditorView()
        {
            InitializeComponent();
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
        
        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            // 这是默认行为，不再需要重写
            base.OnClosing(e);
        }
    }
} 