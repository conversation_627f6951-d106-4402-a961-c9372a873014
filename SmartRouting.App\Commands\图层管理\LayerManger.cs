﻿using SmartRouting.App.Commands.图层管理.View;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.图层管理
{
    public class LayerManger
    {
        [CommandTag("常    用", nameof(LayerMangerCmd), "图层管理")]
        [CommandMethod(nameof(LayerMangerCmd))]
        public void LayerMangerCmd()
        {
            #region 判断是否过了使用期限
            if (GlobalSetting.SetDeadLine())
            {
                MessageBoxUtil.MessageInformation("软件已过使用期限或未联网，请联系开发者!");
                return;
            }
            #endregion
            InpuDwgFilesView win = new InpuDwgFilesView();
            CadApp.ShowModalWindow(win);




        }




    }
}
