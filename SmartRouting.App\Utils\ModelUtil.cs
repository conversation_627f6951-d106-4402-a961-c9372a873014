﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    public static class ModelUtil
    {
        public static ObjectId CreateEntity(Entity entity, int colorIndex, string layerName)
        {
            LayerUtil.CreateLayer(layerName, (short)colorIndex, true);
            Database database = W.Doc.Database;
            ObjectId result = default(ObjectId);
            bool flag = entity != null;
            if (flag)
            {
                using (W.Doc.LockDocument())
                {
                    using (Transaction transaction = database.TransactionManager.StartTransaction())
                    {
                        BlockTableRecord blockTableRecord = (BlockTableRecord)transaction.GetObject(database.CurrentSpaceId, OpenMode.ForWrite, false);
                        entity.ColorIndex = colorIndex;
                        entity.Layer = layerName;
                        result = blockTableRecord.AppendEntity(entity);
                        transaction.AddNewlyCreatedDBObject(entity, true);
                        transaction.Commit();
                    }
                }
            }
            return result;
        }


        public static void EraseEntities(this List<ObjectId> ids)
        {
            Database db = HostApplicationServices.WorkingDatabase;
            using (<PERSON><PERSON>DocLock)
            {
                using (Transaction tran = db.TransactionManager.StartTransaction())
                {
                    foreach (var id in ids)
                    {
                        var ent = tran.GetObject(id, OpenMode.ForWrite) as Entity;
                        if (ent.IsErased) continue;
                        if (ent.ObjectId == ObjectId.Null) continue;
                        ent.Erase();
                    }
                    tran.Commit();
                }
            }
        }



        public static Entity CreateEntity2(Entity entity, int colorIndex, string layerName)
        {
            LayerUtil.CreateLayer(layerName, (short)colorIndex, true);
            Database database = W.Doc.Database;
            ObjectId result = default(ObjectId);
            bool flag = entity != null;
            if (flag)
            {
                using (W.Doc.LockDocument())
                {
                    using (Transaction transaction = database.TransactionManager.StartTransaction())
                    {
                        BlockTableRecord blockTableRecord = (BlockTableRecord)transaction.GetObject(database.CurrentSpaceId, OpenMode.ForWrite, false);
                        entity.ColorIndex = colorIndex;
                        entity.Layer = layerName;
                        result = blockTableRecord.AppendEntity(entity);
                        transaction.AddNewlyCreatedDBObject(entity, true);
                        transaction.Commit();
                        return entity;
                    }
                }
            }
            return entity;
        }
        public static ObjectId CreateEntity(Entity entity)
        {
            Database db = W.Doc.Database;
            ObjectId result = default(ObjectId);
            if (entity != null)
            {
                using (W.Doc.LockDocument())
                {
                    using (Transaction transaction = W.Trans)
                    {
                        BlockTableRecord blockTableRecord = (BlockTableRecord)transaction.GetObject(db.CurrentSpaceId, OpenMode.ForWrite);
                        result = blockTableRecord.AppendEntity(entity);
                        transaction.AddNewlyCreatedDBObject(entity, true);
                        transaction.Commit();
                    }
                }
            }
            return result;
        }

        public static List<Line> GetBreakCurves<T>(List<T> curves) where T : Curve
        {
            List<Line> lines = new List<Line>();
            foreach (var curve in curves)
            {
                if (curve is Line)
                {
                    lines.Add(curve as Line);
                }
                if (curve is Polyline polyline)
                {
                    List<Curve> cs = ModelUtil.ExplodePolyline(polyline);//炸开后的曲线
                    foreach (var c in cs)
                    {
                        if (c is Line line1) lines.Add(line1);
                    }
                }
            }
            Dictionary<Line, List<Point3d>> pointsOnLine = new();
            foreach (var line in lines)
            {
                pointsOnLine.Add(line, new List<Point3d>());
            }
            for (int i = 0; i < lines.Count - 1; i++)
            {

                for (int j = i + 1; j < lines.Count; j++)
                {
                    Point3dCollection pos = new();
                    lines[i].IntersectWith(lines[j], Intersect.OnBothOperands, pos, IntPtr.Zero, IntPtr.Zero);
                    if (pos.Count > 0)
                    {
                        foreach (Point3d p in pos)
                        {
                            if (!pointsOnLine[lines[i]].Any(s => s.DistanceTo(p) < 2))
                            {

                                pointsOnLine[lines[i]].Add(p);
                            }
                            if (!pointsOnLine[lines[j]].Any(s => s.DistanceTo(p) < 2))
                            {
                                pointsOnLine[lines[j]].Add(p);
                            }

                        }

                    }
                }


            }
            lines.Clear();
            foreach (var item in pointsOnLine)
            {

                Line line = item.Key;
                //  var pts = item.Value.Distinct(new EqualityPoint()).ToList();
                var pts = item.Value.ToList();
                if (pts.Count == 0)
                {
                    lines.Add(line);
                }

                else
                {
                    if (pts.Count > 1)
                    {

                        // pts = pts.OrderBy(x => line.GetParameterAtPoint(line.GetClosestPointTo(x, false))).ToList();

                        pts = pts.OrderBy(x => x.DistanceTo(line.StartPoint)).ToList();

                    }
                    Point3dCollection pos = new();
                    foreach (var item2 in pts)
                    {
                        var p = line.GetClosestPointTo(item2, false);
                        pos.Add(p);
                    }
                    if (pos.Count > 0)
                    {
                        try
                        {
                            DBObjectCollection dbs = line.GetSplitCurves(pos);
                            foreach (Curve dbobject in dbs)
                            {
                                if (dbobject is Line line2)
                                {
                                    if (line2.Length <= 1) continue;
                                    lines.Add(line2);
                                }
                            }
                        }
                        catch (System.Exception ex)
                        {
                            W.Ed.WriteMessage(ex.Message);
                            continue;
                        }

                    }

                }
            }
            return lines;
        }





        /// <summary>
        /// 图元的复制
        /// </summary>
        /// <param name="ent"></param>
        /// <param name="sourcePt"></param>
        /// <param name="targetPt"></param>
        /// <returns></returns>

        public static Entity CopyTo(Entity ent, Point3d sourcePt, Point3d targetPt)
        {

            Matrix3d mt = Matrix3d.Displacement(targetPt - sourcePt);
            Entity entity = ent.GetTransformedCopy(mt);
            return entity;
        }









        /// <summary>
        /// 创建二维多段线
        /// </summary>
        /// <param name="pts"></param>
        /// <param name="width"></param>
        /// <returns></returns>
        public static Polyline CreatPolyline(Point2dCollection pts, double width, bool isClose = true)
        {
            Polyline ent = new Polyline();

            for (int i = 0; i < pts.Count; i++)
            {
                ent.AddVertexAt(i, pts[i], 0, width, width);
            }
            //  List<Point3d> sortpt = (from p in pts orderby pline.GetParameterAtPoint(p) select p).Distinct().ToList();//排序好的点集合
            if (isClose == false)
            {
                ent.AddVertexAt(0, pts[0], 0, width, width);
            }
            ent.Closed = isClose;
            return ent ?? null;
        }

        public static Polyline CreatPolyline2(Point2dCollection pts, double width, bool isClose = true)
        {
            Polyline ent = new Polyline();

            for (int i = 0; i < pts.Count; i++)
            {
                ent.AddVertexAt(i, pts[i], 0, width, width);
            }

            ent.Closed = isClose;
            return ent ?? null;
        }

        public static Polyline CreatPolyline2(Point2dCollection pts, double width, string layer, bool isClose = true)
        {
            Polyline ent = new Polyline();

            for (int i = 0; i < pts.Count; i++)
            {
                ent.AddVertexAt(i, pts[i], 0, width, width);
            }
            ent.Layer = layer;
            ent.Closed = isClose;
            return ent ?? null;
        }

        /// <summary>
        /// 炸开多段线
        /// </summary>
        /// <param name="polyline"></param>
        /// <returns></returns>
        public static List<Curve> ExplodePolyline(Polyline polyline)
        {
            DBObjectCollection dBObjectCollection = new();
            polyline.Explode(dBObjectCollection);
            return dBObjectCollection.ToEntities<Curve>();
        }

        public static List<Curve> ExplodeCurve(Curve polyline)
        {
            DBObjectCollection dBObjectCollection = new();
            polyline.Explode(dBObjectCollection);
            return dBObjectCollection.ToEntities<Curve>();
        }

        public static Polyline PolylineRemoveNearPoint(Polyline polyline, double width = 0, double tol = 0.01)
        {
            List<Point2d> pts = new List<Point2d>();
            Polyline ent = new Polyline();

            using (var tr = W.Trans)
            {

                var pLine = tr.GetObject(polyline.Id, OpenMode.ForWrite) as Polyline;
                for (int i = 0; i < pLine.NumberOfVertices; i++)
                {
                    if (!pts.Any(x => x.GetDistanceTo(pLine.GetPoint2dAt(i)) < tol))
                    {
                        pts.Add(pLine.GetPoint2dAt(i));
                    }
                }

                for (int i = 0; i < pts.Count; i++)
                {
                    ent.AddVertexAt(i, pts[i], 0, width, width);
                }
                ent.Closed = true;

            }


            return ent;
        }

        public static void EraseEntities<T>(this List<T> ents) where T : Entity
        {
            Database db = HostApplicationServices.WorkingDatabase;
            using (W.DocLock)
            {
                using (Transaction tran = db.TransactionManager.StartTransaction())
                {
                    foreach (var item in ents)
                    {
                        var ent = tran.GetObject(item.ObjectId, OpenMode.ForWrite) as Entity;
                        if (item.IsErased) continue;
                        if (item.ObjectId == ObjectId.Null) continue;
                        item.Erase();
                    }
                    tran.Commit();
                }
            }
        }


        public static void EraseEntity<T>(this T ent) where T : Entity
        {
            EraseEntities(new List<T>() { ent });
        }



        /// <summary>
        /// 遍历块里面的内容
        /// </summary>
        /// <param name="blockName"></param>
        public static void TraverseEntitiesInBlock(string blockName)
        {

            using (Transaction trans = W.Trans)
            {
                BlockTable bt = (BlockTable)trans.GetObject(W.Db.BlockTableId, OpenMode.ForRead);

                BlockTableRecord btr = null;

                // 查找特定的块定义
                foreach (ObjectId btrId in bt)
                {
                    BlockTableRecord currentBtr = (BlockTableRecord)trans.GetObject(btrId, OpenMode.ForRead);

                    if (currentBtr.Name.ToUpper() == blockName.ToUpper())
                    {
                        btr = currentBtr;
                        break;
                    }
                }

                if (btr != null)
                {

                    // 遍历块定义中的所有实体
                    foreach (ObjectId entId in btr)
                    {
                        Entity ent = (Entity)trans.GetObject(entId, OpenMode.ForRead);
                        if (ent is Circle circle)
                        {
                            // MessageBoxUtil.MessageInformation("圆的半径为：" + circle.Radius);
                        }

                        if (ent is Line line)
                        {
                            // MessageBoxUtil.MessageInformation("直线的长度为：" + line.Length);
                        }

                        if (ent is Polyline polyline)
                        {
                            // MessageBoxUtil.MessageInformation("多段线的图层为：" + polyline.Layer);
                        }


                    }
                }
                trans.Commit();
            }

        }



        /// <summary>
        /// 插入外部DWG
        /// </summary>
        /// <param name="doc"></param>
        /// <param name="fname"></param>
        /// <param name="blockname"></param>
        /// <param name="insertpt"></param>
        /// <param name="ang"></param>
        /// <param name="sc"></param>
        /// <returns></returns>
        public static BlockReference InsertBlockRef(Document doc, string fname, string blockname, Point3d insertpt, double ang, Scale3d sc)
        {
            BlockReference bref = null;
            try
            {
                Database curdb = doc.Database;
                Editor ed = doc.Editor;
                ObjectId blkid = ObjectId.Null;
                using (Transaction curtr = W.Trans)
                {
                    BlockTable curbt = (BlockTable)curtr.GetObject(curdb.BlockTableId, OpenMode.ForRead, false);
                    if (curbt.Has(blockname))
                    {
                        ed.WriteMessage(blockname + "块已存在！");
                        blkid = curbt[blockname];
                        bref = new BlockReference(insertpt, blkid);
                        BlockTableRecord btr = (BlockTableRecord)curtr.GetObject(curdb.CurrentSpaceId, OpenMode.ForWrite);
                        using (BlockTableRecord bdef = (BlockTableRecord)curtr.GetObject(bref.BlockTableRecord, OpenMode.ForWrite))
                        {
                            bref.ScaleFactors = sc;
                            bref.Rotation = ang;
                            bref.TransformBy(ed.CurrentUserCoordinateSystem);
                            bref.RecordGraphicsModified(true);
                            btr.AppendEntity(bref);
                            curtr.AddNewlyCreatedDBObject(bref, true);
                            bref.DowngradeOpen();
                        }

                        curtr.Commit();

                    }
                    else
                    {
                        using (Database db = new Database(false, true))
                        {

                            db.ReadDwgFile(fname, System.IO.FileShare.Read, true, "");
                            db.CloseInput(true);
                            try
                            {
                                blkid = curdb.Insert(blockname, db, false);
                            }
                            catch (System.Exception ex)
                            {


                            }

                        }
                        BlockTableRecord newBlkDef = (BlockTableRecord)curtr.GetObject(blkid, OpenMode.ForRead, false);
                        BlockTableRecord btr = (BlockTableRecord)curtr.GetObject(curdb.CurrentSpaceId, OpenMode.ForWrite);
                        if (newBlkDef == null)
                        {
                            newBlkDef.UpgradeOpen();
                            newBlkDef.Name = blockname;
                            newBlkDef.DowngradeOpen();
                        }

                        bref = new BlockReference(insertpt, blkid);

                        bref.ScaleFactors = sc;
                        bref.Rotation = ang;
                        bref.TransformBy(ed.CurrentUserCoordinateSystem);
                        btr.AppendEntity(bref);
                        curtr.AddNewlyCreatedDBObject(bref, true);


                        curtr.Commit();

                    }
                }
            }
            catch (System.Exception ex)
            {
                Application.ShowAlertDialog(ex.Message + "\n" + ex.StackTrace);
            }
            finally
            {

            }

            return bref;
        }

        public static List<string> GetAllBlockNames()
        {
            // 创建一个列表来存储块名
            List<string> blockNames = new List<string>();

            // 获取当前文档和数据库


            // 使用事务来处理数据库对象
            using (Transaction trans = W.Trans)
            {
                // 打开块表
                BlockTable bt = (BlockTable)trans.GetObject(W.Db.BlockTableId, OpenMode.ForRead, false);

                // 遍历块表中的所有记录
                foreach (ObjectId btrId in bt)
                {
                    // 获取块表记录
                    BlockTableRecord btr = (BlockTableRecord)trans.GetObject(btrId, OpenMode.ForRead, false);

                    // 添加块名到列表（排除匿名块）
                    if (!btr.IsAnonymous && !btr.IsLayout)
                    {
                        blockNames.Add(btr.Name);
                    }
                }

                // 提交事务
                trans.Commit();
            }

            // 返回块名列表
            return blockNames;
        }

























        /// <summary>
        /// 插入外部文件
        /// </summary>
        /// <param name="doc"></param>
        /// <param name="fname"></param>
        /// <param name="blockname"></param>
        /// <param name="insertpt"></param>
        /// <param name="ang"></param>
        /// <param name="sc"></param>

        public static BlockReference InsertBlockRef2(Document doc, string fname, string blockname, Point3d insertpt, double ang, Scale3d sc)
        {
            BlockReference bref = null;
            try
            {

                ObjectId blkid = ObjectId.Null;
                // MessageBoxUtils.MessageOutPutLog("11111");
                using (Transaction curtr = W.Trans)
                {
                    BlockTable curbt = (BlockTable)curtr.GetObject(W.Db.BlockTableId, OpenMode.ForRead, false);
                    using (Database db = new Database(false, true))
                    {

                        // MessageBoxUtils.MessageOutPutLog("2222");
                        db.ReadDwgFile(fname, System.IO.FileShare.Read, true, "");
                        // MessageBoxUtils.MessageOutPutLog("3333");
                        db.CloseInput(true);
                        // MessageBoxUtils.MessageOutPutLog("4444");
                        try
                        {
                            blkid = W.Db.Insert(blockname, db, false);
                        }
                        catch (Exception ex)
                        {


                        }

                        //MessageBoxUtils.MessageOutPutLog("55555");
                    }
                    if (blkid != ObjectId.Null)
                    {
                        BlockTableRecord newBlkDef = (BlockTableRecord)curtr.GetObject(blkid, OpenMode.ForRead, false);
                        BlockTableRecord btr = (BlockTableRecord)curtr.GetObject(W.Db.CurrentSpaceId, OpenMode.ForWrite);
                        if (newBlkDef != null)
                        {
                            bref = new BlockReference(insertpt, blkid);

                            btr.AppendEntity(bref);
                            curtr.AddNewlyCreatedDBObject(bref, true);

                        }


                    }


                    curtr.Commit();


                }
            }
            catch (Exception ex)
            {
                //  MessageBoxUtil.Messag_Error(ex.Message);
                //  CadApp.ShowAlertDialog(ex.Message + "\n" + ex.StackTrace);
            }


            return bref;
        }




        /// <summary>
        /// 批量将实体添加到模型空间，效率极高。
        /// </summary>
        /// <param name="entities">要添加的实体集合。</param>
        public static void AddEntities(IEnumerable<Entity> entities)
        {
            var db = W.Db;
            using (var tr = db.TransactionManager.StartTransaction())
            {
                var btr = (BlockTableRecord)tr.GetObject(db.CurrentSpaceId, OpenMode.ForWrite);
                foreach (var ent in entities)
                {
                    // 确保实体是新的，没有被添加到数据库中
                    if (ent.ObjectId.IsNull)
                    {
                        btr.AppendEntity(ent);
                        tr.AddNewlyCreatedDBObject(ent, true);
                    }
                }
                tr.Commit();
            }
        }



        /// <summary>
        /// 批量将实体添加到模型空间，并对每个新添加的实体执行一个操作（如添加扩展数据）。
        /// </summary>
        /// <param name="entities">要添加的实体集合。</param>
        /// <param name="actionPerEntity">对每个新添加的实体执行的操作。参数为实体和当前事务。</param>
        public static void AddEntities(IEnumerable<Entity> entities, Action<Entity, Transaction> actionPerEntity)
        {
            var db = W.Db;
            using (var tr = db.TransactionManager.StartTransaction())
            {
                var btr = (BlockTableRecord)tr.GetObject(db.CurrentSpaceId, OpenMode.ForWrite);
                foreach (var ent in entities)
                {
                    if (ent.ObjectId.IsNull)
                    {
                        btr.AppendEntity(ent);
                        tr.AddNewlyCreatedDBObject(ent, true);
                        // 在实体添加到数据库后，它就有了 ObjectId，可以对其执行操作了
                        actionPerEntity?.Invoke(ent, tr);
                    }
                }
                tr.Commit();
            }
        }
    }
}
