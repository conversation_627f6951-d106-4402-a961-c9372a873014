﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Common.GeneralDuplicate
{
    public class EqualityBlockReference : IEqualityComparer<BlockReference>
    {
        public bool Equals(BlockReference x, BlockReference y)
        {
            return x.Name.Equals(y.Name)&&(x.Position.DistanceTo(y.Position) <=0.001);
        }

        public int GetHashCode(BlockReference obj)
        {
            return 1;
        }
    }
}
