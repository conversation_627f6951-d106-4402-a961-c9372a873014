﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    public class GetUtil
    {




        /// <summary>
        /// 转换点坐标：UCS -> WCS
        /// </summary>
        /// <param name="ucsPoint"></param>
        /// <returns></returns>
        public static Point3d GetConvertUcsToWcs(Point3d ucsPoint)
        {

            // 获取UCS到WCS的变换矩阵
            Matrix3d ucsToWcs = W.Ed.CurrentUserCoordinateSystem;
            // 转换点坐标：UCS -> WCS
            Point3d wcsPoint = ucsPoint.TransformBy(ucsToWcs);
            return wcsPoint;
        }







        /// <summary>
        /// 得到UCS旋转的角度
        /// </summary>
        /// <returns></returns>
        public static double GetUcsRotationAngle()
        {

            // 获取当前UCS到WCS的变换矩阵
            Matrix3d ucsToWcs = W.Ed.CurrentUserCoordinateSystem;
            // 提取X轴方向向量
            Vector3d xAxis = ucsToWcs.CoordinateSystem3d.Xaxis;
            // 计算与WCS X轴的夹角（弧度）
            double angleRad = Math.Atan2(xAxis.Y, xAxis.X);
            // 转换为度数并调整到0-360范围
            double angleDeg = angleRad * (180.0 / Math.PI);
            if (angleDeg < 0)
                angleDeg += 360.0;
            return angleDeg;
        }

        /// <summary>
        /// 得到填充的填充边界
        /// </summary>
        /// <param name="hatch">填充</param>
        /// <param name="numSample">采样点，数值越大生成的越大，生成的节点越多</param>
        /// <param name="polylineWidth"></param>
        /// <param name="closed"></param>
        /// <returns></returns>

        public static Polyline GetHatchBoundaries(Hatch hatch, int numSample, double polylineWidth = 0, bool closed = true)
        {
            var outerLoops = new List<Polyline>();
            //  innerLoops = new List<Polyline>();

            int loopNum = hatch.NumberOfLoops;
            for (int i = 0; i < loopNum; i++)
            {
                HatchLoop hatLoop;
                try
                {
                    hatLoop = hatch.GetLoopAt(i);
                }
                catch
                {
                    continue;
                }

                // 提取当前环的点集合
                Point2dCollection points = new Point2dCollection();
                if (hatLoop.IsPolyline)
                {
                    // 处理多段线环
                    BulgeVertexCollection vertices = hatLoop.Polyline;
                    foreach (BulgeVertex vertex in vertices)
                    {
                        points.Add(vertex.Vertex);
                    }
                }
                else
                {
                    // 处理曲线环（直线、圆弧等）
                    Curve2dCollection curves = hatLoop.Curves;
                    foreach (Curve2d curve in curves)
                    {
                        Point2d[] samplePoints = curve.GetSamplePoints(numSample);
                        foreach (Point2d pt in samplePoints)
                        {
                            if (!points.Contains(pt))
                                points.Add(pt);
                        }
                    }
                }

                // 生成当前环的多段线
                Polyline loopPolyline = ModelUtil.CreatPolyline2(points, polylineWidth, hatch.Layer, closed);
                outerLoops.Add(loopPolyline);

                //// 判断内外环（关键逻辑！）
                //if (IsOuterLoop(hatch, hatLoop, i))
                //{
                //    outerLoops.Add(loopPolyline);
                //}
                //else
                //{
                //    innerLoops.Add(loopPolyline);
                //}
            }
            var pline = outerLoops.OrderByDescending(x => x.Area).First();
            return pline;
        }



        /// <summary>
        /// 通过2个点绘制矩形框
        /// </summary>
        /// <param name="minPt"></param>
        /// <param name="maxPt"></param>
        /// <returns></returns>
        public static Polyline GetPolylineByTwoPoints(Point3d minPt, Point3d maxPt, double width = 50)
        {
            Point2dCollection points = new Point2dCollection();
            points.Add(new Point2d(minPt.X, minPt.Y));
            points.Add(new Point2d(minPt.X, maxPt.Y));
            points.Add(new Point2d(maxPt.X, maxPt.Y));
            points.Add(new Point2d(maxPt.X, minPt.Y));
            return ModelUtil.CreatPolyline(points, width);
        }







        /// <summary>
        /// 获取用户输入文字
        /// </summary>
        /// <param name="message">提示词</param>
        /// <param name="str">传出的文字</param>
        /// <returns></returns>
        public static bool GetString(out string str, string message = "")
        {
            str = "";
            Editor editor = W.Ed;
            PromptResult pr = editor.GetString(message);
            if (pr.Status == PromptStatus.OK)
            {
                str = pr.StringResult;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取字符串里面所有数字
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static List<String> GetAllNumInTestString(string str)
        {
            List<String> _list = new List<string>();

            if (!String.IsNullOrEmpty(str))
            {
                String pattern = @"\d*\.\d*|0\.\d*[1-9]\d*$";
                Regex regex = new Regex(pattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);
                MatchCollection matchs = regex.Matches(str);
                foreach (Match m in matchs)
                {
                    _list.Add(m.Groups[0].Value);
                }
                str = Regex.Replace(str, pattern, "");

                String pattern2 = @"[0-9]+";
                Regex regex2 = new Regex(pattern2, RegexOptions.IgnoreCase | RegexOptions.Singleline);
                MatchCollection matchs2 = regex2.Matches(str);
                foreach (Match m in matchs2)
                {
                    _list.Add(m.Groups[0].Value);
                }
            }

            return _list;
        }




        /// <summary>
        /// 获取2点的中点
        /// </summary>
        /// <param name="pt1"></param>
        /// <param name="pt2"></param>
        /// <returns></returns>
        public static Point3d GetMidPoint(Point3d pt1, Point3d pt2)
        {
            Point3d result;
            result = new Point3d((pt1.X + pt2.X) / 2.0, (pt1.Y + pt2.Y) / 2.0, 0.0);
            return result;
        }
        /// <summary>
        /// 获得范围内的实体
        /// </summary>
        /// <param name="polyline"></param>
        /// <returns></returns>
        public static List<ObjectId> GetInArangeItems<T>(T curve, double offsetLength, string avoidLayer = "柱网") where T : Entity
        {
            List<ObjectId>? ids = null;
            List<ObjectId> InRange = new List<ObjectId>();
            if (curve is Polyline polyline)
            {
                InRange.Clear();
                DBObjectCollection offsetcur = GeometryUtil.Clockwise(polyline) == 1 ? polyline.GetOffsetCurves(offsetLength) : polyline.GetOffsetCurves(-offsetLength);
                var newPline = offsetcur[0] as Polyline;
                Point3dCollection point3DCollection = new Point3dCollection();
                for (int i = 0; i < newPline.NumberOfVertices; i++)
                {
                    point3DCollection.Add(newPline.GetPoint2dAt(i).ToPoint3d());
                }
                PromptSelectionResult psr = W.Ed.SelectCrossingPolygon(point3DCollection);
                if (psr != null && psr.Status == PromptStatus.OK)
                {
                    ids = psr.Value.GetObjectIds().ToList();

                    using (Transaction tran = W.Db.TransactionManager.StartTransaction())
                    {
                        foreach (ObjectId id in ids)
                        {
                            Entity ent = tran.GetObject(id, OpenMode.ForRead) as Entity;
                            if (ent.Layer == avoidLayer)
                            {
                                Point3dCollection IntersectWithPoint3DCollection = new Point3dCollection();
                                curve.IntersectWith(ent, Intersect.OnBothOperands, IntersectWithPoint3DCollection, IntPtr.Zero, IntPtr.Zero);
                                if (IntersectWithPoint3DCollection.Count > 0)
                                {
                                    InRange.Add(ent.ObjectId);
                                }
                            }
                            else if (ent.ObjectId == curve.ObjectId)
                            {
                                InRange.Add(ent.ObjectId);
                            }
                        }
                        tran.Commit();
                    }

                    return InRange;
                }
            }
            if (curve is Circle circle)
            {
                InRange.Clear();
                DBObjectCollection offsetcur = circle.GetOffsetCurves(offsetLength);
                Circle newCircle = offsetcur[0] as Circle;
                Extents3d extent = newCircle.GeometricExtents;
                var pt1 = extent.MinPoint;
                var pt2 = extent.MaxPoint;
                Point3dCollection point3DCollection = new Point3dCollection();
                point3DCollection.Add(pt1);
                point3DCollection.Add(new Point3d(pt1.X, pt2.Y, 0));
                point3DCollection.Add(pt2);
                point3DCollection.Add(new Point3d(pt2.X, pt1.Y, 0));
                PromptSelectionResult psr = W.Ed.SelectCrossingPolygon(point3DCollection);
                if (psr.Status == PromptStatus.OK)
                {
                    ids = psr.Value.GetObjectIds().ToList();
                    using (Transaction tran = W.Db.TransactionManager.StartTransaction())
                    {
                        foreach (ObjectId id in ids)
                        {
                            Entity ent = tran.GetObject(id, OpenMode.ForRead) as Entity;
                            if (ent.Layer == avoidLayer)
                            {
                                Point3dCollection IntersectWithPoint3DCollection = new Point3dCollection();
                                curve.IntersectWith(ent, Intersect.OnBothOperands, IntersectWithPoint3DCollection, IntPtr.Zero, IntPtr.Zero);
                                if (IntersectWithPoint3DCollection.Count > 0)
                                {
                                    InRange.Add(ent.ObjectId);
                                }
                            }
                            else if (ent.ObjectId == curve.ObjectId)
                            {
                                InRange.Add(ent.ObjectId);
                            }
                        }
                        tran.Commit();
                    }
                    return InRange;
                }

            }
            return InRange;

        }



        /// <summary>
        /// 得到一个点集合中相距最远的点
        /// </summary>
        /// <param name="pts"></param>
        /// <returns></returns>
        public static List<Point3d> GetLongerstPts(List<Point3d> pts)
        {
            double dis = 0;
            List<Point3d> rPts = new();
            Point3d sp = new Point3d();
            Point3d ep = new Point3d();
            var tmpList = pts.OrderBy(x => x.X).ToList();
            for (int i = 0; i < pts.Count; i++)
            {
                for (int j = 0; j < tmpList.Count; j++)
                {
                    var s = pts[i].DistanceTo(tmpList[j]);
                    if (s >= dis)
                    {
                        dis = s;
                        sp = tmpList[j];
                        ep = pts[i];
                    }


                }
            }

            rPts.Add(sp);
            rPts.Add(ep);
            return rPts;

        }






        /// <summary>
        /// 获取用户输入整数
        /// </summary>
        /// <param name="message">提示词</param>
        /// <param name="num">传出的整数</param>
        /// <returns></returns>
        public static bool GetInt(out int num, string message = "", int value = 0)
        {
            num = 0;
            Editor editor = W.Ed;
            PromptIntegerOptions pio = new PromptIntegerOptions(message);
            pio.DefaultValue = value;
            PromptIntegerResult pir = editor.GetInteger(pio);
            if (pir.Status == PromptStatus.OK)
            {
                num = pir.Value;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取用户输入小数
        /// </summary>
        /// <param name="message">提示词</param>
        /// <param name="d">传出的小数</param>
        /// <returns></returns>
        public static bool GetDouble(out double d, string message = "请输入数字", double value = 0)
        {
            d = 0;
            Editor editor = W.Ed;
            PromptDoubleOptions pdo = new PromptDoubleOptions(message);
            pdo.DefaultValue = value;
            PromptDoubleResult pdr = editor.GetDouble(pdo);
            if (pdr.Status == PromptStatus.OK)
            {
                d = pdr.Value;
                return true;
            }
            return false;
        }
        /// <summary>
        /// 获取一个点
        /// </summary>
        /// <param name="message">提示词</param>
        /// <param name="point"></param>
        /// <returns></returns>
        public static bool GetPoint(out Point3d point, string message = "选择点", Point3d? p = null)
        {
            point = Point3d.Origin;
            Editor editor = W.Ed;
            PromptPointOptions ppo = new PromptPointOptions(message);
            if (p != null)
            {
                ppo.BasePoint = p.Value;
                ppo.UseBasePoint = true;
            }
            PromptPointResult ppr = editor.GetPoint(ppo);
            if (ppr.Status == PromptStatus.OK)
            {
                point = ppr.Value;
                return true;
            }
            return false;
        }

        public static bool GetCorner(out Point3d basePoint, out Point3d point)
        {

            PromptPointOptions ppo = new PromptPointOptions("\n\t请选择第一个点: ");
            ppo.AllowNone = true;
            PromptPointResult ppr1 = W.Ed.GetPoint(ppo);
            point = Point3d.Origin;
            basePoint = Point3d.Origin;
            if (ppr1.Status != PromptStatus.OK || ppo == null) return false;
            basePoint = ppr1.Value;
            PromptCornerOptions pco = new PromptCornerOptions("\n\t请选择第二个点: ", basePoint);
            pco.UseDashedLine = true;
            pco.AllowNone = true;
            Editor editor = W.Ed;
            PromptPointResult ppr = editor.GetCorner(pco);
            if (ppr.Status == PromptStatus.OK || pco == null)
            {
                point = ppr.Value;
                if (point.DistanceTo(basePoint) > 0) return true;
                else return false;
            }
            return false;
        }
        public static bool GetKeywords(out string result, List<string> strs, string message = "")
        {
            result = "";
            PromptKeywordOptions pko = new PromptKeywordOptions(message);
            foreach (var item in strs)
            {
                if (item.Contains("(") && item.Contains(")"))
                {
                    string str = item.Split('(')[1].Replace(")", "");
                    pko.Keywords.Add(str, str, item);
                }
            }
            Editor editor = W.Ed;
            PromptResult pr = editor.GetKeywords(pko);
            if (pr.Status == PromptStatus.OK)
            {
                result = pr.StringResult;
                return true;
            }
            return false;
        }
        public static List<Point3d> GetPoints()
        {
            var r1 = W.Ed.GetSelection();
            var a = r1.Value[0];
            List<Point3d> points = new List<Point3d>();
            if (a is CrossingOrWindowSelectedObject c)
            {
                foreach (PickPointDescriptor item in c.GetPickPoints())
                {
                    points.Add(item.PointOnLine);
                }
            }
            return points;
        }





        /// <summary>
        /// 得到多边形形心
        /// </summary>
        /// <param name="pl"></param>
        /// <returns></returns>

        public static Point3d GetCenter(Polyline pl)
        {
            PointF centroid = new PointF();
            List<PointF> pts = new List<PointF>();
            for (int m = 0; m < pl.NumberOfVertices; m++)
            {
                var point = pl.GetPoint3dAt(m);
                pts.Add(new PointF((float)point.X, (float)point.Y));
            }
            double signedArea = 0.0;
            double x0 = 0.0; // Current vertex X
            double y0 = 0.0; // Current vertex Y
            double x1 = 0.0; // Next vertex X
            double y1 = 0.0; // Next vertex Y
            double a = 0.0;  // Partial signed area

            // For all vertices except last
            int i = 0;
            for (i = 0; i < pts.Count - 1; ++i)
            {
                x0 = pts[i].X;
                y0 = pts[i].Y;
                x1 = pts[i + 1].X;
                y1 = pts[i + 1].Y;
                a = x0 * y1 - x1 * y0;
                signedArea += a;
                centroid.X += (float)((x0 + x1) * a);
                centroid.Y += (float)((y0 + y1) * a);
            }

            // Do last vertex
            x0 = pts[i].X;
            y0 = pts[i].Y;
            x1 = pts[0].X;
            y1 = pts[0].Y;
            a = x0 * y1 - x1 * y0;
            signedArea += a;
            centroid.X += (float)((x0 + x1) * a);
            centroid.Y += (float)((y0 + y1) * a);

            signedArea *= 0.5;
            centroid.X /= (float)((6 * signedArea));
            centroid.Y /= (float)((6 * signedArea));

            return new Point3d(centroid.X, centroid.Y, 0);
        }

        /// <summary>
        ///  获取图元的图层
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static string GetLayerName<T>() where T : Entity
        {
            try
            {
            selectAgain:
                PromptEntityOptions peo = new PromptEntityOptions("请选择图元\r\n");
                W.FocusDoc();
                PromptEntityResult per = W.Ed.GetEntity(peo);
                if (per.Status == PromptStatus.OK)
                {
                    using (W.DocLock)
                    {
                        using (var tr = W.Trans)
                        {
                            var entity = tr.GetObject(per.ObjectId, OpenMode.ForRead) as Entity;
                            if (entity is T e)
                            {
                                return e.Layer;
                            }
                            else
                            {
                                W.Ed.WriteMessage($"请选择{typeof(T)}类型的图元\r\n");
                                goto selectAgain;
                            }
                            tr.Commit();
                        }
                    }
                }
            }
            catch (Exception ex)
            {

                MessageBoxUtil.MessageInformation(ex.Message);
            }

            return string.Empty;
        }





        public static T GeEntity<T>() where T : Entity
        {
        selectAgain:
            PromptEntityOptions peo = new PromptEntityOptions("请选择图元\r\n");
            W.FocusDoc();
            PromptEntityResult per = W.Ed.GetEntity(peo);
            if (per.Status == PromptStatus.OK)
            {
                using (W.DocLock)
                {
                    using (var tr = W.Trans)
                    {
                        var entity = tr.GetObject(per.ObjectId, OpenMode.ForRead) as Entity;
                        if (entity is T e)
                        {
                            return e;

                        }
                        else
                        {
                            W.Ed.WriteMessage($"请选择{typeof(T)}类型的图元\r\n");
                            goto selectAgain;
                        }
                        tr.Commit();
                    }
                }
            }
            else { return null; }

        }













        /// <summary>
        /// 在curve上生成一个法线
        /// </summary>
        /// <param name="curve">这个曲线</param>
        /// <param name="distanceToStartPoint">距离这个curve的起点多远距离开始生成法线</param>
        /// <param name="NormCurveLength">生成的法线的长度</param>
        /// <returns></returns>
        public static Line GetNormLine(Curve curve, double distanceToStartPoint, double NormCurveLength, int dir = 1)
        {
            //获取距离起点 mydis 的多段线上的一个点
            Point3d startPoint = curve.GetPointAtDist(distanceToStartPoint);
            try
            {

                //一阶导数 即切线向量

                Vector3d curVector = curve.GetFirstDerivative(startPoint);

                //获取切线方向的垂线向量 (从切线得到法线) 
                Vector3d curPervector = curVector.GetPerpendicularVector();
                //向量转化成标准单位向量 
                curPervector = curPervector.GetNormal();
                //起始点+ 单位向量*距离 就是距离起始点 距离向量长度的 一个点 
                Point3d endPoint = startPoint.Add(dir * curPervector * NormCurveLength);//法线的一个点
                if (dir == 1)
                {
                    Line line = new Line(startPoint, endPoint);//法线
                    return line;
                }
                else
                {
                    Line line = new Line(endPoint, startPoint);//法线
                    return line;
                }
            }
            catch (Exception)
            {

                return new Line(startPoint, Point3d.Origin);
            }


        }

        /// <summary>
        /// 得到一个曲线上按照指定距离划分后所有的法线
        /// </summary>
        /// <param name="curve">这个曲线</param>
        /// <param name="distanceToStartPoint">距离这个curve的起点多远距离开始生成法线</param>
        /// <param name="distanceOfTwoNormCurve">法线与法线之间的距离</param>
        /// <param name="NormCurveLength">生成的法线的长度</param>
        /// <returns></returns>
        public static List<Line> GetNormaLines(Curve curve, double distanceToStartPoint, double distanceOfTwoNormCurve, double NormCurveLength, int dir = 1)
        {
            List<Line> lines = new List<Line>();
            double myDis = 0;
            double curveLength = 0;
            if (curve is Line line)
            {
                curveLength = line.Length;


            }
            if (curve is Polyline polyline)
            {
                curveLength = polyline.Length;
            }
            // if (curveLength < distance) return null;
            while (myDis < curveLength)
            {
                lines.Add(GetUtil.GetNormLine(curve, myDis, NormCurveLength, dir));
                myDis = myDis + distanceOfTwoNormCurve;     // 间隔递增
            }

            return lines;



        }



        public static Polyline GetPolylineByOffset(Curve curve, double distance)
        {
            var xList1 = GeometryUtil.Offset(curve, distance);
            var xList2 = GeometryUtil.Offset(curve, -distance);
            var lineAddp1 = xList1[0].StartPoint.ToPoint2d();
            var lineAddp2 = xList1[0].EndPoint.ToPoint2d();
            var lineAddp3 = xList2[0].StartPoint.ToPoint2d(); ;
            var lineAddp4 = xList2[0].EndPoint.ToPoint2d(); ;
            Point2dCollection points = new Point2dCollection();
            points.Add(lineAddp1);
            points.Add(lineAddp2);
            points.Add(lineAddp4);
            points.Add(lineAddp3);
            Polyline ent = new Polyline();
            for (int i = 0; i < points.Count; i++)
            {
                ent.AddVertexAt(i, points[i], 0, 0, 0);
            }
            ent.Closed = true;
            return ent;

        }



    }

}
