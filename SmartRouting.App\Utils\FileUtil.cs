﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;
using System.Windows.Media;
using System.Windows.Forms;

namespace SmartRouting.App.Utils
{
    public class FileUtil
    {



        public static bool OpenDialog(out string path, string info = "请选择文件",string filter = "json文件(*.json)|*.json")
        {
            path = "";
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Title = info;
            //openFileDialog.Filter = "json文件(*.json)|*.json";
            openFileDialog.Filter = filter;
            openFileDialog.FilterIndex = 1;
            openFileDialog.Multiselect = false;
            if (openFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                path = openFileDialog.FileName;
                return true;
            }
            return false;
        }











        /// <summary>
        /// 将图片转化为资源
        /// </summary>
        /// <param name="imagePath"></param>
        /// <returns></returns>
        private WriteableBitmap GetImage(string imagePath)
        {
            Bitmap bitmap = new Bitmap(imagePath);

            // 将Bitmap转换为MemoryStream
            System.IO.MemoryStream stream = new System.IO.MemoryStream();
            bitmap.Save(stream, System.Drawing.Imaging.ImageFormat.Bmp);
            stream.Position = 0;

            // 创建WriteableBitmap
            BitmapImage bitmapImage = new BitmapImage();
            bitmapImage.BeginInit();
            bitmapImage.StreamSource = stream;
            bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
            bitmapImage.EndInit();

            // 将BitmapImage转换为WriteableBitmap
            WriteableBitmap writeableBitmap = new WriteableBitmap(bitmapImage);

            // 返回WriteableBitmap
            return writeableBitmap;
        }

      

        private static System.Windows.Controls.Image Bitmap2Image(System.Drawing.Bitmap Bi)
        {
            MemoryStream ms = new MemoryStream();
            Bi.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
            BitmapImage bImage = new BitmapImage();
            bImage.BeginInit();
            bImage.StreamSource = new MemoryStream(ms.ToArray());
            bImage.EndInit();
            ms.Dispose();
            Bi.Dispose();
            System.Windows.Controls.Image i = new System.Windows.Controls.Image();
            i.Source = bImage;
            return i;
        }



        public static System.Drawing.Bitmap ToBitmap(BitmapSource source)
        {

            if (source == null) return null;
            System.Drawing.Bitmap bitmap = new System.Drawing.Bitmap((int)source.Width, (int)source.Height);
            int width = bitmap.Width;
            int height = bitmap.Height;

            System.Drawing.Rectangle rect = new System.Drawing.Rectangle(0, 0, width, height);
            System.Drawing.Imaging.BitmapData bmpData = bitmap.LockBits(rect, System.Drawing.Imaging.ImageLockMode.ReadWrite, bitmap.PixelFormat);
            IntPtr ptr = bmpData.Scan0;

            byte[] rgbValues = BitmapImageToByteArray(source as BitmapImage);

            System.Runtime.InteropServices.Marshal.Copy(rgbValues, 0, ptr, rgbValues.Length);
            bitmap.UnlockBits(bmpData);

            return bitmap;

        }

        /// <summary>
        /// BitmapImage转数组
        /// </summary>
        /// <param name="bmp"></param>
        /// <returns></returns>
        public static byte[] BitmapImageToByteArray(BitmapSource bmp)
        {
            Int32 PixelHeight = bmp.PixelHeight; // 图像高度
            Int32 PixelWidth = bmp.PixelWidth;   // 图像宽度
            Int32 Stride = PixelWidth << 2;         // 扫描行跨距
            Byte[] Pixels = new Byte[PixelHeight * Stride];
            if (bmp.Format == PixelFormats.Bgr32 || bmp.Format == PixelFormats.Bgra32)
            {   // 拷贝像素数据
                bmp.CopyPixels(Pixels, Stride, 0);
            }
            else
            {   // 先进行像素格式转换，再拷贝像素数据
                new FormatConvertedBitmap(bmp, PixelFormats.Bgr32, null, 0).CopyPixels(Pixels, Stride, 0);
            }
            return Pixels;

        }




        /// <summary>
        /// 得到文件夹下所有的文件（如果有子文件夹就遍历）
        /// </summary>
        /// <param name="dir"></param>
        /// <param name="e"></param>
        /// <returns></returns>
        public static List<string> GetFilePath(DirectoryInfo dir, string e = ".jpg")
        {
            List<string> files = new List<string>();
            FileInfo[] allFile = dir.GetFiles();
            foreach (FileInfo fi in allFile)
            {
                if (fi.Extension.ToLower() == e)
                    files.Add(fi.FullName);
            }
            DirectoryInfo[] allDir = dir.GetDirectories();
            foreach (DirectoryInfo d in allDir)
            {
                GetFilePath(d);
            }
            return files;
        }









        /// <summary>
        /// 获取自定文件夹下所有的文件
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static string[] GetFilePath(string path)
        {
            if (Directory.Exists(path))
            {
                //文件路径
                string[] dir = Directory.GetDirectories(path);
                //文件名
                string[] names = new string[dir.Length];
                for (int i = 0; i < dir.Length; i++)
                {
                    //赋值文件命名
                    names[i] = Path.GetFileName(dir[i]);
                }
                return names;
            }
            else
            {

                return null;
            }
        }
    }
}
