﻿using SmartRouting.App.Commands.图层管理.Model;
using SmartRouting.App.Utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SmartRouting.App.Commands.图层管理.View
{
    /// <summary>
    /// DwgSettingView.xaml 的交互逻辑
    /// </summary>
    public partial class DwgSettingView : Window
    {
        public DwgSettingView()
        {
            InitializeComponent();
            Dg_LayerInfo.ItemsSource = layerModels;
            Dg_BlockInfo.ItemsSource = blockModels;
            Dg_LayerAndLine.ItemsSource = dataModels;

        }
        ObservableCollection<LayerModel> layerModels = new ObservableCollection<LayerModel>();
        ObservableCollection<BlockModel> blockModels = new ObservableCollection<BlockModel>();
        ObservableCollection<DataModel> dataModels = new ObservableCollection<DataModel>();
      //  string filePath = "C:\\Plugin_ForCAD\\Config\\ModelConfig.json";
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
           
            if (File.Exists(GlobalSetting.FilePath))
            {
                //读取之前的配置信息
                string json = File.ReadAllText(GlobalSetting.FilePath);
                var infos = JsonConvert.DeserializeObject<ConfigModel>(json);

                var dataModelConfigs = infos.DataModelConfigs;
                var layerModelConfigs = infos.LayerModelConfigs;
                var blockModelConfigs = infos.BlockModelConfigs;
                List<string> layerNames = new List<string>();
                List<string> blockNames = new List<string>();
                using (W.DocLock)
                {
                    W.FocusDoc();
                    var layers = LayerUtil.GetAllLayers();
                    foreach (var item in layers)
                    {
                        if (!layerNames.Equals(item))
                        {
                            layerNames.Add(item);
                        }
                    }

                    #region 图层管理
                    foreach (var item in layerModelConfigs)
                    {
                        LayerModel model1 = new LayerModel();
                        model1.LayerName = item.LayerName;
                        model1.IsFunc = item.IsFunc;
                        layerModels.Add(model1);
                    }
                    #endregion


                    #region 数据管理
                    foreach (var item in dataModelConfigs)
                    {
                        DataModel dataModel1 = new DataModel();
                        dataModel1.IsFunc = item.IsFunc;
                        dataModel1.LayerName = item.LayerName;
                        dataModel1.LineScale = item.LineScale;
                        dataModel1.LineStyle = item.LineStyle;
                        dataModel1.ColorIndex = item.ColorIndex;
                        dataModels.Add(dataModel1);
                    }
                    #endregion




                }


                using (W.DocLock)
                {
                    W.FocusDoc();
                    var referces = SelectUtil.SelectAllEntities<BlockReference>().Distinct(new EqualityBlockName()).ToList();
                    if (referces.Any())
                    {
                        foreach (var item in referces)
                        {
                            if (!blockNames.Equals(item.Name))
                            {
                                blockNames.Add(item.Name);
                            }
                        }

                        foreach (var item in blockModelConfigs)
                        {
                            BlockModel blockModel1 = new BlockModel();
                            blockModel1.BlockName = item.BlockName;
                            blockModel1.IsFunc = item.IsFunc;
                            blockModels.Add(blockModel1);
                        }
                    }
                }

                Cb_BlockName.ItemsSource = blockNames;
                Cb_LayerName.ItemsSource = layerNames;
                Cb_LayerAndLine.ItemsSource = layerNames;
                Cb_LineType.ItemsSource = W.Db.GetTotalLineTypeWithTransactions();













































            }
            else
            {
                List<string> layerNames = new List<string>();
                List<string> blockNames = new List<string>();
                using (W.DocLock)
                {
                    W.FocusDoc();
                    var layers = LayerUtil.GetAllLayers();
                    foreach (var item in layers)
                    {
                        if (!layerNames.Equals(item))
                        {
                            layerNames.Add(item);
                        }
                    }

                    #region 图层管理
                    LayerModel model1 = new LayerModel();
                    model1.LayerName = "TrafficInfo_A";
                    model1.IsFunc = true;

                    LayerModel model2 = new LayerModel();
                    model2.LayerName = "TrafficInfo_L";
                    model2.IsFunc = true;


                    LayerModel model3 = new LayerModel();
                    model3.LayerName = "TrafficInfo_P";
                    model3.IsFunc = true;
                    #endregion


                    #region
                    DataModel dataModel1 = new DataModel();
                    dataModel1.IsFunc = true;
                    dataModel1.LayerName = "40";
                    dataModel1.LineScale = 10;
                    dataModel1.LineStyle = "Continuous";
                    dataModel1.ColorIndex = 1;


                    DataModel dataModel2 = new DataModel();
                    dataModel2.IsFunc = true;
                    dataModel2.LayerName = "R_道路红线";
                    dataModel2.LineScale = 10;
                    dataModel2.LineStyle = "Continuous";
                    dataModel2.ColorIndex = 1;


                    DataModel dataModel3 = new DataModel();
                    dataModel3.IsFunc = true;
                    dataModel3.LayerName = "R_道路中心线";
                    dataModel3.LineScale = 10;
                    dataModel3.LineStyle = "DASHDOT";
                    dataModel3.ColorIndex = 251;

                    DataModel dataModel4 = new DataModel();
                    dataModel4.IsFunc = true;
                    dataModel4.LayerName = "41";
                    dataModel4.LineScale = 10;
                    dataModel4.LineStyle = "DASHDOT";
                    dataModel4.ColorIndex = 251;


                    DataModel dataModel5 = new DataModel();
                    dataModel5.IsFunc = true;
                    dataModel5.LayerName = "Road_Edge_Regular_L";
                    dataModel5.LineScale = 1;
                    dataModel5.LineStyle = "0CSX";
                    dataModel5.ColorIndex = 256;







                    #endregion

                    layerModels.Add(model1);
                    layerModels.Add(model2);
                    layerModels.Add(model3);



                    dataModels.Add(dataModel1);
                    dataModels.Add(dataModel2);
                    dataModels.Add(dataModel3);
                    dataModels.Add(dataModel4);
                    dataModels.Add(dataModel5);
                }


                using (W.DocLock)
                {
                    W.FocusDoc();
                    var referces = SelectUtil.SelectAllEntities<BlockReference>().Distinct(new EqualityBlockName()).ToList();
                    if (referces.Any())
                    {
                        foreach (var item in referces)
                        {
                            if (!blockNames.Equals(item.Name))
                            {
                                blockNames.Add(item.Name);
                            }
                        }

                        BlockModel blockModel1 = new BlockModel();
                        blockModel1.BlockName = "1_500";
                        blockModel1.IsFunc = true;
                        BlockModel blockModel2 = new BlockModel();
                        blockModel2.BlockName = "2_500";
                        blockModel2.IsFunc = true;

                        BlockModel blockModel3 = new BlockModel();
                        blockModel3.BlockName = "3_500";
                        blockModel3.IsFunc = true;

                        BlockModel blockModel4 = new BlockModel();
                        blockModel4.BlockName = "4_500";
                        blockModel4.IsFunc = true;

                        BlockModel blockModel5 = new BlockModel();
                        blockModel5.BlockName = "0tc_500";
                        blockModel5.IsFunc = true;

                        BlockModel blockModel6 = new BlockModel();
                        blockModel6.BlockName = "2tc_500";
                        blockModel6.IsFunc = true;


                        BlockModel blockModel7 = new BlockModel();
                        blockModel7.BlockName = "4tc_500";
                        blockModel7.IsFunc = true;

                        BlockModel blockModel8 = new BlockModel();
                        blockModel8.BlockName = "500框";
                        blockModel8.IsFunc = true;

                        BlockModel blockModel9 = new BlockModel();
                        blockModel9.BlockName = "luoma3_500";
                        blockModel9.IsFunc = true;


                        blockModels.Add(blockModel1);
                        blockModels.Add(blockModel2);
                        blockModels.Add(blockModel3);
                        blockModels.Add(blockModel4);
                        blockModels.Add(blockModel5);
                        blockModels.Add(blockModel6);
                        blockModels.Add(blockModel7);
                        blockModels.Add(blockModel8);
                        blockModels.Add(blockModel9);

                    }
                }

                Cb_BlockName.ItemsSource = blockNames;
                Cb_LayerName.ItemsSource = layerNames;
                Cb_LayerAndLine.ItemsSource = layerNames;
                Cb_LineType.ItemsSource = W.Db.GetTotalLineTypeWithTransactions();

            }




















        }

        private void CheckBox_Click(object sender, RoutedEventArgs e)
        {

        }



        private void Btn_CloseLayer_Click(object sender, RoutedEventArgs e)
        {
            //关闭图层
            var myModels = layerModels.Where(x => x.IsFunc == true && !string.IsNullOrEmpty(x.LayerName)).ToList();
            using (W.DocLock)
            {
                W.FocusDoc();
                foreach (var item in myModels)
                {
                    try
                    {
                        LayerUtil.TurnLayerIsFrozen(item.LayerName);
                    }
                    catch (Exception ex)
                    {
                        W.Ed.WriteMessage("当前图层无法关闭!");
                        continue;
                    }

                }
                //  W.Ed.WriteMessage("操作完毕!");
            }
        }

        private void Btn_LineThinkness_Click(object sender, RoutedEventArgs e)
        {
            using (W.DocLock)
            {
                W.FocusDoc();
                var polylines = SelectUtil.SelectAllEntities<Curve>(x => x is CadDb. Polyline || x is Polyline2d);
                using (Transaction tran = W.Db.TransactionManager.StartTransaction())
                {

                    foreach (var item in polylines)
                    {
                        if (item is CadDb. Polyline polyline)
                        {
                           CadDb.Polyline tmpPline = tran.GetObject(polyline.Id, OpenMode.ForWrite) as CadDb.Polyline;
                            tmpPline.ConstantWidth = Tb_LineThinkness.Text.StringToDouble();
                        }
                        if (item is Polyline2d polyline2d)
                        {
                            CadDb.Polyline2d tmpPline = tran.GetObject(polyline2d.Id, OpenMode.ForWrite) as Polyline2d;
                            tmpPline.ConstantWidth = Tb_LineThinkness.Text.StringToDouble();
                        }
                    }

                    tran.Commit();
                    // W.Ed.WriteMessage("操作完毕!");
                }

            }
        }

        private void Btn_TotalEntitiesLineAndLayerChange_Click(object sender, RoutedEventArgs e)
        {
            using (W.DocLock)
            {
                W.FocusDoc();

                using (Transaction tran = W.Db.TransactionManager.StartTransaction())
                {
                    var entities = SelectUtil.SelectAllEntities<Entity>().ToList();
                    foreach (var item in entities)
                    {
                        var tmpEntity = tran.GetObject(item.Id, OpenMode.ForWrite) as Entity;
                        tmpEntity.ColorIndex = 256;
                        tmpEntity.LineWeight = LineWeight.ByLayer;

                    }

                    tran.Commit();
                    // W.Ed.WriteMessage("操作完毕!");
                }

            }
        }

        private void Btn_FontEdit_Click(object sender, RoutedEventArgs e)
        {
            List<string> arrayList = new List<string>();
            using (W.DocLock)
            {
                W.FocusDoc();

                using (Transaction tran = W.Db.TransactionManager.StartTransaction())
                {
                    // W.Db.AddTextStyle("newTextStyle","txt.shx","chinese.shx");
                    //打开文字样式表
                    TextStyleTable st = (TextStyleTable)W.Db.TextStyleTableId.GetObject(OpenMode.ForWrite);
                    foreach (var item in st)
                    {
                        TextStyleTableRecord textStyleTableRecord = tran.GetObject(item, OpenMode.ForWrite) as TextStyleTableRecord;
                        textStyleTableRecord.FileName = "txt.shx";
                        textStyleTableRecord.BigFontFileName = "chinese.shx";
                    }
                    tran.Commit();
                    //  W.Ed.WriteMessage("操作完毕!");
                }

            }

        }

        private void Btn_DelectBlock_Click(object sender, RoutedEventArgs e)
        {
            using (W.DocLock)
            {
                W.FocusDoc();

                using (Transaction tran = W.Db.TransactionManager.StartTransaction())
                {
                    foreach (var model in blockModels)
                    {
                        if (model.IsFunc == true && !string.IsNullOrEmpty(model.BlockName))
                        {
                            var referces = SelectUtil.SelectAllEntities<BlockReference>(x => x.Name.Equals(model.BlockName)).ToList();
                            referces.EraseEntities();
                        }
                    }

                    tran.Commit();
                    //  W.Ed.WriteMessage("操作完毕!");
                }

            }
        }

        private void Btn_DelectLayer_Click(object sender, RoutedEventArgs e)
        {
            using (W.DocLock)
            {
                W.FocusDoc();

                using (Transaction tran = W.Db.TransactionManager.StartTransaction())
                {
                    foreach (var model in layerModels)
                    {
                        if (model.IsFunc == true && !string.IsNullOrEmpty(model.LayerName))
                        {
                            var referces = SelectUtil.SelectAllEntities<Entity>(x => x.Layer.Equals(model.LayerName)).ToList();
                            referces.EraseEntities();
                        }
                    }

                    tran.Commit();
                    //  W.Ed.WriteMessage("操作完毕!");
                }

            }
        }

        private void Btn_SetColorIndexTo8_Click(object sender, RoutedEventArgs e)
        {
            using (W.DocLock)
            {
                W.FocusDoc();


                using (Transaction tran = W.Trans)
                {
                    var lts = LayerUtil.GetAllLayersWithOutTransaction(W.Db).ToList();
                    foreach (LayerTableRecord item in lts)
                    {
                        var tmpEntity = tran.GetObject(item.Id, OpenMode.ForWrite) as LayerTableRecord;
                        tmpEntity.Color =Cad.Colors.Color.FromColorIndex(Cad.Colors.ColorMethod.ByAci, 8);
                    }
                    tran.Commit();
                    // W.Ed.WriteMessage("操作完毕!");
                }

            }
        }

        private void Btn_LayerAndLine_Click(object sender, RoutedEventArgs e)
        {
            using (W.DocLock)
            {
                W.FocusDoc();

                using (Transaction tran = W.Db.TransactionManager.StartTransaction())
                {
                    foreach (var model in dataModels)
                    {
                        if (model.IsFunc == true && !string.IsNullOrEmpty(model.LayerName))
                        {
                            var entities = SelectUtil.SelectAllEntities<Entity>(x => x.Layer.Equals(model.LayerName)).ToList();
                            foreach (var item in entities)
                            {
                                var tmpEntity = tran.GetObject(item.Id, OpenMode.ForWrite) as Entity;
                                tmpEntity.ColorIndex = model.ColorIndex;
                                tmpEntity.Linetype = model.LineStyle;
                                tmpEntity.LinetypeScale = model.LineScale;

                            }


                        }
                    }

                    tran.Commit();
                    // W.Ed.WriteMessage("操作完毕!");
                }

            }
        }

        /// <summary>
        /// 一键修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_ModifyByOneButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Btn_SetColorIndexTo8_Click(null, null);//修改为8号色
                Btn_LayerAndLine_Click(null, null);//线型修改
                Btn_DelectLayer_Click(null, null);//删除图层
                Btn_DelectBlock_Click(null, null);//删除块
                Btn_FontEdit_Click(null, null);//文字样式修改

                Btn_LineThinkness_Click(null, null);//线宽修改
                Btn_CloseLayer_Click(null, null);//图层关闭
                Btn_TotalEntitiesLineAndLayerChange_Click(null, null);//数据样式
            }
            catch (Exception ex)
            {

                MessageBoxUtil.Message_Error(ex.StackTrace);
            }

        }

        /// <summary>
        /// 保存配置文件信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_SaveConfig_Click(object sender, RoutedEventArgs e)
        {
            ConfigModel configModel = new ConfigModel();
            List<BlockModelConfig> blockModelConfigs = new List<BlockModelConfig>();
            List<LayerModelConfig> layerModelConfigs = new List<LayerModelConfig>();
            List<DataModelConfig> dataModelConfigs = new List<DataModelConfig>();
            foreach (var item in blockModels)
            {
                BlockModelConfig blockModelConfig = new BlockModelConfig();
                blockModelConfig.BlockName = item.BlockName;
                blockModelConfig.IsFunc = item.IsFunc;
                blockModelConfigs.Add(blockModelConfig);
            }
            foreach (var item in layerModels)
            {
                LayerModelConfig layerModelConfig = new LayerModelConfig();
                layerModelConfig.IsFunc = item.IsFunc;
                layerModelConfig.LayerName = item.LayerName;
                layerModelConfigs.Add(layerModelConfig);

            }

            foreach (var item in dataModels)
            {
                DataModelConfig datamodelconfig = new DataModelConfig();
                datamodelconfig.IsFunc = item.IsFunc;
                datamodelconfig.LayerName = item.LayerName;
                datamodelconfig.ColorIndex = item.ColorIndex;
                datamodelconfig.LineScale = item.LineScale;
                datamodelconfig.LineStyle = item.LineStyle;
                dataModelConfigs.Add(datamodelconfig);
            }
            configModel.BlockModelConfigs = blockModelConfigs;
            configModel.LayerModelConfigs = layerModelConfigs;
            configModel.DataModelConfigs = dataModelConfigs;
            string toJson = JsonConvert.SerializeObject(configModel,Formatting.Indented);
            File.WriteAllText(GlobalSetting.FilePath, toJson, System.Text.Encoding.UTF8);
            
        }
    }

    internal class EqualityBlockName : IEqualityComparer<BlockReference>
    {
        public bool Equals(BlockReference x, BlockReference y)
        {
            return x.Name.Equals(y.Name);
        }

        public int GetHashCode(BlockReference obj)
        {
            return 1;
        }
    }
}
