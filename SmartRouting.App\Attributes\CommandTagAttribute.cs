﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Attributes
{
    [AttributeUsage(AttributeTargets.Method)]
    public class CommandTagAttribute : Attribute
    {
        public string Type { get; set; }//类型，如结构 建筑等
        public string Description { get; set; }//快捷键介绍
        public string Cmd { get; set; }//快捷键名称
        public string ToolTip { get; set; }//提示
        public CommandTagAttribute(string type, string cmd, string description, string toolTip = "")
        {
            this.Type = type;
            this.Cmd = cmd;
            this.Description = description;
            this.ToolTip = string.IsNullOrEmpty(toolTip) ? description : toolTip;
        }
    }
}
