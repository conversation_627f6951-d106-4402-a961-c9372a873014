﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.线路连接.Model
{
    public class JoinTotalLinesModel : BindableBase
    {
        /// <summary>
        /// 块名
        /// </summary>
        public string BlockName
        {
            get => GetProperty(() => BlockName);
            set => SetProperty(() => BlockName, value);
        }

        /// <summary>
        /// 是否打断
        /// </summary>
        public bool IsBreak
        {
            get => GetProperty(() => IsBreak);
            set => SetProperty(() => IsBreak, value);
        }
    }
}
