﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Common.GeneralDuplicate
{
    /// <summary>
    /// 直线去重比较器
    /// </summary>
    public class EqualityLine : IEqualityComparer<Line>
    {  /// <summary>
       /// 判断点是否在直线上
       /// </summary>
       /// <param name="pf"></param>
       /// <param name="p1"></param>
       /// <param name="p2"></param>
       /// <param name="range">判断的的误差，不需要误差则赋值0</param>
       /// <returns></returns>
        public bool GetPointIsInLine(PointF pf, PointF p1, PointF p2, double range = 1)
        {
            //range 判断的的误差，不需要误差则赋值0
            //点在线段首尾两端之外则return false
            double cross = (p2.X - p1.X) * (pf.X - p1.X) + (p2.Y - p1.Y) * (pf.Y - p1.Y);
            if (cross <= 0) return false;
            double d2 = (p2.X - p1.X) * (p2.X - p1.X) + (p2.Y - p1.Y) * (p2.Y - p1.Y);
            if (cross >= d2) return false;
            double r = cross / d2;
            double px = p1.X + (p2.X - p1.X) * r;
            double py = p1.Y + (p2.Y - p1.Y) * r;
            //判断距离是否小于误差
            return Math.Sqrt((pf.X - px) * (pf.X - px) + (py - pf.Y) * (py - pf.Y)) <= range;
        }
        const double eee = 1e-4;
        public bool Equals(Line line1, Line line2)
        {

            bool start = line1.StartPoint.Equals(line2.StartPoint);
            bool end = line1.EndPoint.Equals(line2.EndPoint);
            bool start1 = line1.StartPoint.Equals(line2.EndPoint);
            bool end2 = line1.EndPoint.Equals(line2.StartPoint);
            var PS1 = new PointF((float)line1.StartPoint.X, (float)line1.StartPoint.Y);
            var PE1 = new PointF((float)line1.EndPoint.X, (float)line1.EndPoint.Y);

            var PS2 = new PointF((float)line2.StartPoint.X, (float)line2.StartPoint.Y);
            var PE2 = new PointF((float)line2.EndPoint.X, (float)line2.EndPoint.Y);
            //  MessageBox.Show("AAAAA");

            if ((line1.Length < line2.Length) && ((GetPointIsInLine(PS1, PS2, PE2)) == true && (GetPointIsInLine(PE1, PS2, PE2)) == true))
            {
                // MessageBoxUtil.MessageOutPutLog(line1.StartPoint+"___"+ line1.EndPoint);
                DelEntity(line1.ObjectId);


                //  line1.Erase();
                return true;
            }
            if ((line2.Length < line1.Length) && ((GetPointIsInLine(PS2, PS1, PE1)) == true && (GetPointIsInLine(PE2, PS1, PE1)) == true))
            {
                //MessageBoxUtil.MessageOutPutLog(line2.StartPoint + "___" + line2.EndPoint);
                DelEntity(line2.ObjectId);
                // line2.Erase();
                return true;
            }

            if ((start && end) || (start1 && end2) == true)
            {
                //MessageBoxUtil.MessageOutPutLog(line2.StartPoint + "___" + line2.EndPoint);
                DelEntity(line2.ObjectId);
                // line2.Erase();
                return true;
            }


            return (start && end) || (start1 && end2);
        }

        public int GetHashCode(Line line)
        {
            // MessageBox.Show("BBB");
            return 1;
            // return line.StartPoint.GetHashCode() + line.EndPoint.GetHashCode();
        }
        public bool DelEntity(ObjectId id)
        {
            try
            {

                if (!id.IsNull)
                {

                    using (Database db = HostApplicationServices.WorkingDatabase)
                    {

                        using (Transaction trans = db.TransactionManager.StartTransaction())
                        {

                            Entity entity = (Entity)trans.GetObject(id, OpenMode.ForWrite, true);

                            entity.Erase(true);

                            trans.Commit();
                        }
                    }
                }
                else
                {
                    return false;
                }
            }
            catch
            {

                return false;
            }
            return true;
        }
    }


}
