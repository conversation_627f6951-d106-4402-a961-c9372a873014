﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.图层管理.Model
{
    public class LayerModel : BindableBase
    {
        /// <summary>
        /// 图层名
        /// </summary>
        public string LayerName
        {
            get => GetProperty(() => LayerName);
            set => SetProperty(() => LayerName, value);
        }
        public bool IsFunc
        {
            get => GetProperty(() => IsFunc);
            set => SetProperty(() => IsFunc, value);
        }
        
    }
}
