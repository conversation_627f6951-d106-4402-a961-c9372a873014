﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="SmartRouting.App.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <userSettings>
        <SmartRouting.App.Properties.Settings>
            <setting name="线图层" serializeAs="String">
                <value>0</value>
            </setting>
            <setting name="ExcelPath" serializeAs="String">
                <value>c:\</value>
            </setting>
            <setting name="LastCodes" serializeAs="String">
                <value />
            </setting>
            <setting name="Prompt" serializeAs="String">
                <value />
            </setting>
            <setting name="ApiKey" serializeAs="String">
                <value>请输入您的ApiKey</value>
            </setting>
            <setting name="工井照片" serializeAs="String">
                <value />
            </setting>
            <setting name="SS" serializeAs="String">
                <value />
            </setting>
        </SmartRouting.App.Properties.Settings>
    </userSettings>
</configuration>