﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.图层管理.Model
{
    public class BlockModel : BindableBase
    {
        /// <summary>
        /// 块名
        /// </summary>
        public string BlockName
        {
            get => GetProperty(() => BlockName);
            set => SetProperty(() => BlockName, value);
        }
        public bool IsFunc
        {
            get => GetProperty(() => IsFunc);
            set => SetProperty(() => IsFunc, value);
        }

    }
}
