﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    public class LayerUtil
    {


        /// <summary>
        /// 获取当前图形中所有的图层
        /// </summary>
        /// <param name="db">数据库对象</param>
        /// <returns>返回所有的层表记录</returns>
        public static List<LayerTableRecord> GetAllLayers(Database db)
        {
            //打开层表
            LayerTable lt = (LayerTable)db.LayerTableId.GetObject(OpenMode.ForRead);
            //用于返回层表记录的列表
            List<LayerTableRecord> ltrs = new List<LayerTableRecord>();
            foreach (ObjectId id in lt)//遍历层表
            {
                //打开层表记录
                LayerTableRecord ltr = (LayerTableRecord)id.GetObject(OpenMode.ForRead);
                ltrs.Add(ltr);//添加到返回列表中
            }
            return ltrs;//返回所有的层表记录
        }








        /// <summary>
        /// 转换到当前图层
        /// </summary>
        /// <param name="layerName"></param>
        /// <returns></returns>
        public static bool SwitchCurrentLayer(string layerName)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor editor = doc.Editor;
            using (W.Doc.LockDocument())
            {
                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    LayerTable layerTable = tr.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                    if (layerTable.Has(layerName))
                    {
                        LayerTableRecord layer = tr.GetObject(layerTable[layerName], OpenMode.ForWrite) as LayerTableRecord;
                        //  editor.WriteMessage($"Switching to layer: {layerName}\n");
                        db.Clayer = layer.ObjectId;
                    }
                    else
                    {
                        return false;
                    }

                    tr.Commit();
                }
            }
            return true;
        }
        /// <summary>
        /// 新建图层
        /// </summary>
        /// <param name="layername"></param>
        /// <param name="color"></param>
        /// <param name="IsPlottable"></param>

        public static void CreateLayer(string layername, short color, bool IsPlottable)
        {
            Database workingDatabase = HostApplicationServices.WorkingDatabase;

            using (W.Doc.LockDocument())
            {
                using (Transaction transaction = workingDatabase.TransactionManager.StartTransaction())
                {
                    LayerTable layerTable = transaction.GetObject(workingDatabase.LayerTableId, OpenMode.ForWrite) as LayerTable;
                    LayerTableRecord layerTableRecord;
                    if (!layerTable.Has(layername))
                    {
                        layerTableRecord = new LayerTableRecord();
                        layerTableRecord.Name = layername;
                        layerTableRecord.Color = Color.FromColorIndex(ColorMethod.ByAci, color);
                        layerTableRecord.IsPlottable = IsPlottable;
                        layerTable.UpgradeOpen();
                        layerTable.Add(layerTableRecord);
                        transaction.AddNewlyCreatedDBObject(layerTableRecord, true);
                    }
                    else
                    {
                        layerTableRecord = transaction.GetObject(layerTable[layername], OpenMode.ForWrite) as LayerTableRecord;
                        layerTableRecord.Color = Color.FromColorIndex(ColorMethod.ByAci, color);
                        layerTableRecord.IsPlottable = IsPlottable;
                    }
                    try
                    {
                        workingDatabase.Clayer = layerTable[layername];
                        transaction.Commit();

                    }
                    catch (System.Exception)
                    {


                    }


                }
            }
        }



        /// <summary>
        /// 删除图层
        /// </summary>
        /// <param name="layerName"></param>

        public static void EraseLayer(string layerName) //删除图层
        {
            Document document = Application.DocumentManager.MdiActiveDocument;
            Database database = document.Database;
            using (Transaction transaction = database.TransactionManager.StartTransaction())
            {
                LayerTable layerTable = transaction.GetObject(database.LayerTableId, OpenMode.ForRead) as LayerTable;
                string sLayerName = layerName;
                if (layerTable.Has(sLayerName) == true)
                {
                    ObjectIdCollection objectIdCollection = new ObjectIdCollection();
                    objectIdCollection.Add(layerTable[sLayerName]);
                    //database.Purge(objectIdCollection);
                    if (objectIdCollection.Count > 0)
                    {
                        LayerTableRecord layerTableRecord = transaction.GetObject(objectIdCollection[0], OpenMode.ForWrite) as LayerTableRecord;
                        try
                        {
                            layerTableRecord.Erase(true);
                            transaction.Commit();
                        }
                        catch (System.Exception)
                        {
                            //Application.ShowAlertDialog("Error:\n" + ex.Message);
                        }
                    }
                }
            }
        }


        /// <summary>
        /// 获取当前图形中所有的图层
        /// </summary>
        /// <param name="db">数据库对象</param>
        /// <returns>返回所有的层表记录</returns>
        public static List<LayerTableRecord> GetAllLayersWithOutTransaction(Database db)
        {

            //打开层表
            LayerTable lt = (LayerTable)db.LayerTableId.GetObject(OpenMode.ForRead);
            //用于返回层表记录的列表

            List<LayerTableRecord> ltrs = new List<LayerTableRecord>();

            foreach (ObjectId id in lt)//遍历层表
            {
                //打开层表记录

                LayerTableRecord ltr = (LayerTableRecord)id.GetObject(OpenMode.ForRead);

                ltrs.Add(ltr);//添加到返回列表中

            }
            return ltrs;//返回所有的层表记录
        }

        /// <summary>
        /// 关闭指定的图层
        /// </summary>
        /// <param name="slayer">冻结的图层名</param>
        public static void TurnLayerIsFrozen(string slayer)
        {
         
            using (Transaction trans =W.Trans)
            {
                LayerTable layerTable;
                layerTable = trans.GetObject(W.Db.LayerTableId, OpenMode.ForWrite) as LayerTable;
                LayerTableRecord layerTableRecord = trans.GetObject(layerTable[slayer], OpenMode.ForWrite) as LayerTableRecord;
                layerTableRecord.IsFrozen = true;//冻结
                trans.Commit();
                W.DocLock.Dispose();
            }
          CadApp.UpdateScreen();//及时刷新窗体
        }

        /// <summary>
        // 关闭指定的图层
        /// </summary>
        /// <param name="slayer"></param>
        public static void TurnLayerIsOff(string slayer)
        {

            using (Transaction trans = W.Trans)
            {
                LayerTable layerTable;
                layerTable = trans.GetObject(W.Db.LayerTableId, OpenMode.ForWrite) as LayerTable;
                LayerTableRecord layerTableRecord = trans.GetObject(layerTable[slayer], OpenMode.ForWrite) as LayerTableRecord;
                layerTableRecord.IsOff = true;//关闭
                trans.Commit();
                W.DocLock.Dispose();
            }
           // CadApp.UpdateScreen();//及时刷新窗体
        }



        /// <summary>
        // 打开指定的图层
        /// </summary>
        /// <param name="slayer"></param>
        public static void TurnLayerIsOn(string slayer)
        {

            using (Transaction trans = W.Trans)
            {
                LayerTable layerTable;
                layerTable = trans.GetObject(W.Db.LayerTableId, OpenMode.ForWrite) as LayerTable;
                LayerTableRecord layerTableRecord = trans.GetObject(layerTable[slayer], OpenMode.ForWrite) as LayerTableRecord;
                layerTableRecord.IsOff = false;//关闭
                trans.Commit();
                W.DocLock.Dispose();
            }
            CadApp.UpdateScreen();//及时刷新窗体
        }


        public static List<string> GetAllLayers()//得到模型空间中所有的图层信息
        {
            List<string> allLayers = new List<string>();
            Document doc = CadApp.DocumentManager.MdiActiveDocument;
            Database db = HostApplicationServices.WorkingDatabase;
            Editor ed = doc.Editor;
            using (var trans = db.TransactionManager.StartTransaction())
            {
                LayerTable lt = (LayerTable)trans.GetObject(db.LayerTableId, OpenMode.ForRead) as LayerTable;
                foreach (ObjectId id in lt)
                {
                    LayerTableRecord ltr = (LayerTableRecord)trans.GetObject(id, OpenMode.ForRead) as LayerTableRecord;
                    allLayers.Add(ltr.Name);
                }
                trans.Commit();
                HashSet<string> hs = new HashSet<string>(allLayers);//hs为去重后的数据
                return hs.ToList();//模型空间中所有的图层信息
            }
        }

    }
}
