﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.常用.扩展数据显示.Model
{

    // WPF数据项类
    public class XDataItem
    {
        public string Application { get; set; }
        public string TypeCode { get; set; }
        public string Value { get; set; }

        public XDataItem(string app, int typeCode, object value)
        {
            Application = app;
            TypeCode = typeCode.ToString();
            Value = FormatValue(value);
        }

        private string FormatValue(object value)
        {
            if (value is double[] array)
                return string.Join(", ", array);

            return value?.ToString() ?? "null";
        }
    }

}
