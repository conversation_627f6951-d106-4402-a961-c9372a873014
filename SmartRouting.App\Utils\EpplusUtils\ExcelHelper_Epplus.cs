﻿using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils.EpplusUtils
{
    public class ExcelHelper_Epplus
    {

        /// <summary>
        /// 创建CAD里面的表
        /// </summary>
        /// <param name="db"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static Table CreateAcadTable(Database db, List<List<string>> data)
        {
            Table tb = new Table();
            tb.SetDatabaseDefaults(db);
            // 增加一个健壮性检查，防止传入空数据时出错
            if (data == null || data.Count == 0)
            {
                return tb; // 返回一个空的表格对象
            }
            int numRows = data.Count;
            int numCols = data[0].Count;
            tb.SetSize(numRows, numCols);
            tb.SetRowHeight(800);    // 设置默认行高
            tb.SetColumnWidth(2000); // 设置默认列宽
                                     // 新增下面这行来全局设置文字高度
            tb.Cells.TextHeight = 300; // 将所有单元格的文字高度统一设置为 300
            // 填充数据
            for (int i = 0; i < numRows; i++)
            {
                for (int j = 0; j < numCols; j++)
                {
                    tb.Cells[i, j].TextString = data[i][j];
                    tb.Cells[i, j].Alignment = CellAlignment.MiddleCenter;
                }
            }
            return tb;
        }
















        /// <summary>
        /// (新方法) 获取Excel文件中的所有工作表名称
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <returns>工作表名称列表</returns>
        public static List<string> GetSheetNames(string filePath)
        {
            var sheetNames = new List<string>();
            var fileInfo = new FileInfo(filePath);

            if (!fileInfo.Exists)
            {
                MessageBoxUtil.Message_Error($"文件 '{filePath}' 未找到。");
                return sheetNames;
            }

            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(fileInfo))
                {
                    foreach (var worksheet in package.Workbook.Worksheets)
                    {
                        sheetNames.Add(worksheet.Name);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBoxUtil.Message_Error($"读取 Excel 文件时发生错误: {ex.Message}");
            }

            return sheetNames;
        }


        /// <summary>
        /// 读取excel一行一行的数据
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="sheetName"></param>
        /// <returns></returns>
        public static List<List<string>> ReadSheetData(string filePath, string sheetName, int userStartRow, int userStartCol, int userEndCol)
        {
            // System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

            var allRowsData = new List<List<string>>();
            FileInfo fileInfo = new FileInfo(filePath);

            if (!fileInfo.Exists)
            {
                MessageBoxUtil.Message_Error($"文件 '{filePath}' 未找到。");
                return allRowsData;
            }

            if (userStartRow <= 0)
            {
                MessageBoxUtil.Message_Error($"指定的起始行 ({userStartRow}) 无效，必须大于 0。");
                return allRowsData;
            }
            if (userStartCol <= 0)
            {
                MessageBoxUtil.Message_Error($"指定的起始列 ({userStartCol}) 无效，必须大于 0。");
                return allRowsData;
            }
            if (userEndCol < userStartCol)
            {
                MessageBoxUtil.Message_Error($"指定的结束列 ({userEndCol}) 不能小于起始列 ({userStartCol})。");
                return allRowsData;
            }

            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using (var package = new ExcelPackage(fileInfo))
                {
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[sheetName];
                    if (worksheet == null)
                    {
                        MessageBoxUtil.Message_Error($"在文件 '{filePath}' 中未找到名为 '{sheetName}' 的工作表。");
                        return allRowsData;
                    }

                    int actualSheetStartRow = worksheet.Dimension?.Start?.Row ?? 1;
                    int actualSheetEndRow = worksheet.Dimension?.End?.Row ?? 0;
                    int actualSheetStartCol = worksheet.Dimension?.Start?.Column ?? 1;
                    int actualSheetEndCol = worksheet.Dimension?.End?.Column ?? 0;

                    if (actualSheetEndRow == 0 || actualSheetEndCol == 0)
                    {
                        MessageBoxUtil.Message_Error($"工作表 '{sheetName}' 为空或没有有效的维度信息。");
                        return allRowsData;
                    }

                    int effectiveStartRow = Math.Max(userStartRow, actualSheetStartRow);
                    if (effectiveStartRow > actualSheetEndRow)
                    {
                        MessageBoxUtil.Message_Error($"指定的起始行 ({userStartRow}) 超出了工作表 '{sheetName}' 的实际数据范围 (最大行: {actualSheetEndRow})。");
                        return allRowsData;
                    }

                    // 行的结束是工作表的实际结束行
                    int effectiveEndRow = actualSheetEndRow;

                    int effectiveStartCol = Math.Max(userStartCol, actualSheetStartCol);
                    if (effectiveStartCol > actualSheetEndCol)
                    {
                        MessageBoxUtil.Message_Error($"指定的起始列 ({userStartCol}) 超出了工作表 '{sheetName}' 的实际数据范围 (最大列: {actualSheetEndCol})。");
                        return allRowsData;
                    }

                    // int maxExcelCol = OfficeOpenXml.Core.ExcelConstants.MaxColumns; // 旧行
                    int maxExcelCol = 16384; //  <-------------------- 修改点
                    int effectiveEndCol = Math.Min(userEndCol, actualSheetEndCol);
                    effectiveEndCol = Math.Min(effectiveEndCol, maxExcelCol);

                    if (effectiveStartRow > effectiveEndRow || effectiveStartCol > effectiveEndCol)
                    {
                        MessageBoxUtil.Message_Error($"根据工作表 '{sheetName}' 的实际维度调整后，指定的读取范围无效。Effective Range: Rows {effectiveStartRow}-{effectiveEndRow}, Cols {effectiveStartCol}-{effectiveEndCol}");
                        return allRowsData;
                    }

                    Console.WriteLine($"调试信息：用户请求范围 行:{userStartRow}-N/A, 列:{userStartCol}-{userEndCol}");
                    Console.WriteLine($"调试信息：工作表实际维度 行:{actualSheetStartRow}-{actualSheetEndRow}, 列:{actualSheetStartCol}-{actualSheetEndCol}");
                    Console.WriteLine($"调试信息：将要读取的有效范围 行:{effectiveStartRow}-{effectiveEndRow}, 列:{effectiveStartCol}-{effectiveEndCol}");

                    for (int rowNum = effectiveStartRow; rowNum <= effectiveEndRow; rowNum++)
                    {
                        var currentRowData = new List<string>();
                        for (int colNum = effectiveStartCol; colNum <= effectiveEndCol; colNum++)
                        {
                            var cellValue = worksheet.Cells[rowNum, colNum].Value;
                            currentRowData.Add(cellValue?.ToString() ?? string.Empty);
                        }
                        allRowsData.Add(currentRowData);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBoxUtil.Message_Error($"读取 Excel 文件时发生错误: {ex.Message}{Environment.NewLine}堆栈: {ex.StackTrace}");
            }

            return allRowsData;
        }
        public static void WriteSheetData(string filePath, string sheetName, List<List<string>> dataToWrite, string newSheetNameIfCreating = "NewSheet")
        {
            // System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance); // 如果需要
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial; // 或 LicenseContext.Commercial

            FileInfo fileInfo = new FileInfo(filePath);

            using (var package = new ExcelPackage(fileInfo))
            {
                ExcelWorksheet worksheet;
                if (package.Workbook.Worksheets[sheetName] != null)
                {
                    worksheet = package.Workbook.Worksheets[sheetName];
                    // 如果需要，可以在这里添加逻辑来决定是覆盖、追加还是其他操作
                    // 为简单起见，这里我们假设如果工作表存在，就直接从第一行第一列开始写入，会覆盖原有数据
                    worksheet.Cells.Clear(); // 清除现有数据（可选）
                }
                else
                {
                    // 如果工作表不存在，则创建一个新的
                    worksheet = package.Workbook.Worksheets.Add(string.IsNullOrEmpty(newSheetNameIfCreating) ? sheetName : newSheetNameIfCreating);
                }

                if (dataToWrite == null || dataToWrite.Count == 0)
                {
                    MessageBoxUtil.MessageInformation("没有要写入的数据。");
                    // 如果文件是新创建的，但没有数据写入，你可能还是想保存一个空的工作簿
                    // package.Save();
                    return;
                }

                // 从第一行第一列开始写入数据
                for (int i = 0; i < dataToWrite.Count; i++)
                {
                    for (int j = 0; j < dataToWrite[i].Count; j++)
                    {
                        // EPPlus 的行和列索引是从1开始的
                        worksheet.Cells[i + 1, j + 1].Value = dataToWrite[i][j];
                    }
                }

                try
                {
                    package.Save(); // 保存更改到文件
                    MessageBoxUtil.MessageInformation($"数据成功写入到文件 '{filePath}' 的工作表 '{worksheet.Name}'。");
                }
                catch (InvalidOperationException ex) // 常见于文件被其他程序打开锁定的情况
                {
                    MessageBoxUtil.Message_Error($"保存 Excel 文件失败: {ex.Message}. 请确保文件未被其他程序打开。");
                }
                catch (Exception ex)
                {
                    MessageBoxUtil.Message_Error($"保存 Excel 文件时发生错误: {ex.Message}");
                }
            }
        }
    }

 


}
