﻿

using SmartRouting.App.Commands.数据录入.Model;
using SmartRouting.App.Commands.线路连接.Model;
using SmartRouting.App.Commands.表格转底图.Model;
using SmartRouting.App.Commands.设置.Model;
using SmartRouting.App.Properties;
using SmartRouting.App.Utils.EpplusUtils;
using SmartRouting.App.Utils.XRecordUtil;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SmartRouting.App.Commands.表格转底图.View
{
    /// <summary>
    /// Excel2DWGView.xaml 的交互逻辑
    /// </summary>
    public partial class Excel2DWGView : Window
    {
        public Excel2DWGView()
        {
            InitializeComponent();
        }


        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            Settings.Default.ExcelPath = Tb_ExcelPath.Text;
            Settings.Default.Save();

        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            Tb_ExcelPath.Text = Settings.Default.ExcelPath;
            if (!string.IsNullOrEmpty(Tb_ExcelPath.Text) && File.Exists(Tb_ExcelPath.Text))
            {
                LoadSheetNames(Tb_ExcelPath.Text);
            }

        }

        private void Btn_Creation_Click(object sender, RoutedEventArgs e)
        {
            if (!File.Exists(GlobalSetting.SaveCurveAndBlockTypeFilePath))
            {

                MessageBoxUtil.MessageInformation("请现在设置项目参数,再使用本功能!");
                return;
            }
            // 读取之前的配置信息
            string json = File.ReadAllText(GlobalSetting.SaveCurveAndBlockTypeFilePath);
            var infos = JsonConvert.DeserializeObject<ProjectSettingsModel>(json);
            var filter = FilterUtil.OrFilter("LWPolyline,line,INSERT,polyline");
            var layers = infos.CurveLayers;
            var unBreakBlockNames = infos.BlockNames;
            List<CadDb.Line> lines = new List<CadDb.Line>();
            List<CadDb.BlockReference> createdBlockReferences = new List<CadDb.BlockReference>();
            List<Excel2DWGModel> models = new List<Excel2DWGModel>();
            var file = Tb_ExcelPath.Text;

            // 检查文件路径和是否已选择工作表
            if (string.IsNullOrEmpty(file) || Cb_SheetName.SelectedItem == null)
            {
                MessageBox.Show("请先选择一个有效的Excel文件和工作表。", "提示");
                return;
            }

            // 从 ComboBox 获取选择的工作表名称
            var sheetName = Cb_SheetName.SelectedItem.ToString();

            var datas = ExcelHelper_Epplus.ReadSheetData(file, sheetName, 2, 1, 11);
            if (datas == null || !datas.Any())
            {
                MessageBox.Show("在选定的工作表中没有读取到有效数据。", "提示");
                return;
            }
            foreach (var items in datas)
            {
                //管种名称	图上点号	方向图上点号	特征点名	管径（孔、根数）	材    质	纵坐标（x）	横坐标（y）	地面高程	管点埋深	备注
                Excel2DWGModel excel2DWGModel = new Excel2DWGModel();
                excel2DWGModel.PipeName = items[0];//管种名称--权属
                excel2DWGModel.PointNum = items[1];//图上点号
                excel2DWGModel.DirPointNum = items[2];//方向图上点号
                excel2DWGModel.FeaturePointName = items[3];//特征点名
                excel2DWGModel.PipeDiameter = items[4];//管径（孔、根数）
                excel2DWGModel.Material = items[5];//材质
                excel2DWGModel.X = RegularExpressionsUtil.ExtractFirstDecimal(items[6]);//纵坐标（x）
                excel2DWGModel.Y = RegularExpressionsUtil.ExtractFirstDecimal(items[7]);//横坐标（y）
                excel2DWGModel.Z = RegularExpressionsUtil.ExtractFirstDecimal(items[8]);//地面高程
                excel2DWGModel.BuriedDepth = RegularExpressionsUtil.ExtractFirstDecimal(items[9]);//管点埋深
                excel2DWGModel.Remarks = items[10];//备注
                models.Add(excel2DWGModel);
            }
            DirectoryInfo directoryInfo = new DirectoryInfo(@"C:\Plugin_ForCAD\Config\图例\");
            List<string> blockNames = FileUtil.GetFilePath(directoryInfo, ".dwg");
            foreach (var excel2DWGModel in models)
            {
                var filePath = blockNames.FirstOrDefault(x => excel2DWGModel.FeaturePointName.Contains(System.IO.Path.GetFileNameWithoutExtension(x)));
                var startPoint = excel2DWGModel.PointNum;
                var p1 = new Point3d(excel2DWGModel.X, excel2DWGModel.Y, 0);
                var endPoint = excel2DWGModel.DirPointNum;
                var nextNum = models.FirstOrDefault(x => x.PointNum.Equals(excel2DWGModel.DirPointNum));
                if (nextNum == null) continue;
                var p2 = new Point3d(nextNum.X, nextNum.Y, 0);
                var line = new CadDb.Line { StartPoint = p1, EndPoint = p2 };
                // ModelUtil.CreateEntity(line.LineToPolyline(), 3, "管_信息");//创建线
                lines.Add(line);

                var textId = ModelUtil.CreateEntity(new DBText { TextString = excel2DWGModel.PointNum + "/" + excel2DWGModel.Z, Height = 1, WidthFactor = 0.8, Position = p1 }, 3, "图_信息");//创建文字
                var brf = ModelUtil.InsertBlockRef2(W.Doc, filePath, "*", p1, 0, new Scale3d(0.5, 0.5, 0.5));//导入图块
                if (brf != null)
                {
                    using (Transaction curtr = W.Trans)
                    {
                        try
                        {
                            BlockReference curbt = (BlockReference)curtr.GetObject(brf.Id, OpenMode.ForWrite);
                            //WellModel cableEntryModel = new WellModel();
                            //cableEntryModel.WellName = excel2DWGModel.PointNum;//工井名称
                            //cableEntryModel.WellTelecomOperator = excel2DWGModel.PipeName;//工井权属
                            //cableEntryModel.WellId = curbt.Id;//工井ID
                            //var values = GenericExtensionDictionaryManager.ObjectToTypeValueList<WellModel>(cableEntryModel);
                            //string recordName = typeof(WellModel).Name;


                            DBObjectCollection explodedEntities = new DBObjectCollection();
                            curbt.Explode(explodedEntities);

                            // 4. 遍历新实体，添加到模型空间并附加扩展数据
                            // 获取块参照所在的块表记录（例如模型空间）
                            BlockTableRecord btr = (BlockTableRecord)curtr.GetObject(curbt.OwnerId, OpenMode.ForWrite);
                            foreach (DBObject dbo in explodedEntities)
                            {
                                // 确保对象是实体 (Entity)
                                if (dbo is Entity ent)
                                {
                                    if (ent is BlockReference blockReference)
                                    {
                                        if (createdBlockReferences.Any(x => x.Position.IsEqualTo(blockReference.Position, new Tolerance(0.001, 0.001)) && x.Name.Equals(blockReference.Name)))
                                        {
                                            continue;

                                        }
                                        else
                                        {
                                            var id = ModelUtil.CreateEntity(ent);//新的块
                                            WellModel cableEntryModel = new WellModel();
                                            cableEntryModel.WellName = excel2DWGModel.PointNum;//工井名称
                                            cableEntryModel.WellTelecomOperator = excel2DWGModel.PipeName;//工井权属
                                                                                                          // cableEntryModel.WellId = id;//工井ID
                                            var values = GenericExtensionDictionaryManager.ObjectToTypeValueList<WellModel>(cableEntryModel);
                                            string recordName = typeof(WellModel).Name;
                                            id.AddXRecordToObj(recordName, values);//给工井添加扩展数据                                    

                                            createdBlockReferences.Add(ent as BlockReference);
                                        }


                                    }

                                }
                            }

                            // 5. 删除原始的块参照
                            curbt.EraseEntity<BlockReference>();

                            curtr.Commit();
                        }
                        catch (System.Exception ex)
                        {
                            // 建议加上异常处理，以便调试
                            W.Ed.WriteMessage($"\n处理块参照时发生错误: {ex.Message}");
                            // 如果出错，则中止事务
                            curtr.Abort();
                        }
                    }
                }
            }
            lines = lines.Distinct(new EqualityLine()).ToList();//直线去重

            foreach (var line in lines)
            {
                ModelUtil.CreateEntity(line, 3, "管_信息");//创建直线
            }


            // var ents = SelectUtil.SelectAllEntities<Entity>(filter, x => layers.Contains(x.Layer));

            var ents = SelectUtil.SelectAllEntities<Entity>();

            var finallblockNames = ModelUtil.GetAllBlockNames().Where(x => !string.IsNullOrEmpty(x)).OrderBy(x => x).ToList();
            SerachDrawingEntities(ents, layers, finallblockNames, unBreakBlockNames);

            bool success = CustomDataStorage.SetCustomData("MaxNumber", "0");//设置全局编号

        }

        private void Btn_Cancle_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void Btn_SelectExcelPath_Click(object sender, RoutedEventArgs e)
        {
            if (!FileUtil.OpenDialog(out string file, "请选择数据表", "Excel文件(*.xlsx)|*.xlsx")) return;

            // if (!FileUtil.OpenDialog(out string file, "请选择数据表", "Excel 文件(*.xls;*.xlsx)|*.xls;*.xlsx")) return;



            Tb_ExcelPath.Text = file;
            // 调用新方法加载工作表名称
            LoadSheetNames(file);


        }

        /// <summary>
        /// 加载Excel文件的工作表名称到ComboBox
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        private void LoadSheetNames(string filePath)
        {
            var sheetNames = ExcelHelper_Epplus.GetSheetNames(filePath);
            Cb_SheetName.ItemsSource = sheetNames;
            // 如果有工作表，默认选中第一个
            if (sheetNames != null && sheetNames.Any())
            {
                Cb_SheetName.SelectedIndex = 0;
            }
        }


        /// <summary>
        /// 图纸处理
        /// </summary>
        /// <param name="ents">"LWPolyline,line,INSERT,polyline"这些实体</param>
        /// <param name="linesLayerName">所有线的图层</param>
        /// <param name="totalBlockNames">所有块的图层</param>
        /// <param name="unBreakBlockNames">探测点的图层</param>
        public void SerachDrawingEntities(List<Entity> ents, string linesLayerName, List<string> totalBlockNames, List<string> unBreakBlockNames)
        {
            if (string.IsNullOrEmpty(linesLayerName))
            {
                MessageBoxUtil.MessageInformation("请设置线图层");
                return;
            }
            //TypedValueList values = new TypedValueList();
            //values.Add(new TypedValue(1000, "管道"));//块名
            var stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();

            try
            {


                // =================================================================================
                // 1. 选择实体, 并准备原始ID用于最终删除
                // =================================================================================
                var lineLayers = linesLayerName.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                //   var models = Dg_BlockInfo.ItemsSource as ObservableCollection<JoinTotalLinesModel>;
                //  if (!SelectUtil.SelectEntities(out List<Entity> ents, FilterUtil.OrFilter("LWPolyline,line,INSERT,polyline"), "请选择多段线或直线或块")) return;

                var originalCurveIdsToDelete = new List<ObjectId>();
                var curves = new List<CadDb.Line>();
                var blockReferences = new List<BlockReference>();
                var breakBlockRefers = new List<BlockReference>();
                // var modelNames = new HashSet<string>(models.Select(m => m.BlockName));
                //  var breakModelNames = new HashSet<string>(models.Where(m => m.IsBreak).Select(m => m.BlockName));

                var modelNames = totalBlockNames;
                var breakModelNames = totalBlockNames.Except(unBreakBlockNames).ToList();
                foreach (var entity in ents)
                {
                    if (entity is BlockReference blockReference && modelNames.Contains(blockReference.Name))
                    {
                        blockReferences.Add(blockReference);
                        if (breakModelNames.Contains(blockReference.Name)) breakBlockRefers.Add(blockReference);
                    }
                    else if (entity is Curve curve && lineLayers.Contains(curve.Layer))
                    {
                        originalCurveIdsToDelete.Add(curve.ObjectId); // 记录原始ID
                        if (curve is CadDb.Line line) curves.Add(line.Clone() as CadDb.Line);
                        else curves.AddRange(ModelUtil.ExplodeCurve(curve).Cast<CadDb.Line>());
                    }
                }
                if (curves.Count == 0 && blockReferences.Count == 0) return;
                //  W.Ed.WriteMessage($"\n[诊断] 1. 选择与分类完成: {stopwatch.ElapsedMilliseconds} ms");
                // =================================================================================
                // 2. 内存计算：吸附与去重
                // =================================================================================
                double gridSize = 2.0;
                var blockGrid = new Dictionary<Tuple<int, int>, List<BlockReference>>();
                foreach (var br in blockReferences)
                {
                    var pos = br.Position;
                    var key = new Tuple<int, int>((int)(pos.X / gridSize), (int)(pos.Y / gridSize));
                    if (!blockGrid.TryGetValue(key, out var blockList))
                    {
                        blockList = new List<BlockReference>();
                        blockGrid[key] = blockList;
                    }
                    blockList.Add(br);
                }
                foreach (var line in curves)
                {
                    var startPoint = line.StartPoint.SetZ(0);
                    var lineDir = (line.EndPoint.SetZ(0) - startPoint).GetNormal();
                    if (FindNearestSnapPoint(startPoint, lineDir, GetCandidateBlocks(startPoint, blockGrid, gridSize)) is Point3d nearestToStart) line.StartPoint = nearestToStart;

                    var endPoint = line.EndPoint.SetZ(0);
                    lineDir = (startPoint - endPoint).GetNormal();
                    if (FindNearestSnapPoint(endPoint, lineDir, GetCandidateBlocks(endPoint, blockGrid, gridSize)) is Point3d nearestToEnd) line.EndPoint = nearestToEnd;
                }
                curves = curves.Distinct(new EqualityForOnePlaneCADLine()).ToList();
                //  W.Ed.WriteMessage($"\n[诊断] 2. 吸附与去重完成: {stopwatch.ElapsedMilliseconds} ms");

                // =================================================================================
                // 3. 创建临时线实体用于几何分析 (批处理)
                // =================================================================================
                ModelUtil.AddEntities(curves);
                var tempIdsToDelete = curves.Select(c => c.ObjectId).ToList(); // 此刻ID已有效
                                                                               //  W.Ed.WriteMessage($"\n[诊断] 3. 创建临时线完成: {stopwatch.ElapsedMilliseconds} ms");

                // =================================================================================
                // 4. 路径追踪、打断判断、并收集最终要创建的实体
                // =================================================================================
                var finalEntitiesToCreate = new List<Entity>();
                var linesGroups = GeometryUtil.GroupEntities(curves, 0.01, GeometryUtil.IsOverlapping);
                var breakLinePoints = breakBlockRefers.Select(b => b.Position.SetZ(0)).ToList();
                string recordName = typeof(PipelineModel).Name;
                foreach (var linesForOneGroup in linesGroups)
                {
                    var x = new 线图(linesForOneGroup);
                    var tree = new Dictionary<节点, List<节点>>();
                    foreach (var d in x.dds)
                    {
                        var childs = new List<节点>();
                        foreach (var line in d.连接线) childs.Add(line.GetOther(d));
                        tree.Add(d, childs);
                    }
                    var solution = new Solution();
                    var groups = solution.GroupPaths(tree);

                    foreach (var group in groups)
                    {
                        var point2Ds = new Point2dCollection();
                        foreach (var item in group) point2Ds.Add(item.Pt.ToPoint2d());
                        var pline = ModelUtil.CreatPolyline(point2Ds, 0, false);
                        pline.ColorIndex = 256;
                        pline.Layer = linesForOneGroup.First().Layer;

                        var pointsOnPolyline = breakLinePoints.Where(pt => GeometryUtil.PtRelationToPoly(pline, pt.ToPoint2d(), 0.001) == 0).ToList();
                        if (pointsOnPolyline.Any())
                        {
                            var pts = pointsOnPolyline.OrderBy(p => pline.GetParameterAtPoint(pline.GetClosestPointTo(p, false))).ToList();
                            DBObjectCollection dbCurveList = pline.GetSplitCurves(pts.ToPoint3dCollection());
                            foreach (Entity ent in dbCurveList)
                            {
                                ent.ColorIndex = 256;
                                ent.Layer = pline.Layer;
                                finalEntitiesToCreate.Add(ent);



                            }
                        }
                        else
                        {


                            // pline.ObjectId.AddXData(RegAppName_Pipe, values, W.Trans); // 假设给另一个应用添加数据
                            finalEntitiesToCreate.Add(pline);
                        }
                    }
                }





                // 开启一个总的事务来处理所有数据库写操作
                using (Transaction tr = W.Doc.Database.TransactionManager.StartTransaction())
                {
                    try
                    {
                        // 获取要写入的目标空间（例如模型空间）
                        BlockTableRecord btr = (BlockTableRecord)tr.GetObject(W.Doc.Database.CurrentSpaceId, OpenMode.ForWrite);

                        // --- 第1步：将所有新实体添加到数据库 ---
                        foreach (var entityToAdd in finalEntitiesToCreate)
                        {
                            btr.AppendEntity(entityToAdd);
                            tr.AddNewlyCreatedDBObject(entityToAdd, true);
                        }

                        // --- 第2步：为已添加到数据库的实体附加扩展数据 ---
                        // 此刻, finalEntitiesToCreate 中所有实体的 ObjectId 都已变得有效
                        foreach (var addedEntity in finalEntitiesToCreate)
                        {
                            if (addedEntity == null || addedEntity.ObjectId.IsNull)
                            {
                                continue; // 以防万一，做个检查
                            }

                            var model = new PipelineModel();
                            // model.PipelineId = addedEntity.ObjectId; // 使用现在有效的ObjectId
                            var valuesList = GenericExtensionDictionaryManager.ObjectToTypeValueList<PipelineModel>(model);

                            if (valuesList == null)
                            {
                                W.Ed.WriteMessage($"为实体 {addedEntity.ObjectId} 生成的valuesList为空。\r\n");
                                continue;
                            }

                            // 调用一个修改版的、接收现有事务的附加方法，效率更高
                            addedEntity.ObjectId.AddXRecordToObj(recordName, valuesList);
                        }

                        // --- 第3步：清理所有临时和原始实体 ---
                        ModelUtil.EraseEntities(originalCurveIdsToDelete);
                        ModelUtil.EraseEntities(tempIdsToDelete);

                        // 所有操作成功，提交事务
                        tr.Commit();
                    }
                    catch (Exception ex)
                    {
                        W.Ed.WriteMessage(ex.Message + "," + ex.StackTrace + "\r\n");
                        // 发生任何错误，回滚所有操作
                        tr.Abort();
                    }
                }



















                // ModelUtil.EraseEntities(originalCurveIdsToDelete);
                // ModelUtil.EraseEntities(tempIdsToDelete);
                // W.Ed.WriteMessage($"\n[诊断] 6. 清理完成: {stopwatch.ElapsedMilliseconds} ms");

                stopwatch.Stop();
                // W.Ed.WriteMessage($"\n--- 总耗时: {stopwatch.ElapsedMilliseconds} ms ---");
            }
            catch (Exception ex)
            {
                MessageBoxUtil.Message_Error(ex.Message + "," + ex.StackTrace);
            }
        }

        #region Helper Methods for Optimization
        private List<BlockReference> GetCandidateBlocks(Point3d point, Dictionary<Tuple<int, int>, List<BlockReference>> grid, double gridSize)
        {
            var candidates = new List<BlockReference>();
            int x = (int)(point.X / gridSize);
            int y = (int)(point.Y / gridSize);
            for (int i = -1; i <= 1; i++)
            {
                for (int j = -1; j <= 1; j++)
                {
                    if (grid.TryGetValue(new Tuple<int, int>(x + i, y + j), out var blockList))
                    {
                        candidates.AddRange(blockList);
                    }
                }
            }
            return candidates;
        }

        private Point3d? FindNearestSnapPoint(Point3d point, Vector3d lineDir, List<BlockReference> candidates)
        {
            if (candidates.Count == 0) return null;
            var nearestBlock = candidates.FirstOrDefault(b => b.Position.SetZ(0).DistanceTo(point) <= 0.0001);
            if (nearestBlock != null) return nearestBlock.Position.SetZ(0);
            nearestBlock = candidates
                .Where(b => b.Position.SetZ(0).DistanceTo(point) <= 1)
                .Where(b => (b.Position.SetZ(0) - point).IsParallelTo(lineDir, new Tolerance(0.05, 0.05)))
                .OrderBy(b => b.Position.SetZ(0).DistanceTo(point))
                .FirstOrDefault();
            return nearestBlock?.Position.SetZ(0);
        }
        #endregion


    }
}
