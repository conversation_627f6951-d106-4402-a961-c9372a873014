﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils.UserDefClass
{
    public struct PointD
    {
        public PointD(double x, double y)
        {
            X = x;
            Y = y;
        }

        public double X { get; set; }
        public double Y { get; set; }

        public override bool Equals(object obj)
        {
            if (obj == null || GetType() != obj.GetType())
            {
                return false;
            }

            PointD other = (PointD)obj;
            return X.Equals(other.X) && Y.Equals(other.Y);
        }

        public static double Distance(PointD p1, PointD p2)
        {
            return Math.Sqrt(Math.Abs(p1.X - p2.X) * Math.Abs(p1.X - p2.X) + Math.Abs(p1.Y - p2.Y) * Math.Abs(p1.Y - p2.Y));

        }

        public static double GetAngle(PointD a, PointD b)
        {
            var angle = Math.Atan2(a.Y, a.X) - Math.Atan2(b.Y, b.X);
            if (angle < 0) angle += 2 * Math.PI;
            return angle;
        }


    }
}
