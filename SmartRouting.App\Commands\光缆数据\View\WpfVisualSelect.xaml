﻿<Window
    x:Class="SmartRouting.App.Commands.光缆数据.View.WpfVisualSelect"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SmartRouting.App.Commands.光缆数据.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="WPF 可视化选择"
    Width="800"
    Height="600"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <!--  数据网格区域，占据所有剩余空间  -->
            <RowDefinition Height="Auto" />
            <!--  按钮区域，高度自适应  -->
        </Grid.RowDefinitions>

        <!--  数据网格控件  -->
        <DataGrid
            x:Name="dataGrid"
            Grid.Row="0"
            Margin="0,0,0,10"
            AutoGenerateColumns="True"
            CanUserAddRows="False"
            CanUserDeleteRows="False"
            HorizontalScrollBarVisibility="Auto"
            IsReadOnly="True"
            SelectionMode="Extended"
            SelectionUnit="Cell"
            VerticalScrollBarVisibility="Auto" />

        <!--  按钮面板  -->
        <StackPanel
            Grid.Row="1"
            HorizontalAlignment="Right"
            Orientation="Horizontal">
            <Button
                x:Name="btnImport"
                Width="120"
                Height="30"
                Margin="0,0,10,0"
                Click="BtnImport_Click"
                Content="导入选中区域" />
            <Button
                x:Name="btnCancel"
                Width="80"
                Height="30"
                Click="BtnCancel_Click"
                Content="取消" />
        </StackPanel>
    </Grid>
</Window>
