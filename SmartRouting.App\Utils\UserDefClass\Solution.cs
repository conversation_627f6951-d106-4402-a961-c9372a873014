﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils.UserDefClass
{
    public class Solution
    {
        public List<List<节点>> GroupPaths(Dictionary<节点, List<节点>> adjacency)
        {
            var groups = new List<List<节点>>();
            var visitedEdges = new HashSet<Tuple<节点, 节点>>(); // 存储无向边
            // 找到所有端部节点（邻接数为1）
            var endNodes = adjacency.Where(kvp => kvp.Value.Count == 1).Select(kvp => kvp.Key).ToList();
            foreach (var endNode in endNodes)
            {
                Traverse(endNode, null, new List<节点>(), groups, adjacency, visitedEdges);
            }

            return groups;
        }

        // 核心遍历逻辑
        private void Traverse(节点 currentNode, 节点 parentNode, List<节点> currentPath, List<List<节点>> groups, Dictionary<节点, List<节点>> adjacency, HashSet<Tuple<节点, 节点>> visitedEdges)
        {
            currentPath.Add(currentNode);
            // 处理边访问（无向边）
            if (parentNode != null)
            {
                var edge = CreateEdge(parentNode, currentNode);
                if (visitedEdges.Contains(edge))
                {
                    currentPath.RemoveAt(currentPath.Count - 1);
                    return;
                }
                visitedEdges.Add(edge);
            }

            var neighbors = adjacency[currentNode].Where(n => n != parentNode).ToList();

            if (neighbors.Count == 0) // 端部节点终止
            {
                groups.Add(new List<节点>(currentPath));
                currentPath.RemoveAt(currentPath.Count - 1);
                return;
            }

            if (neighbors.Count == 1) // 单线继续遍历
            {
                Traverse(neighbors[0], currentNode, currentPath, groups, adjacency, visitedEdges);
                currentPath.RemoveAt(currentPath.Count - 1);
            }
            else // 分叉节点终止并创建新路径
            {
                groups.Add(new List<节点>(currentPath));
                foreach (var neighbor in neighbors)
                {
                    var edge = CreateEdge(currentNode, neighbor);
                    if (!visitedEdges.Contains(edge))
                    {
                        var newPath = new List<节点> { currentNode };
                        Traverse(neighbor, currentNode, newPath, groups, adjacency, visitedEdges);
                    }
                }
                currentPath.RemoveAt(currentPath.Count - 1);
            }
        }

        // 创建无向边（确保A-B和B-A视为同一边）
        private Tuple<节点, 节点> CreateEdge(节点 a, 节点 b)
        {
            return a.GetHashCode() < b.GetHashCode() ?
                Tuple.Create(a, b) :
                Tuple.Create(b, a);
        }
    }
}
