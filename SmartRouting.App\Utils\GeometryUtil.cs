﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    public class GeometryUtil
    {
        /// <summary>
        /// 缩放
        /// </summary>
        /// <param name="id">数据ID</param>
        /// <param name="basePoint">基点</param>
        /// <param name="scale">缩放比例参数-</param>
        public static void Scale(ObjectId id, Point3d basePoint, double scale)
        {
            Matrix3d transform = Matrix3d.Scaling(scale, basePoint);
            Database db = id.Database;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    Entity ent = (Entity)tr.GetObject(id, OpenMode.ForWrite);

                    if (ent != null)
                    {
                        ent.TransformBy(transform);
                    }

                    tr.Commit();

                }
                catch (Exception ex)
                {
                    CadApp.ShowAlertDialog(ex.Message);
                }
            }
        }
        public static void Rotate(Entity ent, Point3d basePt, double angle, Vector3d Axis)
        {
            Matrix3d mt = Matrix3d.Rotation(angle, Axis, basePt);
            ent.TransformBy(mt);
        }
        public static List<Entity> GetEntitiesInSelection(Document doc)
        {
            Editor ed = doc.Editor;
            Database db = doc.Database;
            List<Entity> entities = new List<Entity>();

            PromptSelectionResult result = ed.GetSelection();
            if (result.Status != PromptStatus.OK)
                return entities;

            SelectionSet selSet = result.Value;
            if (selSet == null || selSet.Count == 0)
                return entities;

            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                foreach (SelectedObject selObj in selSet)
                {
                    Entity ent = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                    if (ent != null)
                    {
                        entities.Add(ent);
                    }
                }
                tr.Commit();
            }
            return entities;
        }
        /// <summary>
        /// 只有布局才有Viewport
        /// </summary>
        /// <param name="doc"></param>
        /// <param name="layout"></param>
        /// <returns></returns>
        public static List<Viewport> GetViewport(Document doc, Layout layout)
        {
            List<Viewport> viewportList = new List<Viewport>();
            Database db = doc.Database;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                if (layout != null)
                {
                    //BasicFunction.WriteMessage(doc, $"layout为：{layout.LayoutName}");
                    BlockTableRecord btr = (BlockTableRecord)trans.GetObject(layout.BlockTableRecordId, OpenMode.ForRead);

                    // 遍历BlockTableRecord的Entities找到Viewport类型的对象
                    foreach (ObjectId id in btr)
                    {
                        DBObject obj = trans.GetObject(id, OpenMode.ForRead);
                        Viewport vp = obj as Viewport;
                        if (vp != null)
                        {
                            //BasicFunction.WriteMessage(doc, $"width: {vp.Width} height:{vp.Height}");
                            viewportList.Add(vp);
                        }
                    }
                }

            }
            return viewportList;


        }
        public static T GetClosestEntityToLine_ByGripPoint<T>(Document doc, Line line, List<T> entities) where T : Entity
        {
            Dictionary<T, Point3d> entity_GripPoint = new Dictionary<T, Point3d>();
            foreach (var item in entities)
            {
                entity_GripPoint.Add(item, GetGripPoint(item));
            }
            Dictionary<T, double> entity_Distance = new Dictionary<T, double>();
            foreach (var item in entity_GripPoint)
            {
                entity_Distance.Add(item.Key, GetDistance_PointToLine(doc, item.Value, line));
            }
            entity_Distance = entity_Distance.OrderBy(x => x.Value).ToDictionary(x => x.Key, x => x.Value);
            return entity_Distance.First().Key;

        }
        public static T GetClosestEntityToLine_ByGripPoint<T>(Document doc, Xline line, List<T> entities) where T : MText
        {
            Dictionary<T, Point3d> entity_GripPoint = new Dictionary<T, Point3d>();
            foreach (var item in entities)
            {
                entity_GripPoint.Add(item, GetGripPoint(item));
            }
            Dictionary<T, double> entity_Distance = new Dictionary<T, double>();

            foreach (var item in entity_GripPoint)
            {
                entity_Distance.Add(item.Key, GetDistance_PointToLine(doc, item.Value, line));
            }
            entity_Distance = entity_Distance.OrderBy(x => x.Value).ToDictionary(x => x.Key, x => x.Value);
            return entity_Distance.First().Key;

        }

        public static double GetDistance_PointToLine(Document doc, Point3d point, Line line)
        {
            Point3d lineStartPoint = line.StartPoint;
            Point3d lineEndPoint = line.EndPoint;

            // 将线段转换为向量
            Vector3d lineVector = lineEndPoint - lineStartPoint;

            // 将点与线起点构成的向量
            Vector3d pointVector = point - lineStartPoint;

            // 计算点到线的投影长度
            double projectionLength = pointVector.DotProduct(lineVector) / lineVector.Length;

            // 如果投影长度小于等于0，则点到线的距离为点到线起点的距离
            if (projectionLength <= 0)
            {
                return point.DistanceTo(lineStartPoint);
            }

            // 如果投影长度大于等于线段长度，则点到线的距离为点到线终点的距离
            if (projectionLength >= lineVector.Length)
            {
                return point.DistanceTo(lineEndPoint);
            }

            // 计算点到投影点的距离
            Point3d projectionPoint = lineStartPoint + lineVector.GetNormal().MultiplyBy(projectionLength);
            return point.DistanceTo(projectionPoint);
        }
        public static double GetDistance_PointToLine(Document doc, Point3d point, Xline line_)
        {
            Line perpendicularLine = new Line(point, line_.GetClosestPointTo(point, true));
            return perpendicularLine.Length;
        }

        public static List<string> GetLayerNames(Document doc)
        {
            List<string> layerNames = new List<string>();

            using (Transaction tr = doc.TransactionManager.StartTransaction())
            {
                LayerTable lt = tr.GetObject(doc.Database.LayerTableId, OpenMode.ForRead) as LayerTable;

                foreach (ObjectId layerId in lt)
                {
                    LayerTableRecord ltr = tr.GetObject(layerId, OpenMode.ForRead) as LayerTableRecord;
                    layerNames.Add(ltr.Name);
                }

                tr.Commit();
            }

            return layerNames;
        }
        public static void ResetStartPointOfPolyline(Document doc, Polyline polyline_in, Point3d point)
        {
            Database database = doc.Database;
            Point3d startPoint_original = polyline_in.StartPoint;
            Point3d endPoint_original = polyline_in.EndPoint;
            double to_startPoint_original = point.DistanceTo(startPoint_original);
            double to_endPoint_original = point.DistanceTo(endPoint_original);
            if (to_startPoint_original <= to_endPoint_original)
            {

            }
            else
            {
                using (Transaction trans = database.TransactionManager.StartTransaction())
                {
                    Polyline polyline = (Polyline)trans.GetObject(polyline_in.ObjectId, OpenMode.ForWrite);

                    if (polyline.Closed)
                    {
                        // 如果多段线是闭合的，起点和终点不能交换
                        doc.Editor.WriteMessage("\n无法交换起点和终点，因为多段线是闭合的。");
                        return;
                    }

                    //int numVertices = polyline.NumberOfVertices;

                    // 交换起点和终点
                    //Point3d temp = polyline.GetPoint3dAt(0);
                    //polyline.SetPointAt(numVertices - 1, temp);
                    //polyline.SetPointAt(0, polyline.GetPoint3dAt(numVertices - 1));

                    //polyline.SetPointAt(0, new Point2d(endPoint_original.X, endPoint_original.Y));
                    //polyline.SetPointAt(numVertices - 1, new Point2d(startPoint_original.X, startPoint_original.Y));
                    polyline.ReverseCurve();
                    trans.Commit();
                }
            }
        }
        /// <summary>
        /// 将点集中距离较近的两个点成对打包成数组的列表，输出
        /// </summary>
        /// <returns></returns>
        public static List<Point3d> GetTwoClosePoints(Polyline polyline, List<Point3d> intersectedPoints_现状道路_flattened, double 道路最大间距, bool is_单线_现状道路, out List<Point3d[]> 成对点集_现状道路)
        {
            if (!is_单线_现状道路)
            {
                成对点集_现状道路 = new List<Point3d[]>();
                List<Point3d> 成对点集的中点_现状道路 = new List<Point3d>();
                foreach (var point_现状道路 in intersectedPoints_现状道路_flattened)
                {
                    foreach (var point_现状道路2 in intersectedPoints_现状道路_flattened)
                    {
                        if (point_现状道路 != point_现状道路2)
                        {
                            if (point_现状道路.DistanceTo(point_现状道路2) <= 道路最大间距)
                            {
                                Point3d[] 成对点 = new Point3d[2] { point_现状道路, point_现状道路2 };
                                成对点集_现状道路.Add(成对点);

                                double para1 = polyline.GetParameterAtPoint(point_现状道路);
                                double para2 = polyline.GetParameterAtPoint(point_现状道路2);
                                double para_mid = (para1 + para2) / 2;
                                Point3d mid = polyline.GetPointAtParameter(para_mid);

                                //Point3d mid = new Point3d
                                //    (
                                //    0.5 * (point_现状道路.X + point_现状道路2.X)
                                //    , 0.5 * (point_现状道路.Y + point_现状道路2.Y)
                                //    , 0.5 * (point_现状道路.Z + point_现状道路2.Z)
                                //    );
                                成对点集的中点_现状道路.Add(mid);
                            }
                        }
                    }
                }
                return 成对点集的中点_现状道路;
            }
            else
            {
                成对点集_现状道路 = new List<Point3d[]>();
                return intersectedPoints_现状道路_flattened;
            }

        }
        public static Point3d GetPointViaClick(Document doc)
        {
            Editor ed = doc.Editor;
            Point3d pickedPoint = new Point3d();
            PromptPointOptions opts = new PromptPointOptions("\n请选择一个点：");
            PromptPointResult res = ed.GetPoint(opts);

            if (res.Status == PromptStatus.OK)
            {
                pickedPoint = res.Value;

                // 在控制台输出所选点的坐标
                //ed.WriteMessage($"\n所选点的坐标为：X={pickedPoint.X}, Y={pickedPoint.Y}, Z={pickedPoint.Z}");
            }
            return pickedPoint;
        }
        public static Point3d GetGripPoint(Entity entity)
        {
            Point3dCollection gripPoints__ = new Point3dCollection();
            IntegerCollection snapModes__ = new IntegerCollection(); // 此参数可省略？
            IntegerCollection geometryIds__ = new IntegerCollection(); // 此参数可省略？
            entity.GetGripPoints(gripPoints__, snapModes__, geometryIds__);
            Point3d p__ = gripPoints__[0];
            return p__;
        }
        public static void WriteMessage(Document doc, string message)
        {
            Editor editor = doc.Editor;
            editor.WriteMessage("\n" + message);
        }
        public static bool GetFirstPoint_ViaRay<T>(Ray ray, List<T> entities, out Point3d intersectionPoint, out T insertEnt, bool isContaionBasePoint = true) where T : Entity
        {
            var flag = false;//初始默认无交点
            double shortestDistance = double.MaxValue;
            insertEnt = null;
            intersectionPoint = Point3d.Origin;
            // Point3d intersectionPoint = Point3d.Origin;
            foreach (var entity in entities)
            {
                // 检测射线与实体的相交关系
                Point3dCollection intersectionPoints = new Point3dCollection();
                ray.IntersectWith(entity, Intersect.OnBothOperands, intersectionPoints, IntPtr.Zero, IntPtr.Zero);

                // 如果有交点存在
                if (intersectionPoints.Count > 0)
                {
                    flag = true;
                    // 计算交点与射线起点的距离
                    foreach (Point3d item in intersectionPoints)
                    {
                        double distance = ray.BasePoint.DistanceTo(item);
                        if (isContaionBasePoint)
                        {
                            if (distance < shortestDistance)
                            {
                                //包含当发射点
                                shortestDistance = distance;
                                intersectionPoint = item;
                                insertEnt = entity;
                            }
                        }
                        else
                        {
                            if (distance < shortestDistance && distance >= 0.0001)
                            {
                                //包含当发射点
                                shortestDistance = distance;
                                intersectionPoint = item;
                                insertEnt = entity;
                            }
                        }





                    }

                }

            }
            return flag;
        }
        public static Entity GetEntityViaObjectId(Document doc, ObjectId objectId)
        {
            Database database = doc.Database;

            using (Transaction trans = database.TransactionManager.StartTransaction())
            {
                Entity entity = null;

                try
                {
                    entity = trans.GetObject(objectId, OpenMode.ForRead) as Entity;
                }
                catch (Exception)
                {
                    // 处理对象未找到或者其他异常情况
                }

                trans.Commit();

                return entity;
            }
        }
        public static bool IsInDocument(Document doc, ObjectId[] os)
        {
            using (Transaction tr = doc.TransactionManager.StartTransaction())
            {
                foreach (ObjectId objectId in os)
                {

                    if (!objectId.IsValid || objectId.IsNull || objectId.IsErased)
                    {
                        return false;
                    }
                }

                tr.Commit();
            }
            return true;
        }
        //public static bool IsInDocument(Document doc, ObjectId[] os)
        //{
        //    Database database = doc.Database;

        //    using (Transaction trans = database.TransactionManager.StartTransaction())
        //    {
        //        BlockTable blockTable = (BlockTable)trans.GetObject(database.BlockTableId, OpenMode.ForRead, false);

        //        foreach (ObjectId o in os)
        //        {
        //            if (!blockTable.Has(o))  // 使用Has方法判断ObjectId是否存在于块表中
        //            {
        //                return false;  // 如果任何一个ObjectId不存在于文档中，则返回False
        //            }
        //        }

        //        trans.Commit();
        //    }

        //    return true;  // 如果所有的ObjectId都存在于文档中，则返回True
        //}
        public static bool IsInDocument(Document doc, ObjectId o)
        {
            Database database = doc.Database;

            using (Transaction trans = database.TransactionManager.StartTransaction())
            {
                BlockTable blockTable = (BlockTable)trans.GetObject(database.BlockTableId, OpenMode.ForRead, false);

                if (blockTable.Has(o))  // 使用Has方法判断ObjectId是否存在于块表中
                {
                    return true;
                }
                else
                {
                    trans.Commit();
                    return false;
                }


            }
        }
        public static bool IsInDocument(Document doc, Entity entity)
        {
            Database database = doc.Database;

            using (Transaction trans = database.TransactionManager.StartTransaction())
            {
                BlockTable blockTable = (BlockTable)trans.GetObject(database.BlockTableId, OpenMode.ForRead, false);
                BlockTableRecord blockTableRecord = (BlockTableRecord)trans.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForRead, false);

                // 遍历模型空间中的实体
                foreach (ObjectId objectId in blockTableRecord)
                {
                    Entity e = trans.GetObject(objectId, OpenMode.ForRead) as Entity;

                    if (e != null && entity.ObjectId == entity.Id)
                    {
                        // 找到了匹配的实体
                        // 可以执行相关操作或者在控制台输出确认信息
                        //Console.WriteLine("\n实体仍然存在于文档中。");
                        return true;


                    }
                }

                trans.Commit();
            }
            //Console.WriteLine("\n实体不存在于文档中。");

            return false;
        }
        public static void ZoomWin(

          Editor ed, Point3d min, Point3d max

        )

        {

            Point2d min2d = new Point2d(min.X, min.Y);

            Point2d max2d = new Point2d(max.X, max.Y);


            ViewTableRecord view =

              new ViewTableRecord();


            view.CenterPoint =

              min2d + ((max2d - min2d) / 2.0);

            view.Height = max2d.Y - min2d.Y;

            view.Width = max2d.X - min2d.X;

            ed.SetCurrentView(view);

        }
        public static void ZoomWin(Editor ed, List<Polyline> polylines)
        {
            List<Point3d> point3ds = new List<Point3d>();
            foreach (Polyline polyline in polylines)
            {
                point3ds.AddRange(PointsOfAPolyLine(polyline));
            }
            List<Point2d> point2ds = new List<Point2d>();
            foreach (Point3d point3d in point3ds)
            {
                point2ds.Add(new Point2d(point3d.X, point3d.Y));
            }
            Point2d max = point2ds.FirstOrDefault(
                point => point.X >= point2ds.Select(otherPoint => otherPoint.X).Max());
            Point2d min = point2ds.FirstOrDefault(
                point => point.X <= point2ds.Select(otherPoint => otherPoint.X).Min());

            Point2d min2d = new Point2d(min.X, min.Y);

            Point2d max2d = new Point2d(max.X, max.Y);


            ViewTableRecord view =

              new ViewTableRecord();


            view.CenterPoint =

              min2d + ((max2d - min2d) / 2.0);

            view.Height = max2d.Y - min2d.Y;

            view.Width = max2d.X - min2d.X;

            ed.SetCurrentView(view);

        }
        public static Point2d Max(Point2d p1, Point2d p2)
        {
            if (p1.X > p2.X && p1.Y > p2.Y)
            {
                return p1;
            }
            else
            {
                return p2;
            }
        }
        public static Point2d Min(Point2d p1, Point2d p2)
        {
            if (p1.X < p2.X && p1.Y < p2.Y)
            {
                return p1;
            }
            else
            {
                return p2;
            }
        }

        public static List<Entity> GetAllEntitiesInClosedPolyline(Document doc, Polyline polyline, SelectionFilter filter)
        {
            List<Entity> entities = new List<Entity>();
            if (polyline != null)
            {
                var points = PointsOfAPolyLine(polyline);
                Point3d first_Corner = points[0];
                Point3d opposite_Corner = points[2];
                ZoomWin(doc.Editor, first_Corner, opposite_Corner);
                PromptSelectionResult psr = doc.Editor.SelectWindow(first_Corner, opposite_Corner, filter);
                //PromptSelectionResult psr = doc.Editor.SelectWindow(first_Corner, opposite_Corner);
                SelectionSet sSET = psr.Value;
                ObjectId[] ids = sSET?.GetObjectIds();
                if (ids == null) return entities;
                using (Transaction trans = doc.Database.TransactionManager.StartTransaction())
                {
                    for (int i = 0; i < ids.Length; i++)
                    {
                        Entity ent = (Entity)ids[i].GetObject(OpenMode.ForRead);
                        entities.Add(ent);
                    }
                }
            }
            return entities;

        }
        public static Document GetOpenDocument(string docName)
        {
            // 获取当前程序实例
            DocumentCollection documents = Application.DocumentManager;

            // 遍历所有已经打开的文档
            foreach (Document doc in documents)
            {
                if (doc.Name.Equals(docName, StringComparison.OrdinalIgnoreCase))
                {
                    // 如果找到了相同文件名的文档，则返回该文档
                    return doc;
                }
            }

            // 如果没有找到相同文件名的文档，则返回 null
            return null;
        }
        public static bool IsDocumentAlreadyOpen(string docName)
        {
            // 获取当前程序实例
            DocumentCollection documents = Application.DocumentManager;

            // 遍历所有已经打开的文档
            foreach (Document doc in documents)
            {
                if (doc.Name.Equals(docName, StringComparison.OrdinalIgnoreCase))
                {
                    // 如果找到了相同文件名的文档，则返回 true 表示已经打开
                    return true;
                }
            }

            // 如果没有找到相同文件名的文档，则返回 false 表示没有打开
            return false;
        }
        public static Entity GetEntityViaClick(Document doc)
        {
            Editor editor = doc.Editor;
            Entity e = null;
            using (Transaction tr = doc.Database.TransactionManager.StartTransaction())
            {
                PromptEntityOptions options = new PromptEntityOptions("\n请点选实体：");
                PromptEntityResult result = editor.GetEntity(options);
                if (result.Status == PromptStatus.OK)
                {
                    DBObject dBObject = tr.GetObject(result.ObjectId, OpenMode.ForWrite);
                    if (dBObject is Entity entity)
                    {
                        if (entity != null)
                        {
                            string layerName = entity.Layer;
                            Color color = entity.Color;
                            e = entity;
                        }
                    }
                }
            }
            return e;

        }
        public static List<Entity> GetEntitiesViaClick(Document doc, string hints)
        {

            List<Entity> entities = new List<Entity>();

            while (true)
            {
                doc.Editor.WriteMessage(("\n") + hints);
                Editor editor = doc.Editor;
                Database db = doc.Database;
                // 提示用户选择第一个实体

                PromptEntityOptions peo1 = new PromptEntityOptions("\n请选择第一个实体：");
                //peo1.AllowNone = true;
                //peo1.SetRejectMessage("请选择有效实体。");
                //peo1.AddAllowedClass(typeof(Entity), true);

                PromptEntityResult per1 = editor.GetEntity(peo1);
                if (per1.Status != PromptStatus.OK)
                    break;

                Entity ent1 = null;
                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    ent1 = tr.GetObject(per1.ObjectId, OpenMode.ForRead) as Entity;
                    tr.Commit();
                }
                entities.Add(ent1);
                // 提示用户选择第二个实体
                PromptEntityOptions peo2 = new PromptEntityOptions("\n请选择第二个实体：");
                //peo2.AllowNone = true;
                //peo2.SetRejectMessage("请选择有效实体。");
                //peo2.AddAllowedClass(typeof(Entity), true);

                PromptEntityResult per2 = editor.GetEntity(peo2);
                if (per2.Status != PromptStatus.OK)
                    break;

                Entity ent2 = null;
                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    ent2 = tr.GetObject(per2.ObjectId, OpenMode.ForRead) as Entity;
                    tr.Commit();
                }
                entities.Add(ent2);

            }


            return entities;
        }

        public static List<object> GetObjectsViaClick(Document doc, string hints)
        {

            List<object> entities = new List<object>();

            while (true)
            {
                doc.Editor.WriteMessage(("\n") + hints);
                Editor editor = doc.Editor;
                Database db = doc.Database;


                // 提示用户选择第一个实体
                PromptEntityOptions peo1 = new PromptEntityOptions("\n请点选文本：");
                PromptEntityResult per1 = editor.GetEntity(peo1);
                if (per1.Status != PromptStatus.OK)
                    break;
                Entity ent1 = null;
                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    ent1 = tr.GetObject(per1.ObjectId, OpenMode.ForRead) as Entity;
                    tr.Commit();
                }
                entities.Add(ent1);

                // 提示用户选择第二个实体
                PromptEntityOptions peo2 = new PromptEntityOptions("\n请点选多段线：");
                PromptEntityResult per2 = editor.GetEntity(peo2);
                if (per2.Status != PromptStatus.OK)
                    break;
                Entity ent2 = null;
                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    ent2 = tr.GetObject(per2.ObjectId, OpenMode.ForRead) as Entity;
                    tr.Commit();
                }
                entities.Add(ent2);

                // 提示用户选择点
                PromptPointOptions peo3 = new PromptPointOptions("\n请点击多段线的起点");
                PromptPointResult per3 = editor.GetPoint(peo3);
                if (per3.Status != PromptStatus.OK)
                    break;
                Point3d point = new Point3d();
                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    point = per3.Value;
                    tr.Commit();
                }
                entities.Add(point);



            }


            return entities;
        }

        /// <summary>
        /// 将Point3d转换为实体点DBPoint添加到文档中
        /// </summary>
        /// <param name="doc"></param>
        /// <param name="p"></param>
        public static DBPoint AddDBPoint(Document doc, Point3d p)
        {
            Database db = doc.Database;
            Editor editor = doc.Editor;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                DBPoint point = new DBPoint(p);
                point.Color = Color.FromColorIndex(ColorMethod.ByColor, 1);
                btr.AppendEntity(point);
                tr.AddNewlyCreatedDBObject(point, true);
                tr.Commit();
                return point;
            }

        }
        public static DBPoint AddDBPoint(Document doc, Point3d p, short colorIndex)
        {
            Database db = doc.Database;
            Editor editor = doc.Editor;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                DBPoint point = new DBPoint(p);
                point.Color = Color.FromColorIndex(ColorMethod.ByColor, colorIndex);
                btr.AppendEntity(point);
                tr.AddNewlyCreatedDBObject(point, true);
                tr.Commit();
                return point;
            }

        }

        /// <summary>
        /// 画点列表中的各个点，并用文字标注点的顺序
        /// </summary>
        /// <param name="doc"></param>
        /// <param name="points"></param>
        public static void DrawPointViaPointList(Document doc, List<Point3d> points, string layerName, string message = "")
        {
            Database db = doc.Database;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                foreach (var item in points)
                {
                    Point3d startPoint = item;
                    DBPoint dBPoint = AddDBPoint(doc, startPoint);
                    dBPoint.Layer = layerName;
                    if (message != "")
                    {
                        DBText text_ = CreateDBText($"第{points.IndexOf(item) + 1}个点 距离为：{message}米"
                                        , startPoint
                                        , 10
                                        , 2
                                        , 0 // Math.PI / 2
                                        , false
                                        , 3);
                        text_.Layer = layerName;
                        ToModelSpace(text_, db);
                    }
                    else
                    {
                        DBText text = CreateDBText($"第{points.IndexOf(item) + 1}个点"
                        , startPoint
                        , 10
                        , 2
                        , 0 // Math.PI / 2
                        , false
                        , 3);
                        text.Layer = layerName;
                        ToModelSpace(text, db);
                    }
                }
                trans.Commit();
            }

        }

        public static bool MoveToward(Document doc, Entity entity, Vector3d translation)
        {
            Database db = doc.Database;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    Entity ent = trans.GetObject(entity.Id, OpenMode.ForWrite, false, true) as Entity;
                    //BasicFunction.UnlockLayer(ent); // 解锁ent所在图层
                    ent.TransformBy(Matrix3d.Displacement(translation)); // 将实体应用平移变换
                    trans.Commit(); // 提交事务
                    return true;
                }
                catch (System.Exception ex)
                {
                    doc.Editor.WriteMessage("\n移动出现错误：" + ex.Message);
                    trans.Abort();
                    return false;
                }
            }
        }

        public static void UnlockLayer(Entity e)
        {

            ObjectId layerId = e.LayerId;

            using (Transaction tr = e.Database.TransactionManager.StartTransaction())
            {
                LayerTableRecord layer = (LayerTableRecord)tr.GetObject(layerId, OpenMode.ForWrite);
                if (layer.IsLocked)
                {
                    layer.IsLocked = false;
                }
            }

        }
        public static List<Point3d> Intersect_2Entities(Document doc, Entity e1, Entity e2)
        {
            Point3dCollection p3c = new Point3dCollection();
            List<Point3d> intersectPoints = new List<Point3d>();
            e1.IntersectWith(e2, Intersect.OnBothOperands, p3c, IntPtr.Zero, IntPtr.Zero);
            if (p3c.Count > 0)
            {
                foreach (Point3d item in p3c)
                {
                    intersectPoints.Add(item);
                }
                return intersectPoints;
            }
            else
            {
                return new List<Point3d>();
            }
        }
        /// <summary>
        /// 将一个实体添加到database所在的模型空间里
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="database"></param>
        /// <returns></returns>
        public static ObjectId ToModelSpace(Entity entity, Database database)
        {
            ObjectId objectId;
            using (Transaction trans = database.TransactionManager.StartTransaction())
            {
                BlockTable blockTable = (BlockTable)trans.GetObject(database.BlockTableId, OpenMode.ForWrite, false);
                BlockTableRecord blockTableRecord = (BlockTableRecord)trans.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite, false);
                objectId = blockTableRecord.AppendEntity(entity);
                trans.AddNewlyCreatedDBObject(entity, true);
                trans.Commit();
            }
            return objectId;
        }
        /// <summary>
        /// 将一个实体添加到database所在的模型空间里
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="database"></param>
        /// <returns></returns>
        public static ObjectId ToModelSpace(Document doc, Entity entity)
        {
            ObjectId objectId;

            using (doc.LockDocument())
            {
                Database database = doc.Database;
                using (Transaction trans = database.TransactionManager.StartTransaction())
                {
                    BlockTable blockTable = (BlockTable)trans.GetObject(database.BlockTableId, OpenMode.ForWrite, false);
                    BlockTableRecord blockTableRecord = (BlockTableRecord)trans.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite, false);
                    objectId = blockTableRecord.AppendEntity(entity);
                    trans.AddNewlyCreatedDBObject(entity, true);
                    trans.Commit();

                }

            }

            return objectId;
        }

        /// <summary>
        /// 判断线段是否在攀升
        /// </summary>
        /// <param name="line"></param>
        /// <returns></returns>
        public static bool IsLineUp(Line line, Dictionary<Point3d, double> allPointsElevations)
        {
            Point3d startPoint = line.StartPoint;
            Point3d endPoint = line.EndPoint;
            if (allPointsElevations[endPoint] - allPointsElevations[startPoint] > 0) return true;
            else return false;
        }
        public static bool IsLineHorizontal(Line line)
        {

            if ((line.EndPoint.Y - line.StartPoint.Y) == 0) return true;
            else return false;

        }
        /// <summary>
        /// 将Point3d列表按照X坐标的值升序
        /// </summary>
        /// <param name="p"></param>
        /// <returns></returns>
        public static List<Point3d> SortPoints(List<Point3d> p)
        {
            List<Point3d> newPoints = new List<Point3d>();
            newPoints = p.OrderBy(x => x.X).ToList();
            return newPoints;
        }
        /// <summary>
        /// 根据块名获得块
        /// </summary>
        /// <param name="btrName"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public static BlockTableRecord GetBlock(string btrName, Database db)
        {
            BlockTableRecord block = new BlockTableRecord();
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                block = tr.GetObject(bt[btrName], OpenMode.ForRead) as BlockTableRecord;
                tr.Commit();
            }
            return block;
        }
        /// <summary>
        /// 在指定点插入块
        /// </summary>
        /// <param name="db"></param>
        /// <param name="blockName"></param>
        /// <param name="point"></param>
        /// <returns></returns>

        public static ObjectId AddBlockReferenceToCertainPoint(Database db, string blockName, Point3d point)
        {
            ObjectId obI;
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                BlockReference br = new BlockReference(point, GetBlock(blockName, db).Id); // 通过块定义创建块参照
                obI = btr.AppendEntity(br); //把块参照添加到块表记录
                tr.AddNewlyCreatedDBObject(br, true); // 通过事务添加块参照到数据库
                tr.Commit();
            }
            return obI;
        }
        /// <summary>
        /// 由插入点、文字内容、文字样式、文字高度创建单行文字
        /// </summary>
        /// <param name="textString">文字内容</param>
        /// <param name="position">基点</param>
        /// <param name="height">文字高度</param>
        /// <param name="rot">文字转角</param>
        /// <param name="isfield">是否是包含域</param>
        /// <returns></returns>
        public static DBText CreateDBText(string textString, Point3d position, double height, double widthFactor, double rot, bool isfield, short colorIndex)
        {
            DBText txt = new DBText();
            txt.Position = position;
            txt.Height = height;
            txt.Rotation = rot;
            txt.WidthFactor = widthFactor;
            txt.Color = Color.FromColorIndex(ColorMethod.ByColor, colorIndex);
            if (isfield)
            {
                Field field = new Field(textString);
                txt.SetField(field);
            }
            else
                txt.TextString = textString;
            return txt;
        }



        public static Entity SelectEntity(string word)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;
            Entity entity = null;
            PromptEntityResult ent = ed.GetEntity(word);
            if (ent.Status == PromptStatus.OK)
            {
                using (Transaction transaction = db.TransactionManager.StartTransaction())
                {
                    entity = (Entity)transaction.GetObject(ent.ObjectId, OpenMode.ForWrite, true);
                    transaction.Commit();
                }
            }
            return entity;
        }
        /// <summary>
        /// 指定两个镜像参照点得到实体镜像
        /// </summary>
        /// <param name="ent">实体对象</param>
        /// <param name="mirrorPt1">镜像点1</param>
        /// <param name="mirrorPt2">镜像点2</param>
        public static Entity Mirror(Entity ent, Point3d mirrorPt1, Point3d mirrorPt2)
        {
            Line3d mirrorLine = new Line3d(mirrorPt1, mirrorPt2);
            Matrix3d mt = Matrix3d.Mirroring(mirrorLine);
            ent.TransformBy(mt);
            return ent;
        }
        /// <summary>
        /// 将块表记录加入到块表中
        /// </summary>
        /// <returns></returns>
        public ObjectId AddBlockTableRecord(BlockTableRecord btr, Database db)
        {
            ObjectId id = new ObjectId();
            using (Transaction transaction = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = transaction.GetObject(db.BlockTableId, OpenMode.ForWrite) as BlockTable;
                id = bt.Add(btr);
                transaction.AddNewlyCreatedDBObject(btr, true);
                transaction.Commit();
            }
            return id;
        }

        /// <summary>
        /// 根据图层名，图层颜色色号来添加图层
        /// </summary>
        /// <param name="layerName"></param>
        /// <param name="colorIndex"></param>
        /// <param name="database"></param>
        /// <returns></returns>
        public static ObjectId AddLayer(string layerName, short colorIndex, Database database)
        {
            ObjectId objectId = ObjectId.Null;
            short colorIndex1 = (short)(colorIndex % 256);
            using (Transaction trans = database.TransactionManager.StartTransaction())
            {
                LayerTable layerTable = (LayerTable)trans.GetObject(database.LayerTableId, OpenMode.ForWrite, false);
                if (layerTable.Has(layerName) == false)
                {
                    LayerTableRecord layerTableRecord = new LayerTableRecord();
                    layerTableRecord.Name = layerName;
                    layerTableRecord.Color = Color.FromColorIndex(ColorMethod.ByColor, colorIndex1);
                    objectId = layerTable.Add(layerTableRecord);
                    trans.AddNewlyCreatedDBObject(layerTableRecord, true);

                }
                trans.Commit();
                return objectId;
            }
        }
        /// <summary>
        /// 将实体对象添加到文档中显示出来
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static ObjectId ToModelSpace2(Entity entity)
        {
            ObjectId id;
            Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)trans.GetObject(db.BlockTableId, OpenMode.ForWrite, false);
                BlockTableRecord btr = (BlockTableRecord)trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite, false);
                id = btr.AppendEntity(entity);
                trans.AddNewlyCreatedDBObject(entity, true);//这里第一个参数好像用btr或者entity都可以
                trans.Commit();
            }
            return id;
        }
        /// <summary>
        /// 获得文档中所有的Polyline2d
        /// </summary>
        /// <param name="db"></param>
        /// <param name="doc"></param>
        /// <returns></returns>
        public static List<Polyline2d> GetAllPolyline2d2(Database db, Document doc)
        {
            List<Polyline2d> polyline2dList = new List<Polyline2d>();
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)trans.GetObject(db.BlockTableId, OpenMode.ForRead, false);
                foreach (var item in bt)
                {
                    BlockTableRecord btr = (BlockTableRecord)trans.GetObject(item, OpenMode.ForRead);
                    foreach (var r in btr)
                    {
                        DBObject dBObject = trans.GetObject(r, OpenMode.ForRead);
                        if (dBObject.GetType() == typeof(Polyline2d))
                        {
                            Polyline2d pl2d = dBObject as Polyline2d;
                            polyline2dList.Add(pl2d);
                        }
                    }
                }
                trans.Commit();

            }
            return polyline2dList;
        }
        /// <summary>
        /// 获得所有的图层名
        /// </summary>
        /// <param name="db"></param>
        /// <param name="doc"></param>
        /// <returns></returns>
        public static List<string> GetAllLayer(Database db, Document doc)
        {
            List<string> layerList = new List<string>();
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                LayerTable lt = (LayerTable)trans.GetObject(db.LayerTableId, OpenMode.ForRead);
                foreach (var item in lt)
                {
                    LayerTableRecord ltr = trans.GetObject(item, OpenMode.ForRead) as LayerTableRecord;
                    layerList.Add(ltr.Name);

                }

            }
            return layerList;
        }
        public static List<Line> LinesOfAPolyLine(Polyline pl)
        {
            List<Point3d> point3ds = new List<Point3d>();
            List<Line> lines = new List<Line>();
            for (int i = 0; i < pl.NumberOfVertices; i++)
            {
                point3ds.Add(pl.GetPoint3dAt(i));
            }
            for (int i = 0; i < point3ds.Count - 1; i++)
            {
                Line line = new Line(point3ds[i], point3ds[i + 1]);
                lines.Add(line);
            }
            return lines;
        }
        public static List<Point3d> PointsOfAPolyLine(Polyline pl)
        {
            List<Point3d> point3ds = new List<Point3d>();
            for (int i = 0; i < pl.NumberOfVertices; i++)
            {
                point3ds.Add(pl.GetPoint3dAt(i));
            }
            point3ds = point3ds.Distinct(new EqualityPoint3d()).ToList();
            return point3ds;
        }

        public static bool IsLineDown(Line line, Dictionary<Point3d, double> allPointsElevations)
        {
            Point3d startPoint = line.StartPoint;
            Point3d endPoint = line.EndPoint;
            if (allPointsElevations[endPoint] - allPointsElevations[startPoint] < 0) return true;
            else return false;
        }

        /// <summary>
        /// 创建多行文字
        /// </summary>
        /// <param name="textString"></param>
        /// <param name="location"></param>
        /// <param name="height"></param>
        /// <param name="width"></param>
        /// <param name="rot">文字转角</param>
        /// <param name="isfield">是否是包涵域</param>
        /// <returns></returns>
        public MText CreateMText(string textString, Point3d location, double height, double width, double rot, bool isfield)
        {
            MText txt = new MText();
            txt.Location = location;
            txt.TextHeight = height;
            txt.Width = width;
            txt.Rotation = rot;
            txt.Color = Color.FromColorIndex(ColorMethod.ByColor, 20);
            if (isfield)
            {
                Field field = new Field(textString);
                txt.SetField(field);
            }
            else
                txt.Contents = textString;
            return txt;
        }

        public static List<T> GetAllT_viaSelection<T>(Document doc, string layerName, short colorIndex) where T : Entity
        {
            List<T> selectedLines = new List<T>();

            using (Transaction tr = doc.Database.TransactionManager.StartTransaction())
            {
                // 设置提示词
                PromptSelectionOptions opts = new PromptSelectionOptions();
                opts.MessageForAdding = "\n请框选一行图纸：\n";
                // 提示用户框选实体
                PromptSelectionResult result = doc.Editor.GetSelection(opts);
                if (result.Status != PromptStatus.OK)
                    return null;

                // 获取选中实体的ObjectId数组
                ObjectId[] objectIds = result.Value.GetObjectIds();

                // 遍历选中实体
                foreach (ObjectId objectId in objectIds)
                {
                    // 通过ObjectId打开实体
                    Entity entity = (Entity)objectId.GetObject(OpenMode.ForRead);

                    // 判断实体是否为Line类型
                    if (entity.GetType() == typeof(T) && entity.Layer == layerName && entity.ColorIndex == colorIndex)
                    {
                        T line = (T)entity;
                        selectedLines.Add(line);
                    }
                }

                tr.Commit();
            }
            return selectedLines;
        }
        public static List<Entity> GetAllEntity_viaSelection(Document doc)
        {
            List<Entity> selectedEntities = new List<Entity>();
            using (Transaction tr = doc.Database.TransactionManager.StartTransaction())
            {
                PromptSelectionOptions opts = new PromptSelectionOptions();
                opts.MessageForAdding = "\n请框选一行图纸：\n";
                PromptSelectionResult result = doc.Editor.GetSelection(opts);
                if (result.Status != PromptStatus.OK)
                    return null;
                ObjectId[] objectIds = result.Value.GetObjectIds();
                foreach (ObjectId objectId in objectIds)
                {
                    Entity entity = (Entity)objectId.GetObject(OpenMode.ForRead);
                    if (entity != null)
                    {
                        selectedEntities.Add(entity);
                    }
                }

                tr.Commit();
            }
            return selectedEntities;
        }

        /// <summary>
        /// 获得指定类型在文档中的所有对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="doc"></param>
        /// <returns></returns>
        public static List<T> GetAllT<T>(Document doc) where T : DBObject
        {
            Database db = doc.Database;
            List<T> tList = new List<T>();
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)trans.GetObject(db.BlockTableId, OpenMode.ForRead, false);
                foreach (var item in bt)
                {
                    BlockTableRecord btr = (BlockTableRecord)trans.GetObject(item, OpenMode.ForRead);
                    foreach (var r in btr)
                    {
                        DBObject dBObject = trans.GetObject(r, OpenMode.ForRead);
                        //if (dBObject.GetType() == typeof(T))
                        if (dBObject is T)
                        {
                            T t = dBObject as T;
                            tList.Add(t);
                        }
                    }
                }
                trans.Commit();

            }
            return tList;

        }

        /// <summary>
        /// 获得指定类型在文档中的所有对象,限制条件包括：图层名
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="doc"></param>
        /// <returns></returns>
        public static List<T> GetAllT<T>(Document doc, string layerName) where T : DBObject
        {
            Database db = doc.Database;
            List<T> tList = new List<T>();
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)trans.GetObject(db.BlockTableId, OpenMode.ForRead, false);
                foreach (var item in bt)
                {
                    BlockTableRecord btr = (BlockTableRecord)trans.GetObject(item, OpenMode.ForRead);
                    foreach (var r in btr)
                    {
                        DBObject dBObject = trans.GetObject(r, OpenMode.ForRead);
                        Entity entity = dBObject as Entity;
                        if (entity is T && entity.Layer == layerName)
                        {
                            T t = dBObject as T;
                            tList.Add(t);
                        }
                    }
                }
                trans.Commit();

            }
            return tList;

        }
        /// <summary>
        /// 获得指定类型在文档中的所有对象,限制条件包括：图层名,colorIndex
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="doc"></param>
        /// <returns></returns>
        public static List<T> GetAllT<T>(Document doc, string layerName, short colorIndex) where T : DBObject
        {
            Database db = doc.Database;
            List<T> tList = new List<T>();
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)trans.GetObject(db.BlockTableId, OpenMode.ForRead, false);
                foreach (var item in bt)
                {
                    BlockTableRecord btr = (BlockTableRecord)trans.GetObject(item, OpenMode.ForRead);
                    foreach (var r in btr)
                    {
                        DBObject dBObject = trans.GetObject(r, OpenMode.ForRead);
                        Entity entity = dBObject as Entity;
                        if (entity is T && entity.Layer == layerName && entity.ColorIndex == colorIndex)
                        {
                            T t = dBObject as T;
                            tList.Add(t);
                        }
                    }
                }
                trans.Commit();

            }
            return tList;

        }


        /// <summary>
        /// 判断两个DBText数组中的元素是否完全相同
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="a1"></param>
        /// <param name="a2"></param>
        /// <returns></returns>
        public static bool AreDBTextArraysEqual(DBText[] a1, DBText[] a2)
        {
            if (ReferenceEquals(a1, a2))
                return true;

            if (a1 == null || a2 == null)
                return false;

            if (a1.Length != a2.Length)
                return false;
            bool iFlag = false;
            for (int i = 0; i < a1.Length; i++)
            {
                bool jFlag = false;
                for (int j = 0; j < a2.Length; j++)
                {
                    if (a1[i].TextString == a2[j].TextString)
                    {
                        jFlag = true;
                        break;
                    }
                }
                if (jFlag)
                {
                    iFlag = true;
                }
                else
                {
                    iFlag = false;
                }
            }
            if (iFlag)
            {
                return true;

            }
            else
            {
                return false;
            }


        }
        public static List<Entity> GetOtherEntitiesAroundThisEntity(Entity entity)
        {
            throw new NotImplementedException();
        }
        /// <summary>
        /// 获得鼠标指针从右向左框选的所有实体，并且这些实体属于TypedValue[]所构成的过滤器中的类别
        /// </summary>
        /// <param name="doc"></param>
        /// <param name="typeValues"></param>
        /// <returns></returns>
        public static List<Entity> GetEntitiesViaFrameSelectionPlusTypedValue(Document doc, TypedValue[] typeValues)
        {
            // 获取当前文档和编辑器
            Editor ed = doc.Editor;

            // 定义用于选择的过滤器
            //TypedValue[] filterList = new TypedValue[1] { new TypedValue((int)DxfCode.Operator, "<OR") };
            ////filterList = AddFilterToList(filterList, new[] { "LINE", "LWPOLYLINE", "POLYLINE", "CIRCLE", "ARC", "SPLINE" });
            //filterList = AddFilterToList(filterList, new[] { "LWPOLYLINE", "POLYLINE" });
            //filterList = AddFilterToList(filterList, new TypedValue((int)DxfCode.Operator, "OR>"));
            // 提示用户框选实体
            SelectionFilter filter = new SelectionFilter(typeValues);
            PromptSelectionOptions pso = new PromptSelectionOptions();
            pso.MessageForAdding = "请从右向左框选";
            pso.MessageForRemoval = "选错了";
            PromptSelectionResult psr = ed.GetSelection(pso, filter);

            List<Entity> wantedEntities = new List<Entity>(); // 储存想要的entity

            if (psr.Status == PromptStatus.OK)
            {
                SelectionSet selectionSet = psr.Value;
                Transaction tr = doc.TransactionManager.StartTransaction();

                try
                {
                    // 遍历选择集中的实体
                    foreach (ObjectId id in selectionSet.GetObjectIds())
                    {
                        // 获取实体对象
                        Entity ent = tr.GetObject(id, OpenMode.ForRead) as Entity;
                        // 检查实体是否符合过滤器条件
                        if (ent != null)
                        {
                            // 处理实体对象
                            wantedEntities.Add(ent);
                            //ed.WriteMessage("\nSelected entity: {0}+{1}", ent.GetType().Name, ent.GetType().FullName);
                        }
                    }
                }
                finally
                {
                    tr.Dispose();
                }
            }
            return wantedEntities;
        }
        public static TypedValue[] AddFilterToList(TypedValue[] filterList, string[] filterTypes)
        {
            foreach (string type in filterTypes)
            {
                filterList = AddFilterToList(filterList, new TypedValue((int)DxfCode.Start, type));
            }
            return filterList;
        }

        public static TypedValue[] AddFilterToList(TypedValue[] filterList, TypedValue value)
        {
            Array.Resize(ref filterList, filterList.Length + 1);
            filterList[filterList.Length - 1] = value;
            return filterList;
        }

        /// <summary>
        /// 获得实体entity的图层名
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static string GetLayerName(Entity entity)
        {
            // 获取多段线所在的图层ID
            ObjectId layerId = entity.LayerId;

            // 获取图层名
            string layerName = "";
            using (Transaction tr = entity.Database.TransactionManager.StartTransaction())
            {
                LayerTableRecord layer = (LayerTableRecord)tr.GetObject(layerId, OpenMode.ForRead);
                layerName = layer.Name;
            }

            return layerName;
        }
        /// <summary>
        /// 判断两个实体entity是否相交, e1.IntersectWith(e2, Intersect.ExtendBoth, p3c, IntPtr.Zero, IntPtr.Zero)
        /// </summary>
        /// <param name="e1">e1.IntersectWith(e2, Intersect.ExtendBoth, p3c, IntPtr.Zero, IntPtr.Zero);</param>
        /// <param name="e2">e1.IntersectWith(e2, Intersect.ExtendBoth, p3c, IntPtr.Zero, IntPtr.Zero);</param>
        /// <returns></returns>
        public static bool IsIntersected(Entity e1, Entity e2, Intersect intersect)
        {
            Point3dCollection p3c = new Point3dCollection();
            e1.IntersectWith(e2, intersect, p3c, IntPtr.Zero, IntPtr.Zero);
            if (p3c.Count > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public static bool IsBoundingBoxIntersected(Entity e1, Entity e2, Intersect intersect)
        {
            Point3dCollection p3c = new Point3dCollection();
            e1.BoundingBoxIntersectWith(e2, intersect, p3c, IntPtr.Zero, IntPtr.Zero);
            if (p3c.Count > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public static List<Point3d> GetIntersectedPoints(Entity e1, Entity e2, Intersect intersect)
        {
            List<Point3d> temp = new List<Point3d>();
            Point3dCollection p3c = new Point3dCollection();
            e1.IntersectWith(e2, intersect, p3c, IntPtr.Zero, IntPtr.Zero);
            if (p3c.Count > 0)
            {
                foreach (Point3d p in p3c)
                {
                    temp.Add(p);
                }
                return temp;
            }
            else
            {
                return temp;
            }

        }


        /// <summary>
        /// 判断两个实体entity的BoundingBox是否相交,e1.BoundingBoxIntersectWith(e2, intersect, p3c, IntPtr.Zero, IntPtr.Zero)
        /// </summary>
        /// <param name="e1"></param>
        /// <param name="e2"></param>
        /// <param name="intersect"></param>
        /// <returns></returns>
        public static bool IsIntersectedViaBoundingBox(Entity e1, Entity e2, Intersect intersect)
        {
            Point3dCollection p3c = new Point3dCollection();
            e1.BoundingBoxIntersectWith(e2, intersect, p3c, IntPtr.Zero, IntPtr.Zero);
            if (p3c.Count > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 判断某个实体entity是否属于某个TypedValue或TypedValue[],但是似乎不起作用
        /// </summary>
        /// <param name="ent"></param>
        /// <param name="typeValues"></param>
        /// <returns></returns>
        public static bool IsEntityOfType(Entity ent, params TypedValue[] typeValues)
        {
            // 获取实体的ObjectID
            ObjectId id = ent.ObjectId;

            // 打开实体的事务处理
            using (Transaction tr = id.Database.TransactionManager.StartTransaction())
            {
                // 打开实体的DBObject
                DBObject obj = tr.GetObject(id, OpenMode.ForRead);

                // 判断实体的DBObject是否是指定的类型
                foreach (TypedValue tv in typeValues)
                {
                    if (obj.GetType().Equals(tv.TypeCode) && obj.GetRXClass().Name.Equals(tv.Value.ToString()))
                    {
                        return true;
                    }
                }

                // 关闭事务处理
                tr.Dispose();
            }

            // 如果实体不属于指定类型，返回false
            return false;
        }

        /// <summary>
        /// 获取点列表里那些Y属性值最小的点，可能不止一个
        /// </summary>
        /// <param name="points"></param>
        /// <returns></returns>
        public static List<Point3d> GetPointsViaMinY(List<Point3d> points)
        {
            List<Point3d> ps = new List<Point3d>();
            ps = points.OrderBy(x => x.Y).ToList();
            List<Point3d> result = new List<Point3d>();
            result.Add(ps[0]);
            for (int i = 0; i < ps.Count; i++)
            {
                if (i == ps.Count - 1)
                {
                    if (ps[i].Y == ps[i - 1].Y)
                    {
                        result.Add(ps[i]);
                    }
                }
                else
                {
                    if (ps[i].Y == ps[i + 1].Y)
                    {
                        result.Add(ps[i + 1]);
                    }
                    else
                    {
                        break;
                    }
                }
            }
            return result;
        }
        /// <summary>
        /// 获得点列表中，点的X属性值最大和最小的点
        /// </summary>
        /// <param name="points"></param>
        /// <returns></returns>
        public static List<Point3d> GetPointsViaMinYAndMaxY(List<Point3d> points)
        {
            List<Point3d> result = new List<Point3d>();
            result.Add(points.OrderBy(x => x.X).First());
            result.Add(points.OrderByDescending(x => x.X).First());
            return result;

        }
        /// <summary>
        /// 获得两个点的中点
        /// </summary>
        /// <param name="p1"></param>
        /// <param name="p2"></param>
        /// <returns></returns>
        public static Point3d GetMidpointOfTwoPoints(Point3d p1, Point3d p2)
        {
            double x = (p1.X + p2.X) / 2;
            double y = (p1.Y + p2.Y) / 2;
            double z = (p1.Z + p2.Z) / 2;
            Point3d p = new Point3d(x, y, z);
            return p;

        }



































        /// <summary>
        /// 计算2个向量形成的角度
        /// </summary>
        /// <param name="vec1"></param>
        /// <param name="vec2"></param>
        /// <returns></returns>
        public static double CalculateAngleBetweenVectors(Vector3d vec1, Vector3d vec2)
        {
            double value = vec1.DotProduct(vec2);
            double magn1 = vec1.Length;
            double magn2 = vec2.Length;
            double cosTheta = value / (magn1 * magn2);
            double theta = Math.Acos(cosTheta);
            double angle = theta.RadToAngle();
            return angle;
        }


        /// <summary>
        /// 一组点中，找到距离最远的2个点
        /// </summary>
        /// <param name="points"></param>
        /// <returns></returns>
        public static Tuple<节点, 节点> FindFarthestPoints(List<节点> points)
        {
            double maxDistance = 0;
            节点 farthest1 = null, farthest2 = null;
            for (int i = 0; i < points.Count; i++)
            {
                for (int j = i + 1; j < points.Count; j++)
                {
                    double distance = points[i].Pt.DistanceTo(points[j].Pt);
                    if (distance > maxDistance)
                    {
                        maxDistance = distance;
                        farthest1 = points[i];
                        farthest2 = points[j];
                    }
                }
            }
            return new Tuple<节点, 节点>(farthest1, farthest2);
        }


        /// <summary>
        /// 自定义相交的定义
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="entity1"></param>
        /// <param name="entity2"></param>
        /// <returns></returns>
        public static bool IsOverlapping<T>(T entity1, T entity2, double maxDistance) where T : Curve
        {
            //Point3dCollection point3DCollection = new Point3dCollection();
            //entity1.IntersectWith(entity2, Intersect.OnBothOperands, point3DCollection, 0, 0);

            var d1 = entity1.GetClosestPointTo(entity2.StartPoint, false).SetZ(0).DistanceTo(entity2.StartPoint.SetZ(0));
            var d2 = entity1.GetClosestPointTo(entity2.EndPoint, false).SetZ(0).DistanceTo(entity2.EndPoint.SetZ(0));
            var sp1 = entity1.StartPoint;
            var sp2 = entity2.StartPoint;
            var ep1 = entity1.EndPoint;
            var ep2 = entity2.EndPoint;
            if ((d1 <= maxDistance || d2 <= maxDistance) && (sp1.IsEqualTo(sp2) || sp1.IsEqualTo(ep2) || ep1.IsEqualTo(sp2) || ep1.IsEqualTo(ep2)))
            {
                //判断相交的情况，并且保证只能是收尾连接的相交
                return true;
            }
            return false;
        }


        public class Friend
        {
            public int Id { get; set; }
            public List<Friend> Friends { get; set; }

            public Friend(int id)
            {
                Id = id;
                Friends = new List<Friend>();

            }

        }


        /// <summary>
        /// 求凸包Graham 扫描算法
        /// </summary>
        /// <param name="points"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static Polyline GrahamScan(List<Point2d> points)
        {
            int n = points.Count;
            if (n < 3)
                throw new ArgumentException("凸包需要至少三个点");

            // 寻找最下方且最左边的点
            Point2d referencePoint = points.OrderBy(p => p.Y).ThenBy(p => p.X).First();

            // 根据极角排序其他点
            List<Point2d> sortedPoints = points.Where(p => p != referencePoint).ToList();
            sortedPoints.Sort(new PolarAngleComparer(referencePoint));

            // 压入参考点和前两个点
            Stack<Point2d> hull = new Stack<Point2d>();
            hull.Push(referencePoint);
            hull.Push(sortedPoints[0]);
            hull.Push(sortedPoints[1]);

            // 处理剩余的点
            for (int i = 2; i < sortedPoints.Count; i++)
            {
                while (hull.Count > 1 && Orientation(hull.ElementAt(1), hull.Peek(), sortedPoints[i]) != 2)
                    hull.Pop();
                hull.Push(sortedPoints[i]);
            }
            var pts = hull.ToList();
            Point2dCollection point2Ds = new Point2dCollection();
            pts.ForEach(x => point2Ds.Add(x));

            //point2Ds.Add(pts[0]);
            var pline = ModelUtil.CreatPolyline(point2Ds, 80);
            return pline;
        }

        // 用于排序的比较器，按照极角排序
        public class PolarAngleComparer : IComparer<Point2d>
        {
            private Point2d referencePoint;

            public PolarAngleComparer(Point2d referencePoint)
            {
                this.referencePoint = referencePoint;
            }

            public int Compare(Point2d p1, Point2d p2)
            {
                double angle1 = Angle(referencePoint, p1);
                double angle2 = Angle(referencePoint, p2);

                if (angle1 < angle2)
                    return -1;
                else if (angle1 > angle2)
                    return 1;
                else
                {
                    // 如果两点的极角相同，距离较近的排在前面
                    double distance1 = Math.Pow(p1.X - referencePoint.X, 2) + Math.Pow(p1.Y - referencePoint.Y, 2);
                    double distance2 = Math.Pow(p2.X - referencePoint.X, 2) + Math.Pow(p2.Y - referencePoint.Y, 2);

                    if (distance1 < distance2)
                        return -1;
                    else if (distance1 > distance2)
                        return 1;
                    else
                        return 0;
                }
            }

            // 求极角，返回角度的弧度值
            private static double Angle(Point2d p1, Point2d p2)
            {
                return Math.Atan2(p2.Y - p1.Y, p2.X - p1.X);

            }


        }
        /// <summary>
        /// 逆时针排序点
        /// </summary>
        /// <param name="points"></param>
        /// <exception cref="ArgumentException"></exception>
        public static void OrderPointByClock(ref List<Point3d> point3ds)
        {

            Point3d center = FindCentroid(point3ds);
            point3ds = point3ds.OrderBy(p => Math.Atan2(p.Y - center.Y, p.X - center.X)).ToList();
        }



        // 判断三个点的走向，用于确定是否需要进行栈的出栈操作
        private static int Orientation(Point2d p, Point2d q, Point2d r)
        {
            double val = (q.Y - p.Y) * (r.X - q.X) - (q.X - p.X) * (r.Y - q.Y);

            if (val == 0)
                return 0;  // 三点共线
            return (val > 0) ? 1 : 2; // 顺时针或逆时针
        }



     

        // 找到点集的质心（中心点）
        private static Point3d FindCentroid(List<Point3d> points)
        {
            double xSum = points.Sum(p => p.X);
            double ySum = points.Sum(p => p.Y);
            return new Point3d(xSum / points.Count, ySum / points.Count, 0);
        }
     




        /// <summary>
        /// 分组问题，得到连接的图元
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public static List<List<T>> GroupEntities<T>(List<T> entities, double maxDistance, Func<T, T, double, bool> func) where T : Entity
        {
            List<List<T>> result = new List<List<T>>();
            List<Friend> friends = new List<Friend>();

            for (int i = 0; i < entities.Count; i++)
            {
                Friend friend = new Friend(i);
                friends.Add(friend);
            }
            for (int i = 0; i < entities.Count - 1; i++)
            {
                for (int j = i + 1; j < entities.Count; j++)
                {
                    if (func(entities[i], entities[j], maxDistance))
                    {
                        friends[i].Friends.Add(friends[j]);
                        friends[j].Friends.Add(friends[i]);
                    }
                }
            }

            while (friends.Count > 0)
            {
                List<T> list = new List<T>();
                Queue<Friend> queue = new Queue<Friend>();
                queue.Enqueue(friends[0]);
                friends.RemoveAt(0);
                while (queue.Count > 0)
                {
                    Friend friend = queue.Dequeue();
                    list.Add(entities[friend.Id]);
                    foreach (var f in friend.Friends)
                    {
                        if (friends.Contains(f))
                        {
                            queue.Enqueue(f);
                            friends.Remove(f);
                        }
                    }

                }

                result.Add(list);
            }
            return result;
        }




        /// <summary>
        /// 分组问题，得到连接的图元
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public static List<List<T>> GroupEntities<T>(List<T> entities, Func<T, T, bool> func) where T : Entity
        {
            List<List<T>> result = new List<List<T>>();
            List<Friend> friends = new List<Friend>();

            for (int i = 0; i < entities.Count; i++)
            {
                Friend friend = new Friend(i);
                friends.Add(friend);
            }
            for (int i = 0; i < entities.Count - 1; i++)
            {
                for (int j = i + 1; j < entities.Count; j++)
                {
                    if (func(entities[i], entities[j]))
                    {
                        friends[i].Friends.Add(friends[j]);
                        friends[j].Friends.Add(friends[i]);
                    }
                }
            }

            while (friends.Count > 0)
            {
                List<T> list = new List<T>();
                Queue<Friend> queue = new Queue<Friend>();
                queue.Enqueue(friends[0]);
                friends.RemoveAt(0);
                while (queue.Count > 0)
                {
                    Friend friend = queue.Dequeue();
                    list.Add(entities[friend.Id]);
                    foreach (var f in friend.Friends)
                    {
                        if (friends.Contains(f))
                        {
                            queue.Enqueue(f);
                            friends.Remove(f);
                        }
                    }

                }

                result.Add(list);
            }
            return result;
        }





        /// <summary>
        /// 分组问题，得到连接的图元
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public static List<List<T>> GroupEntitiesByDir<T>(List<T> entities, Func<T, T, bool> func, string dir = "X轴方向") where T : Entity
        {
            List<List<T>> result = new List<List<T>>();
            List<Friend> friends = new List<Friend>();

            for (int i = 0; i < entities.Count; i++)
            {
                Friend friend = new Friend(i);
                friends.Add(friend);
            }
            for (int i = 0; i < entities.Count - 1; i++)
            {
                for (int j = i + 1; j < entities.Count; j++)
                {
                    if (func(entities[i], entities[j]))
                    {
                        friends[i].Friends.Add(friends[j]);
                        friends[j].Friends.Add(friends[i]);
                    }
                }
            }

            while (friends.Count > 0)
            {
                List<T> list = new List<T>();
                Queue<Friend> queue = new Queue<Friend>();
                queue.Enqueue(friends[0]);
                friends.RemoveAt(0);
                while (queue.Count > 0)
                {
                    Friend friend = queue.Dequeue();
                    list.Add(entities[friend.Id]);
                    foreach (var f in friend.Friends)
                    {
                        if (friends.Contains(f))
                        {
                            queue.Enqueue(f);
                            friends.Remove(f);
                        }
                    }

                }
                if (dir == "X轴方向")
                {
                    list = list.OrderBy(s => s.GeometricExtents.MiddlePoint().X).ToList();
                    foreach (var item in list)
                    {
                        if (item is Line line)
                        {
                            var p1 = line.StartPoint;
                            var p2 = line.EndPoint;

                            if (p1.X > p2.X)
                            {
                                line.StartPoint = p2;
                                line.EndPoint = p1;
                            }
                        }
                    }

                }
                else

                {
                    list = list.OrderBy(s => s.GeometricExtents.MiddlePoint().Y).ToList();
                    foreach (var item in list)
                    {
                        if (item is Line line)
                        {
                            var p1 = line.StartPoint;
                            var p2 = line.EndPoint;

                            if (p1.Y > p2.Y)
                            {
                                line.StartPoint = p2;
                                line.EndPoint = p1;
                            }
                        }
                    }

                }
                result.Add(list);
            }
            return result;
        }


        /// <summary>
        /// 根据算法实现首尾连接实现求内轮廓，推荐
        /// </summary>
        /// <param name="lines"></param>
        /// <param name="point"></param>
        /// <returns></returns>
        public static Polyline GetBoundary(List<Line> lines, Point3d point, double tol = 0.01)
        {
            try
            {

                List<Point2d> pts = new List<Point2d>();
                List<Line> usedLines = new List<Line>();
                var firstLine = lines.OrderBy(s => s.GetLineMidPoint().DistanceTo(point)).First();
                usedLines.Add(firstLine);
                lines.Remove(firstLine);
                pts.Add(firstLine.StartPoint.ToPoint2d());
                pts.Add(firstLine.EndPoint.ToPoint2d());

                var v1 = firstLine.StartPoint - point;
                var v2 = firstLine.EndPoint - point;
                if (v1.CrossProduct(v2).Z > 0)
                {
                    pts.Reverse();
                    // polyline.ReverseCurve();
                }
                while (true)
                {

                    List<Line> tmps = new();
                    foreach (var tmpLine in lines)
                    {
                        if (tmpLine.StartPoint.ToPoint2d().GetDistanceTo(pts.Last()) <= tol)
                        {
                            tmps.Add(tmpLine);
                        }
                        else if (tmpLine.EndPoint.ToPoint2d().GetDistanceTo(pts.Last()) <= tol)
                        {

                            ////这个地方会报写文件的Bug
                            if (tmpLine.IsWriteEnabled) tmpLine.ReverseCurve();
                            else
                            {
                                tmpLine.UpgradeOpen();
                                tmpLine.ReverseCurve();
                            }
                            tmps.Add(tmpLine);
                        }
                    }
                    if (tmps.Count == 0)
                    {
                        break;
                    }


                    // Vector3d vector = -polyline.GetFirstDerivative(polyline.EndPoint);
                    Vector3d vector = pts[pts.Count - 2].ToPoint3d() - pts.Last().ToPoint3d();

                    double maxAngle = 0;
                    Line bigLine = new();
                    foreach (var item in tmps)
                    {
                        double angle = vector.GetAngleTo(item.Delta.GetNormal(), -Vector3d.ZAxis);
                        if (angle > maxAngle)
                        {
                            maxAngle = angle;
                            bigLine = item;
                        }
                    }
                    pts.Add(bigLine.StartPoint.ToPoint2d());
                    pts.Add(bigLine.EndPoint.ToPoint2d());



                    usedLines.Add(bigLine);
                    lines.Remove(bigLine);
                    if (pts.First().GetDistanceTo(pts.Last()) <= tol) break;
                }
                lines.AddRange(usedLines);
                Point2dCollection point2Ds = new Point2dCollection();

                pts = pts.Distinct(new EqualityPoint2d()).ToList();
                pts.ForEach(x => point2Ds.Add(x));
                return ModelUtil.CreatPolyline(point2Ds, 10);
            }
            catch (System.Exception ex)
            {
                // W.Ed.WriteMessage(ex.Message);
                return null;
            }




        }

        /// <summary>
        /// 根据算法实现首尾连接实现求内轮廓，推荐
        /// </summary>
        /// <param name="lines"></param>
        /// <param name="point"></param>
        /// <returns></returns>
        public static Polyline GetBoundary(Point3d point, QuadTree<Line> quadTree, double distance = 20000, double tol = 0.01)
        {

            try
            {
                var lines = quadTree.GetNodeRecRange(point, distance);
                List<Point2d> pts = new List<Point2d>();
                List<Line> usedLines = new List<Line>();
                var firstLine = lines.OrderBy(s => s.GetLineMidPoint().DistanceTo(point)).First();
                usedLines.Add(firstLine);
                lines.Remove(firstLine);
                pts.Add(firstLine.StartPoint.ToPoint2d());
                pts.Add(firstLine.EndPoint.ToPoint2d());

                var v1 = firstLine.StartPoint - point;
                var v2 = firstLine.EndPoint - point;
                if (v1.CrossProduct(v2).Z > 0)
                {
                    pts.Reverse();
                    // polyline.ReverseCurve();
                }
                while (true)
                {

                    List<Line> tmps = new();
                    foreach (var tmpLine in lines)
                    {


                        if (tmpLine.StartPoint.ToPoint2d().GetDistanceTo(pts.Last()) <= tol)
                        {
                            tmps.Add(tmpLine);
                        }
                        else if (tmpLine.EndPoint.ToPoint2d().GetDistanceTo(pts.Last()) <= tol)
                        {

                            ////这个地方会报写文件的Bug
                            if (tmpLine.IsWriteEnabled) tmpLine.ReverseCurve();
                            else
                            {
                                tmpLine.UpgradeOpen();
                                tmpLine.ReverseCurve();
                            }
                            tmps.Add(tmpLine);
                        }
                    }
                    if (tmps.Count == 0)
                    {
                        break;
                    }

                    Vector3d vector = pts[pts.Count - 2].ToPoint3d() - pts.Last().ToPoint3d();

                    double maxAngle = 0;
                    Line bigLine = new();
                    foreach (var item in tmps)
                    {
                        double angle = vector.GetAngleTo(item.Delta.GetNormal(), -Vector3d.ZAxis);
                        if (angle > maxAngle)
                        {
                            maxAngle = angle;
                            bigLine = item;
                        }
                    }
                    pts.Add(bigLine.StartPoint.ToPoint2d());
                    pts.Add(bigLine.EndPoint.ToPoint2d());



                    usedLines.Add(bigLine);
                    lines.Remove(bigLine);
                    if (pts.First().GetDistanceTo(pts.Last()) <= tol)
                    {
                        //  W.Ed.WriteMessage("QIDIAN:"+ pts.First()+ "   ZHONGDIAN:" + pts.Last()+"\n");
                        break;
                    }

                }
                lines.AddRange(usedLines);
                Point2dCollection point2Ds = new Point2dCollection();

                pts = pts.Distinct(new EqualityPoint2d()).ToList();
                pts.ForEach(x => point2Ds.Add(x));
                return ModelUtil.CreatPolyline(point2Ds, 10);
            }
            catch (System.Exception ex)
            {
                // W.Ed.WriteMessage(ex.Message);
                return null;
            }



        }
        public static List<Line> GetBreakCurves(List<Curve> curves, double tol = 0.01)
        {
            List<Line> lines = new List<Line>();
            foreach (var curve in curves)
            {
                if (curve is Line)
                {
                    lines.Add(curve as Line);
                }
                if (curve is Polyline)
                {
                    List<Curve> cs = ModelUtil.ExplodePolyline((Polyline)curve);//炸开后的曲线
                    foreach (var c in cs)
                    {
                        if (c is Line line1) lines.Add(line1);
                    }
                }
            }
            Dictionary<Line, List<Point3d>> pointsOnLine = new();
            foreach (var line in lines)
            {
                pointsOnLine.Add(line, new List<Point3d>());
            }
            for (int i = 0; i < lines.Count - 1; i++)
            {

                for (int j = i + 1; j < lines.Count; j++)
                {
                    Point3dCollection pos = new();
                    lines[i].IntersectWith(lines[j], Intersect.OnBothOperands, pos, IntPtr.Zero, IntPtr.Zero);
                    if (pos.Count > 0)
                    {
                        foreach (Point3d p in pos)
                        {
                            //pointsOnLine[lines[i]].Add(p);
                            //  pointsOnLine[lines[j]].Add(p);

                            //var tmp1 = lines[i].GetClosestPointTo(p, false);
                            //pointsOnLine[lines[i]].Add(tmp1);

                            //var tmp2 = lines[j].GetClosestPointTo(p, false);
                            //pointsOnLine[lines[j]].Add(tmp2);

                            if (!pointsOnLine[lines[i]].Any(s => s.DistanceTo(p) < tol))
                            {
                                //  var tmp = lines[i].GetClosestPointTo(p, false);
                                pointsOnLine[lines[i]].Add(p);
                            }
                            if (!pointsOnLine[lines[j]].Any(s => s.DistanceTo(p) < tol))
                            {
                                // var tmp = lines[j].GetClosestPointTo(p, false);
                                pointsOnLine[lines[j]].Add(p);
                            }

                        }

                    }
                }


            }
            lines.Clear();
            foreach (var item in pointsOnLine)
            {

                Line line = item.Key;
                //  var pts = item.Value.Distinct(new EqualityPoint()).ToList();
                var pts = item.Value.ToList();
                if (pts.Count == 0)
                {
                    lines.Add(line);
                }

                else
                {
                    if (pts.Count > 1)
                    {

                        // pts = pts.OrderBy(x => line.GetParameterAtPoint(line.GetClosestPointTo(x, false))).ToList();

                        pts = pts.OrderBy(x => x.DistanceTo(line.StartPoint)).ToList();

                    }
                    Point3dCollection pos = new();
                    foreach (var item2 in pts)
                    {
                        var p = line.GetClosestPointTo(item2, false);
                        pos.Add(p);
                    }
                    if (pos.Count > 0)
                    {
                        try
                        {
                            DBObjectCollection dbs = line.GetSplitCurves(pos);
                            foreach (Curve dbobject in dbs)
                            {
                                if (dbobject is Line line2)
                                {
                                    if (line2.Length <= 1) continue;
                                    lines.Add(line2);
                                }
                            }
                        }
                        catch (System.Exception ex)
                        {
                            W.Ed.WriteMessage(ex.Message);
                            continue;
                        }

                    }

                }
            }
            return lines;
        }


        /// <summary>
        /// 分组问题，得到连接的图元
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public static List<List<Line>> GroupEntities2(List<Line> entities, Func<Line, Line, bool> func)
        {
            List<List<Line>> result = new List<List<Line>>();
            try
            {
                var dir = GetLinesMatrix2d(entities[0]);

                List<Friend> friends = new List<Friend>();

                for (int i = 0; i < entities.Count; i++)
                {
                    Friend friend = new Friend(i);
                    friends.Add(friend);
                }
                for (int i = 0; i < entities.Count - 1; i++)
                {
                    for (int j = i + 1; j < entities.Count; j++)
                    {
                        if (func(entities[i], entities[j]))
                        {
                            friends[i].Friends.Add(friends[j]);
                            friends[j].Friends.Add(friends[i]);
                        }
                    }
                }

                while (friends.Count > 0)
                {
                    List<Line> list = new List<Line>();
                    Queue<Friend> queue = new Queue<Friend>();
                    queue.Enqueue(friends[0]);
                    friends.RemoveAt(0);
                    while (queue.Count > 0)
                    {
                        Friend friend = queue.Dequeue();
                        list.Add(entities[friend.Id]);
                        foreach (var f in friend.Friends)
                        {
                            if (friends.Contains(f))
                            {
                                queue.Enqueue(f);
                                friends.Remove(f);
                            }
                        }

                    }
                    list = list.OrderBy(e => e.GetLineMidPoint().ToPoint2d().TransformBy(dir).X).ToList();//连续梁集合
                    result.Add(list);
                }
                result = result.OrderBy(e => e[0].GetLineMidPoint().ToPoint2d().TransformBy(dir).Y).ToList();//连续梁集合

                //整体调整各个分段的起始点的方向，让他们保持统一
                if (result[0][0].StartPoint.ToPoint2d().GetDistanceTo(result[0][1].EndPoint.ToPoint2d()) <= 1 || result[0][0].StartPoint.ToPoint2d().GetDistanceTo(result[0][1].StartPoint.ToPoint2d()) <= 1)
                {
                    result[0][0].ReverseCurve();
                }

                var basciLine = result[0][0].Delta.GetNormal();
                foreach (var lines in result)
                {
                    foreach (var line in lines)
                    {
                        if (line.Delta.GetNormal().DotProduct(basciLine) < 0.92)
                        {
                            line.ReverseCurve();
                        }
                    }

                }



            }
            catch (System.Exception)
            {

                return null;
            }




            return result;
        }

        /// <summary>
        /// 得到2个向量之间的夹角,向量a到向量b的角度（0~2PI）
        /// </summary>
        /// <param name="vector1"></param>
        /// <param name="vector2"></param>
        /// <returns></returns>
        public static double GetAngle(Vector3d a, Vector3d b)
        {
            var angle = Math.Atan2(a.Y, a.X) - Math.Atan2(b.Y, b.X);
            if (angle < 0) angle += 2 * Math.PI;
            return angle;
        }


        public static double GetAngleV1ToV2(Vector3d v1, Vector3d v2)
        {
            double rt = v2.RotateBy(Math.PI * 2 - v1.AngleOnPlane(new Plane()), Vector3d.ZAxis).AngleOnPlane(new Plane());
            return rt;
        }




        /// <summary>
        /// 得到直线用户坐标系的矩阵
        /// </summary>
        /// <param name="line"></param>
        /// <returns></returns>
        public static Matrix2d GetLinesMatrix2d(Line line)
        {
            double angle = line.Angle;
            Point2d toOrign = line.GetLineMidPoint().Point3dToPoint2d();
            Vector2d toXAxis = new Vector2d(Math.Cos(angle), Math.Sin(angle));
            Vector2d toYAxis = new Vector2d(-Math.Sin(angle), Math.Cos(angle));
            //AlignCoordinateSystem得到的是转换矩阵，实现点动系不动，如果要实现系动点不动（得到新坐标系的坐标值），将from和to交换位置即可
            return Matrix2d.AlignCoordinateSystem(toOrign, toXAxis, toYAxis, Point2d.Origin, Vector2d.XAxis, Vector2d.YAxis); //用此矩阵转换实现从WCS到UCS

        }


        /// <summary>
        /// 凸度求圆心
        /// </summary>
        /// <param name="arc1">圆弧头点</param>
        /// <param name="arc3">圆弧尾点</param>
        /// <param name="bulge">凸度</param>
        /// <returns>圆心</returns>
        public static Point2d GetArcBulgeCenter(Point2d arc1, Point2d arc3, double bulge)
        {

            var x1 = arc1.X;
            var y1 = arc1.Y;
            var x2 = arc3.X;
            var y2 = arc3.Y;

            var b = (1 / bulge - bulge) / 2;
            var x = (x1 + x2 - b * (y2 - y1)) / 2;
            var y = (y1 + y2 + b * (x2 - x1)) / 2;

            return new Point2d(x, y);


        }


        /// <summary>
        /// 凸度求弧长
        /// </summary>
        /// <param name="arc1">圆弧头点</param>
        /// <param name="arc3">圆弧尾点</param>
        /// <param name="bulge">凸度</param>
        /// <returns></returns>
        public static double GetLength(Point2d arc1, Point2d arc3, double bulge)
        {
            var bowLength = arc1.GetDistanceTo(arc3);         //弦长
            var bowLength2 = bowLength / 2;                //半弦
            var archHeight = Math.Abs(bulge) * bowLength2; //拱高==凸度*半弦

            //根据三角函数: (弦长/2)²+(半径-拱高)²=半径²
            //再根据:完全平方公式变形: (a+b)²=a²+2ab+b²、(a-b)²=a²-2ab+b²
            var r = (bowLength2 * bowLength2 + archHeight * archHeight) / (2 * archHeight); //半径
                                                                                            //求圆心角:一半圆心角[(对边==半弦长,斜边==半径)]; *2就是完整圆心角
            var asin = Math.Asin(bowLength2 / r) * 2; //反正弦
                                                      //弧长公式: 弧长=绝对值(圆心弧度)*半径...就是单位*比例..缩放过程
            var arcLength = asin * r;
            return arcLength;
        }





        public static Vector2d GetVector(Line line)
        {

            Vector2d vecLine = line.EndPoint.ToPoint2d() - line.StartPoint.ToPoint2d();
            return vecLine;
        }




        public static LineSegment2d GetUcsLine(Line x, Matrix2d matrix2dW2U)
        {
            //Matrix2d matrix2dW2U = x.SuperMatrix2dW2U();
            //Matrix2d matrix2dU2W = matrix2dW2U.Inverse();
            Point2d pt1 = x.StartPoint.Point3dToPoint2d().TransformBy(matrix2dW2U);
            Point2d pt2 = x.EndPoint.Point3dToPoint2d().TransformBy(matrix2dW2U);
            Point2d startPoint = pt1;
            Point2d endPoint = pt2;
            if (pt1.X > pt2.X)
            {
                startPoint = pt2;
                endPoint = pt1;
            }
            var l1 = new LineSegment2d(startPoint, endPoint);
            return l1;
        }










        /// <summary>
        /// 判断轻型多义性的时钟走向
        /// </summary>
        /// <param name="pline">轻型多义性</param>
        /// <returns>顺时针返回-1,逆时针返回1</returns>
        public static int Clockwise(Polyline pline)
        {
            Polyline pline1 = (Polyline)pline.Clone();
            double bulge0 = pline1.GetBulgeAt(0);
            double area0 = pline1.Area;
            if (bulge0 == 0.0)
            {
                pline1.SetBulgeAt(0, 0.5);
                double area1 = pline1.Area;
                if (area1 > area0)
                    return 1;
                else
                    return -1;
            }
            else
            {
                pline1.SetBulgeAt(0, 0);
                double area1 = pline1.Area;
                if (bulge0 > 0)
                {
                    if (area1 > area0)
                        return -1;
                    else
                        return 1;
                }
                else
                {
                    if (area1 > area0)
                        return 1;
                    else
                        return -1;
                }
            }
        }

        /// <summary>
        /// 判断2个直线是否平行
        /// </summary>
        /// <param name="line1"></param>
        /// <param name="line2"></param>
        /// <returns></returns>
        public static bool Parallel(Line line1, Line line2)
        {
            Plane p = new Plane();
            LineSegment3d l1 = new LineSegment3d(line1.StartPoint, line1.EndPoint);
            LineSegment3d l2 = new LineSegment3d(line2.StartPoint, line2.EndPoint);
            return l1.IsCoplanarWith(l2, out p);

        }

        public static double Distance(Line line1, Line line2)
        {
            var dis = line1.GetClosestPointTo(line2.StartPoint, true).DistanceTo(line2.StartPoint);
            return dis;

        }



        /// <summary>
        /// 点沿着指定方向跟距离移动，返回最后的点
        /// </summary>
        /// <param name="sourcePt">原始点</param>
        /// <param name="vector">方向</param>
        /// <param name="cRad">移动距离</param>
        /// <returns></returns>
        public static Point3d Move(Point3d sourcePt, Vector3d vector, double cRad)
        {
            var Ivect = vector.GetNormal();//获取单位向量
            var lastvect = Ivect * cRad;//最后的向量
            return sourcePt.Add(lastvect);//后面移动后的点

        }

        /// <summary>
        /// 曲线的内偏移
        /// </summary>
        /// <param name="cur"></param>
        /// <param name="distance"></param>
        /// <returns></returns>
        public static List<Polyline> OffsetPolyline(Polyline polyline, double distance)
        {


            DBObjectCollection offsetcur;
            ObjectId id = SymbolUtilityServices.GetBlockModelSpaceId(HostApplicationServices.WorkingDatabase);
            List<Polyline> polylines = new List<Polyline>();
            try
            {

                if (Clockwise(polyline) == 1)
                {
                    offsetcur = polyline.GetOffsetCurves(distance);


                }
                else
                {

                    offsetcur = polyline.GetOffsetCurves(-distance);


                }
                if (offsetcur.Count > 0)
                {
                    foreach (Polyline pl in offsetcur)
                    {

                        polylines.Add(pl);
                    }

                }

            }
            catch (System.Exception)
            {


            }
            return polylines;
        }
        /// <summary>
        /// 曲线的偏移
        /// </summary>
        /// <param name="cur"></param>
        /// <param name="distance"></param>
        /// <returns></returns>
        public static List<Curve> Offset(Curve cur, double distance)
        {
            ObjectId id = SymbolUtilityServices.GetBlockModelSpaceId(HostApplicationServices.WorkingDatabase);
            List<Curve> curves = new List<Curve>();
            try
            {

                DBObjectCollection offsetcur = cur.GetOffsetCurves(distance);
                foreach (var pl in offsetcur)
                {
                    curves.Add((Curve)pl);
                }
            }
            catch (System.Exception)
            {


            }
            return curves;
        }


        // 判断点和多段线的位置关系
        // 返回值：-1表示在多段线外部，0表示在多段线上，1表示在多段线内部
        public static int PtRelationToPoly(Polyline pPoly, Point2d pt, double tol)
        {
            // 1.如果点到多段线的最近点和给定的点重合，表示点在多段线上
            Point3d closestPoint = pPoly.GetClosestPointTo(pt.ToPoint3d(pPoly.Elevation), false);		// 多段线上与给定点距离最近的点	
            if (Math.Abs(closestPoint.X - pt.X) < tol && Math.Abs(closestPoint.Y - pt.Y) < tol)			// 点在多段线上
            {
                return 0;
            }

            // 2.第一个射线的方向是从最近点到当前点，起点是当前点
            Ray pRay = new Ray();
            pRay.BasePoint = new Point3d(pt.X, pt.Y, pPoly.Elevation);
            // 射线的起点是pt，方向为从最近点到pt，如果反向做判断，则最近点距离pt太近的时候，最近点也会被作为一个交点（这个交点不太容易被排除掉）
            // 此外，这样的射线方向很容易判断出点不在内部的情况
            Vector3d vec = new Vector3d(-(closestPoint.X - pt.X), -(closestPoint.Y - pt.Y), 0);
            pRay.UnitDir = vec;

            // 3.射线与多段线计算交点
            Point3dCollection intPoints = new Point3dCollection();

            pPoly.IntersectWith(pRay, Intersect.OnBothOperands, intPoints, 0, 0);
            // IntersectWith函数经常会得到很近的交点，这些点必须进行过滤
            FilterEqualPoints(intPoints, 1.0E-4);

        // 4.判断点和多段线的位置关系
        RETRY:
            Point3d[] pts = new Point3d[10];		//////////////////////////////////////////////////////////////////////////
            if (intPoints.Count > 0)
            {
                pts[0] = intPoints[0];
            }
            if (intPoints.Count > 1)
            {
                pts[1] = intPoints[1];
            }
            if (intPoints.Count > 2)
            {
                pts[2] = intPoints[2];
            }
            if (intPoints.Count > 3)
            {
                pts[3] = intPoints[3];
            }
            // 4.1 如果射线和多段线没有交点，表示点在多段线的外部
            if (intPoints.Count == 0)
            {
                pRay.Dispose();
                return -1;
            }
            else
            {
                // 3.1 过滤掉由于射线被反向延长带来的影响
                FilterEqualPoints(intPoints, closestPoint.ToPoint2d(), 1.0E-4);		// 2008-0907修订记录：当pt距离最近点比较近的时候，最近点竟然被作为一个交点！
                // 3.2 如果某个交点与最近点在给定点的同一方向，要去掉这个点（这个点明显不是交点，还是由于intersectwith函数的Bug）	
                for (int i = intPoints.Count - 1; i >= 0; i--)
                {
                    if ((intPoints[i].X - pt.X) * (closestPoint.X - pt.X) >= 0 &&
                        (intPoints[i].Y - pt.Y) * (closestPoint.Y - pt.Y) >= 0)
                    {
                        intPoints.RemoveAt(i);
                    }
                }

                int count = intPoints.Count;
                for (int i = 0; i < intPoints.Count; i++)
                {
                    if (PointIsPolyVert(pPoly, intPoints[i].ToPoint2d(), 1.0E-4))		// 只要有交点是多段线的顶点就重新进行判断
                    {
                        // 处理给定点很靠近多段线顶点的情况(如果与顶点距离很近，就认为这个点在多段线上，因为这种情况没有什么好的判断方法)
                        if (PointIsPolyVert(pPoly, new Point2d(pt.X, pt.Y), 1.0E-4))
                        {
                            return 0;
                        }

                        // 将射线旋转一个极小的角度(2度)再次判断（假定这样不会再通过上次判断到的顶点）
                        vec = vec.RotateBy(0.035, Vector3d.ZAxis);
                        pRay.UnitDir = vec;
                        intPoints.Clear();
                        pPoly.IntersectWith(pRay, Intersect.OnBothOperands, intPoints, 0, 0);
                        goto RETRY;		// 继续判断结果
                    }
                }

                pRay.Dispose();

                if (count % 2 == 0)
                {
                    return -1;
                }
                else
                {
                    return 1;
                }
            }
        }
        // 从点数组中删除与给定点平面位置重合的点
        // tol: 判断点重合时的精度（两点之间的距离小于tol认为这两个点重合）
        static void FilterEqualPoints(Point3dCollection points, Point2d pt, double tol)
        {
            Point3dCollection tempPoints = new Point3dCollection();
            for (int i = 0; i < points.Count; i++)
            {
                if (points[i].ToPoint2d().GetDistanceTo(pt) > tol)
                {
                    tempPoints.Add(points[i]);
                }
            }

            points = tempPoints;
        }

        // 从点数组中删除与其他点重合的点
        static void FilterEqualPoints(Point3dCollection points, double tol)
        {
            for (int i = points.Count - 1; i > 0; i--)
            {
                for (int j = 0; j < i; j++)
                {
                    if (IsEqual(points[i].X, points[j].X, tol) && IsEqual(points[i].Y, points[j].Y, tol))
                    {
                        points.RemoveAt(i);
                        break;
                    }
                }
            }
        }

        // 点是否是多段线的顶点
        static bool PointIsPolyVert(Polyline pPoly, Point2d pt, double tol)
        {
            for (int i = 0; i < pPoly.NumberOfVertices; i++)
            {
                Point3d vert = pPoly.GetPoint3dAt(i);

                if (IsEqual(vert.ToPoint2d(), pt, tol))
                {
                    return true;
                }
            }

            return false;
        }

        // 二维点是否相同
        static bool IsEqual(Point2d firstPoint, Point2d secondPoint, double tol)
        {
            return (Math.Abs(firstPoint.X - secondPoint.X) < tol && Math.Abs(firstPoint.Y - secondPoint.Y) < tol);
        }

        // 两个实数是否相等
        static bool IsEqual(double a, double b, double tol)
        {
            return (Math.Abs(a - b) < tol);
        }



        /// <summary>
        /// 旋转
        /// </summary>
        /// <param name="ent"></param>
        /// <param name="basePoint"></param>
        /// <param name="angle">弧度制的度数</param>
        public static void Rotate(Entity ent, Point3d basePoint, double angle)
        {

            Matrix3d mt = Matrix3d.Rotation(angle, Vector3d.ZAxis, basePoint);
            ent.TransformBy(mt);
        }

        /// <summary>
        /// 移动
        /// </summary>
        /// <param name="ent"></param>
        /// <param name="sourcePt"></param>
        /// <param name="targetPt"></param>
        public static void Move(Entity ent, Point3d sourcePt, Point3d targetPt)
        {
            var vec = targetPt - sourcePt;
            var mt = Matrix3d.Displacement(vec);
            ent.TransformBy(mt);


        }


        #region 多段线自相交检测
        /// <summary>
        /// 多段线自相交检测
        /// </summary>
        /// <param name="pPolyline">待检测多段线</param>
        /// <param name="intersectPoint3Ds">返回自相交点集合</param>
        /// <returns></returns>
        public static bool SelfIntersectDetect(Polyline pPolyline, out List<Point3d> intersectPoint3Ds)
        {

            intersectPoint3Ds = new List<Point3d>();
            try
            {
                // 自身与自身相交结果（包含顶点和相交点）
                var intersectWithResult = new Point3dCollection();
                pPolyline.IntersectWith(pPolyline, Intersect.OnBothOperands, intersectWithResult, IntPtr.Zero, IntPtr.Zero);

                // 存储顶点
                HashSet<Point3d> vertices = new HashSet<Point3d>();
                int count = pPolyline.NumberOfVertices;
                for (int j = 0; j < count; j++)
                {
                    Point2d vertexd = pPolyline.GetPoint2dAt(j);
                    vertices.Add(new Point3d(vertexd.X, vertexd.Y, 0.0));
                }

                count = intersectWithResult.Count;
                for (int j = 0; j < intersectWithResult.Count; j++)
                {
                    Point3d tempPoint3D = intersectWithResult[j];
                    // 剔除顶点，获得真正的相交点
                    if (!vertices.Contains(tempPoint3D))
                    {
                        intersectPoint3Ds.Add(tempPoint3D);
                    }
                }


                return true;
            }
            catch (System.Exception ex)
            {

                return false;
            }
        }

        #endregion


        /// <summary>
        ///  判断直线是否在一个多段线的内部
        /// </summary>
        /// <param name="line"></param>
        /// <param name="polyline"></param>
        /// <returns></returns>
        public static bool LineInPolyline(Line line, Polyline polyline, bool containLinePoint = false, double tol = 0.01)
        {
            bool flag = false;
            var sp = line.StartPoint;
            var ep = line.EndPoint;
            var value1 = PtRelationToPoly(polyline, sp.ToPoint2d(), tol);
            var value2 = PtRelationToPoly(polyline, ep.ToPoint2d(), tol);
            if (containLinePoint)
            {
                return (value1 == 1 || value1 == 0) && (value2 == 1 || value2 == 0);
            }
            else
            {

                return value1 == 1 && value2 == 1;
            }
        }


        /// <summary>
        /// 获取2点的中点
        /// </summary>
        /// <param name="pt1"></param>
        /// <param name="pt2"></param>
        /// <returns></returns>
        public static Point3d GetMidPoint(Point3d pt1, Point3d pt2)
        {
            Point3d result;
            result = new Point3d((pt1.X + pt2.X) / 2.0, (pt1.Y + pt2.Y) / 2.0, (pt1.Z + pt2.Z) / 2.0);
            return result;
        }


        //面域方法
        static Point3d origin = Point3d.Origin;
        static Vector3d Xv3d = Vector3d.XAxis;
        static Vector3d Yv3d = Vector3d.YAxis;

        public static Point3d GetPolylineCenterByRegion(Polyline pl)
        {
            using (Transaction trans = W.Trans)
            {
                DBObjectCollection db1 = new DBObjectCollection();
                DBObjectCollection db2 = new DBObjectCollection();
                //添加父类为Curve的集合
                db1.Add((Curve)pl);

                //接收两个面域
                db2 = Region.CreateFromCurves(db1);

                //得到两个面域
                Region g1 = db2[0] as Region;
                Point2d acadRegion = g1.AreaProperties(ref origin, ref Xv3d, ref Yv3d).Centroid;
                //  Point2d acadRegion = g1.AR(ref origin, ref Xv3d, ref Yv3d).Centroid;
                return acadRegion.ToPoint3d();
                trans.Commit();
            }
        }

        public static Point3d GetPolylineCenterBySimple(Polyline pl)
        {
            if (pl.HasBulges)
            {
                return GeometryUtil.GetMidPoint(pl.GetPoint2dAt(0).ToPoint3d(), pl.GetPoint2dAt(1).ToPoint3d());
            }

            List<Point3d> points = new List<Point3d>();
            for (int i = 0; i < pl.NumberOfVertices; i++)
            {
                points.Add(pl.GetPoint2dAt(i).ToPoint3d());
            }
            if (points.Count > 3)
            {
                var bPoint = points[0];

                var cPoint = points.OrderByDescending(s => s.DistanceTo(bPoint)).First();
                return GeometryUtil.GetMidPoint(cPoint, bPoint);
            }
            else
            {
                return new Point3d();
            }
        }





      












    }
}
