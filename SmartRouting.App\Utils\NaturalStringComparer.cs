﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    /// <summary>
    /// Implements a natural sorting for strings in the format "PREFIX-NUM1-NUM2-...".
    /// It first groups by prefix, then sorts numerically by the parts.
    /// </summary>
    public class NaturalStringComparer : IComparer<string>
    {
        public int Compare(string? x, string? y)
        {
            if (ReferenceEquals(x, y)) return 0;
            if (x is null) return -1;
            if (y is null) return 1;

            var xParts = x.Split('-');
            var yParts = y.Split('-');

            // 1. Compare the non-numeric prefix
            int prefixComparison = string.Compare(xParts[0], yParts[0], StringComparison.Ordinal);
            if (prefixComparison != 0)
            {
                return prefixComparison;
            }

            // 2. Prefixes are the same, now compare numeric parts
            int minLength = Math.Min(xParts.Length, yParts.Length);

            for (int i = 1; i < minLength; i++)
            {
                // Try to parse parts as numbers for numeric comparison
                bool isXPartNumeric = int.TryParse(xParts[i], out int xNum);
                bool isYPartNumeric = int.TryParse(yParts[i], out int yNum);

                if (isXPartNumeric && isYPartNumeric)
                {
                    int numComparison = xNum.CompareTo(yNum);
                    if (numComparison != 0)
                    {
                        return numComparison;
                    }
                }
                else
                {
                    // Fallback to string comparison if parts are not numeric
                    int stringPartComparison = string.Compare(xParts[i], yParts[i], StringComparison.Ordinal);
                    if (stringPartComparison != 0)
                    {
                        return stringPartComparison;
                    }
                }
            }

            // 3. If all common parts are equal, the one with fewer parts comes first.
            // e.g., "SSSS-2" should come before "SSSS-2-1".
            return xParts.Length.CompareTo(yParts.Length);
        }
    }
}
