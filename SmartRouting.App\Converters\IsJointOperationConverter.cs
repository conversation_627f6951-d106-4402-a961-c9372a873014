using System;
using System.Globalization;
using System.Windows.Data;
using SmartRouting.App.Commands.数据录入.Model;

namespace SmartRouting.App.Converters
{
    public class IsJointOperationConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is OperationEnum op)
            {
                return op == OperationEnum.标记接头_总 || op == OperationEnum.标记接头_分支;
            }
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 因为绑定是单向的，所以不需要从UI转回模型
            throw new NotImplementedException();
        }
    }
} 