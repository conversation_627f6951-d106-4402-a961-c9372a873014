﻿using SmartRouting.App.Commands.线路连接.View;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.设置.View
{
    /// <summary>
    /// 项目的设置类，用于设置一些图层等信息
    /// </summary>
    public class ProjectSettings
    {
        [CommandTag("排    模", nameof(ProjectSettingsCmd), "项目设置")]
        [CommandMethod(nameof(ProjectSettingsCmd))]
        public void ProjectSettingsCmd()
        {
            #region 判断是否过了使用期限
            if (GlobalSetting.SetDeadLine())
            {
                MessageBoxUtil.MessageInformation("软件已过使用期限或未联网，请联系开发者!");
                return;
            }
            #endregion
            WindowManagerUtil.Show<ProjectSettingsView>();//单例模式
        }


    }
}
