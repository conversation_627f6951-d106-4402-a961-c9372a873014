﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
	  <UseWPF>true</UseWPF>
	  <UseWindowsForms>true</UseWindowsForms>
	  <PlatformTarget>x64</PlatformTarget>
	  <LangVersion>preview</LangVersion>
	  <Nullable>enable</Nullable>
	  <NoWarn>MSB3270;NU1504;CS0649;FodyPackageReference;CS0414;CS0067;CS0168;CS0169;NU1603;CS0108;CS0219;CS0612;CS1690;CS0162;CS8600;CS8602;CS8618;CS8604;CS8603;CS8625;CS8619;CS8601;CS0618;CS0659;CS8629;CS8620</NoWarn>
	  <Configurations>Debug;Release;AC13;AC12;ZW25</Configurations>
	  <!--动态释放内存部分-->
	  <AssemblyVersion>1.0.0.*</AssemblyVersion>
	  <FileVersion>1.0.0.0</FileVersion>
	  <Deterministic>False</Deterministic>
    <Configurations>Debug;Release;AC12;AC13;ZW25</Configurations>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='AC13|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='AC12|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ZW25|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>
	<ItemGroup Condition="'$(Configuration)'=='AC13'">
		<PackageReference Include="AutoCAD.NET" Version="20.0.0" Condition="'$(Configuration)'=='AC13'" />
  </ItemGroup>
	<ItemGroup Condition="'$(Configuration)'=='AC12'">
		<PackageReference Include="Autocad.NetApi" Version="17.2.0" Condition="'$(Configuration)'=='AC12'" />
	</ItemGroup>


	


	
	
	
	
	
	
	
	<ItemGroup Condition="'$(Configuration)'=='ZW25'">
	  <PackageReference Include="ZWCAD.NetApi" Version="20.25.0" />
	</ItemGroup>


	<PropertyGroup Condition="'$(Configuration)'=='AC12'">
		<!--注意这里的版本-->
		<TargetFramework>net35</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)'=='AC13'">
		<!--注意这里的版本-->
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)'=='ZW25'">
		<!--注意这里的版本-->
		<TargetFramework>net48</TargetFramework>
	</PropertyGroup>
	<ItemGroup>
	  <None Remove="Resources\98功能x32.png" />
	  <None Remove="Resources\CustomCSharp-Dark.xshd" />
	</ItemGroup>
	<ItemGroup>
	  <EmbeddedResource Include="Resources\CustomCSharp-Dark.xshd" />
	</ItemGroup>

	
	
	<ItemGroup>
	  <PackageReference Include="AutoCAD.NET" Version="20.0.0">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="AvalonEdit" Version="5.0.4" />
	  <PackageReference Include="Costura.Fody" Version="5.7.0">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="EPPlus" Version="6.0.3" />
	  <PackageReference Include="HandyControl" Version="3.5.1" />
	  <PackageReference Include="HelixToolkit.Wpf" Version="2.25.0" />
	  <PackageReference Include="MathNet.Numerics" Version="5.0.0" />
	  <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="4.14.0">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
	  <PackageReference Include="Microsoft.CodeAnalysis.Scripting" Version="4.14.0" />
	  <PackageReference Include="NetTopologySuite.Core" Version="1.15.3" />
	  <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	  <PackageReference Include="System.Runtime" Version="4.3.1" />
	  <PackageReference Include="System.Windows.Interactivity.WPF" Version="2.0.20525" />
	</ItemGroup>

	
	
	<ItemGroup>
	  <Reference Include="Microsoft.Office.Interop.Excel">
	    <HintPath>Dll\Microsoft.Office.Interop.Excel.dll</HintPath>
	  </Reference>
	  <Reference Include="System.Net.Http" />
	</ItemGroup>

	
	
	<ItemGroup>
	  <Resource Include="Resources\98功能x32.png" />
	</ItemGroup>

	
	
	<ItemGroup>
	  <Compile Update="Properties\Resources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>Resources.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Properties\Settings.Designer.cs">
	    <DesignTimeSharedInput>True</DesignTimeSharedInput>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>Settings.settings</DependentUpon>
	  </Compile>
	</ItemGroup>

	
	
	<ItemGroup>
	  <EmbeddedResource Update="Properties\Resources.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>Resources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>
	
	
	
	
	
	<ItemGroup>
	  <None Update="Properties\Settings.settings">
	    <Generator>SettingsSingleFileGenerator</Generator>
	    <LastGenOutput>Settings.Designer.cs</LastGenOutput>
	  </None>
	</ItemGroup>
	
	
	
	
	
	<Target Name="PostBuild" AfterTargets="PostBuildEvent">
	  <Exec Command="&quot;D:\NET Reactor\.NET Reactor\dotNET_Reactor.exe&quot; -project &quot;$(ProjectDir)reactor_settings.nrproj&quot; -file &quot;$(TargetPath)&quot;" />
	</Target>

</Project>

	
	


	


	
	





