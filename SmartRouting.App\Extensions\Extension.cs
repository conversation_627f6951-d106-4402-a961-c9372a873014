﻿using GeoAPI.Geometries;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Markup;
using System.Windows.Navigation;
using System.Windows;
using System.IO.Packaging;

namespace SmartRouting.App.Extensions
{
    public static class Extension
    {
     
        public static bool Contains(this List<double> list, double value, double tolerance)
        {
            foreach (var item in list)
                if (Math.Abs(item - value) < tolerance)
                    return true;
            return false;
        }
        public static Point3d SetZ(this Point3d p, double z)
        {
            return new Point3d(p.X, p.Y, z);
        }


        public static Point3d GetLineMidPoint(this Line line)
        {
            return GeometryUtil.GetMidPoint(line.StartPoint, line.EndPoint);
        }
        public static double GetCurveLength(this Curve c)
        {
            var length = Math.Abs(c.GetDistanceAtParameter(c.EndParam) - c.GetDistanceAtParameter(c.StartParam));
            return length;
        }

        public static List<object> ResultBufferToList(this ResultBuffer typedValues)
        {
            List<object> list = new List<object>();

            foreach (var item in typedValues)
            {
                list.Add(item.Value);
            }

            return list;




        }

        public static ObjectId AddLineType(this Database db, string typeName)
        {
            //打开线型表
            LinetypeTable lt = (LinetypeTable)db.LinetypeTableId.GetObject(OpenMode.ForRead);
            if (!lt.Has(typeName))//如果存在名为typeName的线型表记录
            {
                lt.UpgradeOpen();//切换线型表为写
                //新建一个线型表记录
                LinetypeTableRecord ltr = new LinetypeTableRecord();
                ltr.Name = typeName;//设置线型表记录的名称
                lt.Add(ltr);//将新的线型表记录的信息添加到的线型表中
                db.TransactionManager.AddNewlyCreatedDBObject(ltr, true);
                lt.DowngradeOpen();//为了安全，将线型表的状态切换为读
            }
            return lt[typeName];//返回新添加的线型表记录的ObjectId
        }



        /// <summary>
        /// 创建一个新的文字样式
        /// </summary>
        /// <param name="db">数据库对象</param>
        /// <param name="styleName">文字样式名</param>
        /// <param name="fontFilename">字体文件名</param>
        /// <param name="bigFontFilename">大字体文件名</param>
        /// <returns>返回添加的文字样式的Id</returns>
        public static ObjectId AddTextStyle(this Database db, string styleName, string fontFilename, string bigFontFilename)
        {
            //打开文字样式表
            TextStyleTable st = (TextStyleTable)db.TextStyleTableId.GetObject(OpenMode.ForRead);
            if (!st.Has(styleName))//如果不存在名为styleName的文字样式，则新建一个文字样式
            {
                //定义一个新的文字样式表记录
                TextStyleTableRecord str = new TextStyleTableRecord();
                str.Name = styleName;//设置文字样式名
                str.FileName = fontFilename;//设置字体文件名
                str.BigFontFileName = bigFontFilename;//设置大字体文件名
                st.UpgradeOpen();//切换文字样式表的状态为写以添加新的文字样式
                st.Add(str);//将文字样式表记录的信息添加到文字样式表中
                //把文字样式表记录添加到事务处理中
                db.TransactionManager.AddNewlyCreatedDBObject(str, true);
                st.DowngradeOpen();//为了安全，将文字样式表的状态切换为读
            }
            return st[styleName];//返回新添加的文字样式表记录的ObjectId
        }

        /// <summary>
        /// 创建一个新的文字样式
        /// </summary>
        /// <param name="db">数据库对象</param>
        /// <param name="styleName">文字样式名</param>
        /// <param name="fontFilename">字体文件名</param>
        /// <returns>返回添加的文字样式的Id</returns>
        public static ObjectId AddTextStyle(this Database db, string styleName, string fontFilename)
        {
            return db.AddTextStyle(styleName, fontFilename, "");
        }


        /// <summary>
        /// 添加包含图案的形定义文件
        /// </summary>
        /// <param name="db">数据库对象</param>
        /// <param name="styleName">样式名</param>
        /// <param name="shapeFilename">形定义文件名</param>
        /// <returns>返回新添加的文字样式表记录的ObjectId</returns>
        public static ObjectId AddShapeTextStyle(this Database db, string styleName, string shapeFilename)
        {
            //打开文字样式表
            TextStyleTable st = (TextStyleTable)db.TextStyleTableId.GetObject(OpenMode.ForRead);
            if (!st.Has(styleName))//如果不存在名为styleName的文字样式，则新建一个文字样式
            {
                //定义一个新的的文字样式表记录
                TextStyleTableRecord tstr = new TextStyleTableRecord();
                tstr.Name = styleName;//设置的文字样式名
                tstr.FileName = shapeFilename;//设置字体文件名
                tstr.IsShapeFile = true;//文件为形定义文件
                st.UpgradeOpen();//切换的文字样式表的状态为写以添加新的的文字样式
                st.Add(tstr);//将新建的文字样式表记录的信息添加到文字样式表中
                //把的文字样式表记录添加到事务处理中
                db.TransactionManager.AddNewlyCreatedDBObject(tstr, true);
                st.DowngradeOpen();
            }
            return st[styleName];//返回新添加的文字样式表记录的ObjectId
        }






        /// <summary>
        /// 通过包围盒生成包围盒的多段线
        /// </summary>
        /// <param name="extents"></param>
        /// <returns></returns>






        public static Polyline ExtentToPolyline(this Extents3d extents, double width = 0)
        {
            Polyline pl = new Polyline();

            // 添加多段线的顶点
            pl.AddVertexAt(pl.NumberOfVertices, new Point2d(extents.MinPoint.X, extents.MinPoint.Y), 0, width, width);
            pl.AddVertexAt(pl.NumberOfVertices, new Point2d(extents.MaxPoint.X, extents.MinPoint.Y), 0, width, width);
            pl.AddVertexAt(pl.NumberOfVertices, new Point2d(extents.MaxPoint.X, extents.MaxPoint.Y), 0, width, width);
            pl.AddVertexAt(pl.NumberOfVertices, new Point2d(extents.MinPoint.X, extents.MaxPoint.Y), 0, width, width);
            pl.AddVertexAt(pl.NumberOfVertices, new Point2d(extents.MinPoint.X, extents.MinPoint.Y), 0, width, width);// 闭合多段线
            return pl;
        }


        public static Point2dCollection ListPoint2d2Point2dCollection(this List<Point2d> pts)
        {
            Point2dCollection points = new Point2dCollection();

            foreach (Point2d p in pts)
            {
                points.Add(p);
            }

            return points;
        }



        public static List<Point3d> Point3dCollectionToListPoint3d(this Point3dCollection pts)
        {
            List<Point3d> points = new List<Point3d>();

            foreach (Point3d p in pts)
            {
                points.Add(p);
            }

            return points;
        }


     

      





        public static Vector3d Vector2dToVector3d(this Vector2d vector2D)
        {
            return new Vector3d(vector2D.X, vector2D.Y, 0);

        }


        public static List<Point3d> Point3dCollectionoPoints(this Point3dCollection points)
        {
            List<Point3d> pts = new List<Point3d>();
            foreach (Point3d item in points)
            {
                pts.Add(item);
            }
            return pts;
        }



        public static Vector2d GetLineVector(this Line line)

        {
            return line.StartPoint.Point3dToPoint2d() - line.EndPoint.Point3dToPoint2d();

        }

        public static ObjectIdCollection EntitiesToObjectIdCollection(this List<Entity> entities)

        {
            ObjectIdCollection ids = new ObjectIdCollection();
            entities.ForEach(s => ids.Add(s.ObjectId));
            return ids;
        }


        /// <summary>
        /// 炸开实体
        /// </summary>
        /// <param name="polyline"></param>
        /// <returns>Curve列表</returns>
        public static List<Curve> ExplodeEntity(this Entity entity)
        {
            DBObjectCollection dbs = new DBObjectCollection();
            entity.Explode(dbs);
            List<Curve> curves = new List<Curve>();
            foreach (var item in dbs)
            {
                if (item is Curve)
                {
                    curves.Add(item as Curve);
                }
            }
            return curves;
        }

        /// <summary>
        /// 获取平行线
        /// </summary>
        public static IList<Line> GetParallelLines(this Line baseLine, IList<Line> lines)
        {
            Point2d pb0 = baseLine.StartPoint.Point3dToPoint2d();
            Point2d pb1 = baseLine.EndPoint.Point3dToPoint2d();
            Vector2d baseVector = pb1 - pb0;
            IList<Line> parallels = new List<Line>() { baseLine };
            foreach (var line in lines)
            {
                if (baseLine == line) continue;
                Point2d pt0 = line.StartPoint.Point3dToPoint2d();
                Point2d pt1 = line.EndPoint.Point3dToPoint2d();
                Vector2d vector = pt1 - pt0;
                double ang1 = baseVector.GetAngleTo(vector);
                if (ang1 < 0.001 || ang1 > Math.PI - 0.001)
                {
                    parallels.Add(line);
                }
            }
            return parallels;
        }



        /// <summary>
        /// 得到世界坐标系向局部坐标系的转换矩阵
        /// </summary>
        /// <returns></returns>
        public static Matrix2d SuperMatrix2dW2U(this Line line)
        {
            double angle = line.Angle;
            Point2d toOrign = line.StartPoint.Point3dToPoint2d();
            Vector2d toXAxis = new Vector2d(Math.Cos(angle), Math.Sin(angle));
            Vector2d toYAxis = new Vector2d(-Math.Sin(angle), Math.Cos(angle));
            //AlignCoordinateSystem得到的是转换矩阵，实现点动系不动，如果要实现系动点不动（得到新坐标系的坐标值），将from和to交换位置即可
            return Matrix2d.AlignCoordinateSystem(toOrign, toXAxis, toYAxis, Point2d.Origin, Vector2d.XAxis, Vector2d.YAxis); //用此矩阵转换实现从WCS到UCS
        }

        /// <summary>
        /// 向带列表值的字典中添加元素
        /// </summary>
        /// <typeparam name="TKey"></typeparam>
        /// <typeparam name="TElement"></typeparam>
        /// <param name="dictionary"></param>
        /// <param name="key"></param>
        /// <param name="element"></param>
        public static void SuperAdd<TKey, TElement>(this Dictionary<TKey, List<TElement>> dictionary, TKey key, TElement element)
        {
            if (dictionary.ContainsKey(key))
            {
                dictionary[key].Add(element);
            }
            else
            {
                dictionary.Add(key, new List<TElement> { element });
            }
        }







        public static void LoadViewFromUri(this FrameworkElement userControl, string baseUri)
        {
            try
            {
                var resourceLocater = new Uri(baseUri, UriKind.Relative);
                var exprCa = (PackagePart)typeof(System.Windows.Application).GetMethod("GetResourceOrContentPart", BindingFlags.NonPublic | BindingFlags.Static).Invoke(null, new object[] { resourceLocater });
                var stream = exprCa.GetStream();
                var uri = new Uri((Uri)typeof(BaseUriHelper).GetProperty("PackAppBaseUri", BindingFlags.Static | BindingFlags.NonPublic).GetValue(null, null), resourceLocater);
                var parserContext = new ParserContext
                {
                    BaseUri = uri
                };
                typeof(XamlReader).GetMethod("LoadBaml", BindingFlags.NonPublic | BindingFlags.Static).Invoke(null, new object[] { stream, parserContext, userControl, true });
            }
            catch (Exception)
            {
                //log
            }
        }

        public static double StringToDouble(this string str)
        {
            return Convert.ToDouble(str);
        }
        public static int StringToInt(this string str)
        {
            return Convert.ToInt32(str);
        }
        public static Point3d Point2dToPoint3d(this Point2d point2d)
        {
            return new Point3d(point2d.X, point2d.Y, 0);
        }
        public static Point2d Point3dToPoint2d(this Point3d point3d)
        {
            return new Point2d(point3d.X, point3d.Y);
        }
        public static List<Point3d> GetPolylineCornerPoints(this Polyline polyline)
        {
            List<Point3d> pts = new List<Point3d>();
            for (int i = 0; i < polyline.NumberOfVertices; i++)
            {
                pts.Add(polyline.GetPoint2dAt(i).Point2dToPoint3d());
            }
            pts = pts.Distinct(new EqualityPoint3d()).ToList();
            return pts;
        }


        public static DBObjectCollection ToDBObjectCollection<T>(this List<T> ents) where T : Entity
        {
            DBObjectCollection dbs = new DBObjectCollection();
            ents.ForEach(e => dbs.Add(e));
            return dbs;
        }
        public static List<T> ToEntities<T>(this DBObjectCollection dbs) where T : Entity
        {
            List<T> values = new List<T>();
            foreach (DBObject entity in dbs)
            {
                if (entity is T t)
                {
                    values.Add(t);
                }
            }
            return values;
        }

        /// <summary>
        /// 通过移动夹点，实现拉伸实体的效果
        /// </summary>
        /// <param name="ent">实体</param>
        /// <param name="index">夹点的索引，索引从0开始</param>
        /// <param name="targetPoint">移动后的点位置</param>
        public static void MoveGripPoint(this Entity ent, int index, Point3d targetPoint)
        {
            var pts = new Point3dCollection();//多段线所有的夹点
            var snapModes = new IntegerCollection();
            var geometryIds = new IntegerCollection();
            ent.GetGripPoints(pts, snapModes, geometryIds);

            var indices = new IntegerCollection
            {
                index
            };
            ent.UpgradeOpen();
            ent.MoveGripPointsAt(indices, targetPoint - pts[index]);
            ent.DowngradeOpen();
        }


        public static double GetAngleToXAxis(this Point3d startPoint, Point3d endPoint)
        {
            // 声明一个与X轴平行的向量
            Vector3d temp = new Vector3d(1, 0, 0);
            // 获取七点到终点的向量
            Vector3d VsToe = startPoint.GetVectorTo(endPoint);
            return VsToe.Y > 0 ? temp.GetAngleTo(VsToe) : -temp.GetAngleTo(VsToe);
        }
        public static Extents3d GetEntitiesExtent3d<T>(this List<T> entities) where T : Entity
        {
            Extents3d extents3D = new Extents3d();
            foreach (Entity ent in entities)
            {
                extents3D.AddExtents(ent.GeometricExtents);
            }
            return extents3D;
        }

        public static Point3d PointFToPoint3d(this PointF pointf)
        {
            return new Point3d(pointf.X, pointf.Y, 0);
        }


        public static List<Point3d> DBPointToPoint3d(this List<DBPoint> dBPoints)
        {
            List<Point3d> points = new List<Point3d>();
            foreach (var item in dBPoints)
            {
                Point3d point = new Point3d(item.Position.X, item.Position.Y, 0);
                points.Add(point);
            }
            return points;

        }


        public static List<Point2d> DBPointToPoint2d(this List<DBPoint> dBPoints)
        {
            List<Point2d> points = new List<Point2d>();
            foreach (var item in dBPoints)
            {
                Point2d point = new Point2d(item.Position.X, item.Position.Y);
                points.Add(point);
            }
            return points;

        }


        public static Point3dCollection ToPoint3dCollection(this List<Point3d> points)
        {
            Point3dCollection pos = new Point3dCollection();
            points.ForEach(p => pos.Add(p));
            return pos;
        }

        public static Point2dCollection ToPoint2dCollection(this List<Point3d> points)
        {
            Point2dCollection pos = new Point2dCollection();
            points.ForEach(p => pos.Add(p.ToPoint2d()));
            return pos;
        }

        public static List<Point2d> Point3dListToPoint2dList(this List<Point3d> points)
        {
            List<Point2d> pos = new List<Point2d>();
            points.ForEach(p => pos.Add(p.ToPoint2d()));
            return pos;
        }
        public static List<PointF> Point3dListToPointFs(this List<Point3d> points)
        {
            List<PointF> pos = new List<PointF>();
            points.ForEach(p => pos.Add(new PointF((float)p.X, (float)p.Y)));
            return pos;
        }
        public static Point2dCollection PointFListToPoint2dCollection(this List<PointF> points)
        {
            Point2dCollection pos = new Point2dCollection();
            points.ForEach(p => pos.Add(new Point2d(p.X, p.Y)));
            return pos;
        }
        public static T ToEntity<T>(this ObjectId id) where T : DBObject
        {
            Database db = HostApplicationServices.WorkingDatabase;
            T entity;
            using (Transaction tran = db.TransactionManager.StartTransaction())
            {
                entity = id.GetObject(OpenMode.ForRead) as T;
            }
            return entity;
        }
        public static Point3d ToPoint3d(this Point2d point, double z = 0)
        {
            return new Point3d(point.X, point.Y, z);
        }
        public static Point2d ToPoint2d(this Point3d point)
        {
            return new Point2d(point.X, point.Y);
        }
        public static bool ExtensHit(this Extents3d ext1, Extents3d ext2)
        {
            double AXmin = ext1.MinPoint.X; double AYmin = ext1.MinPoint.Y;
            double AXmax = ext1.MaxPoint.X; double AYmax = ext1.MaxPoint.Y;
            double BXmin = ext2.MinPoint.X; double BYmin = ext2.MinPoint.Y;
            double BXmax = ext2.MaxPoint.X; double BYmax = ext2.MaxPoint.Y;
            bool b1 = AXmax >= BXmin && AXmin <= BXmax && AYmax >= BYmin && AYmin <= BYmax;
            bool b2 = BXmax >= AXmin && BXmin <= AXmax && BYmax >= AYmin && BYmin <= AYmax;
            return b1 || b2;
        }
        public static bool ExtensHit2(this Extents3d ext1, Extents3d ext2)
        {
            Point3d point1 = new Point3d(ext1.MinPoint.X / 2 + ext1.MaxPoint.X / 2, ext1.MinPoint.Y / 2 + ext1.MaxPoint.Y / 2, 0);
            Point3d point2 = new Point3d(ext2.MinPoint.X / 2 + ext2.MaxPoint.X / 2, ext2.MinPoint.Y / 2 + ext2.MaxPoint.Y / 2, 0);
            double d1 = ext1.MinPoint.DistanceTo(ext1.MaxPoint);
            double d2 = ext2.MinPoint.DistanceTo(ext2.MaxPoint);
            return point1.DistanceTo(point2) <= d1 / 2 + d2 / 2;
        }
        public static ObjectId CreatEntity(this Entity entity, short colorIndex, string layerName)
        {
            LayerUtil.CreateLayer(layerName, colorIndex, true);
            Database database = W.Doc.Database;
            ObjectId result = default(ObjectId);
            bool flag = entity != null;
            if (flag)
            {
                using (W.Doc.LockDocument())
                {
                    using (Transaction transaction = database.TransactionManager.StartTransaction())
                    {
                        BlockTableRecord blockTableRecord = (BlockTableRecord)transaction.GetObject(database.CurrentSpaceId, OpenMode.ForWrite, false);
                        entity.ColorIndex = colorIndex;
                        entity.Layer = layerName;
                        result = blockTableRecord.AppendEntity(entity);
                        transaction.AddNewlyCreatedDBObject(entity, true);
                        transaction.Commit();
                    }
                }
            }
            return result;
        }

        public static bool DelEntity(this ObjectId id)
        {
            try
            {

                if (!id.IsNull)
                {

                    using (Database db = HostApplicationServices.WorkingDatabase)
                    {

                        using (Transaction trans = db.TransactionManager.StartTransaction())
                        {

                            Entity entity = (Entity)trans.GetObject(id, OpenMode.ForWrite, true);

                            entity.Erase(true);

                            trans.Commit();
                        }
                    }
                }
                else
                {
                    return false;
                }
            }
            catch
            {

                return false;
            }
            return true;
        }






        // 二维点转三维点
        private static Point3d ToPoint3d(this Point2d point2d)
        {
            return new Point3d(point2d.X, point2d.Y, 0);
        }




        /// <summary>
        /// 生成云线
        /// </summary>
        /// <param name="minPt"></param>
        /// <param name="maxPt"></param>
        /// <param name="num"></param>
        /// <param name="minStp"></param>
        /// <param name="maxSep"></param>
        /// <param name="width"></param>
        /// <returns></returns>
        public static Polyline CreatCloud(this Point3d minPt, Point3d maxPt, int num = 10, double minStp = 200, double maxSep = 800, double width = 20)
        {
            var tmpCurve = new Polyline();
            tmpCurve.AddVertexAt(0, new Point2d(minPt.X, minPt.Y), 0, 0, 0);
            tmpCurve.AddVertexAt(1, new Point2d(maxPt.X, minPt.Y), 0, 0, 0);
            tmpCurve.AddVertexAt(2, new Point2d(maxPt.X, maxPt.Y), 0, 0, 0);
            tmpCurve.AddVertexAt(3, new Point2d(minPt.X, maxPt.Y), 0, 0, 0);
            tmpCurve.Closed = true;
            var dLength = tmpCurve.GetDistanceAtParameter(tmpCurve.EndParam);
            var nMin = dLength / maxSep;
            var nMax = dLength / minStp;
            var t_heigh = Math.Abs(maxPt.Y - minPt.Y);
            var t_width = Math.Abs(maxPt.X - minPt.X);
            var nums = (int)Math.Max(t_heigh, t_width) > 4500 ? 10 : (int)Math.Max(Math.Max(nMin, nMax), num);
            var dis = dLength / nums;
            var pl = new Polyline();
            for (int i = 0; i < nums; i++)
            {
                var pt = tmpCurve.GetPointAtDist(i * dis).ToPoint2d();
                pl.AddVertexAt(i, pt, 0.52, 0, width);
            }
            pl.Closed = true;
            return pl;
        }

        public static Dictionary<string, string> GetProperties(this object dBObject)
        {
            Type type = dBObject.GetType();
            PropertyInfo[] properties = type.GetProperties();
            Dictionary<string, string> keyValuePairs = new Dictionary<string, string>();
            foreach (PropertyInfo property in properties)
            {
                object value = null;
                try
                {
#if CAD12

#elif CAD14
                    value = property.GetValue(dBObject);
#endif

                    keyValuePairs.Add(property.Name, value.ToString());
                }
                catch (Exception)
                {
                }
            }
            return keyValuePairs;
        }
        public static Point3d GetCenterPoint(this Extents3d extents)
        {
            return extents.MinPoint + (extents.MaxPoint - extents.MinPoint) / 2;
        }
        public static bool InExtents3d(this Point3d point, Extents3d extents)
        {
            return point.X > extents.MinPoint.X && point.X < extents.MaxPoint.X && point.Y > extents.MinPoint.Y && point.Y < extents.MaxPoint.Y;
        }
        public static T KeLong<T>(this T t) where T : DBObject
        {
            T tt = t.Clone() as T;
            return tt;
        }
        public static T GetMax<T>(this List<T> ts, Func<T, double> selector) where T : Entity
        {
            double max = ts.Select(selector).Max();
            return ts.Find(x => selector.Invoke(x) == max);
        }
        public static string AddPath(this string str1, string str2)
        {
            return Path.Combine(str1, str2);
        }
        /// <summary>
        /// 亮显实体
        /// </summary>
        /// <param name="ids">要亮显的实体的Id集合</param>
        public static void HighlightEntities(this ObjectIdCollection ids)
        {
            if (ids.Count == 0) return;
            var trans = ids[0].Database.TransactionManager;
            foreach (ObjectId id in ids)
            {
                Entity ent = trans.GetObject(id, OpenMode.ForRead) as Entity;
                if (ent != null)
                {
                    ent.Highlight();
                }
            }
        }

        /// 取得当前图层名称
        /// </summary>
        /// <param name="db">数据库对象</param>
        /// <returns></returns>
        public static string GetCurrentLayer(this Database db)
        {
            //Database db = HostApplicationServices.WorkingDatabase;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                //获取当前图形中所有颜色为红色的图层层名
                var CurrentLayer = (from layer in db.GetAllLayers()
                                    where layer.Id == db.Clayer
                                    select layer.Name).ToList();
                return CurrentLayer[0];
                trans.Commit();
            }
        }


        /// <summary>
        /// 获取当前图形中所有的图层
        /// </summary>
        /// <param name="db">数据库对象</param>
        /// <returns>返回所有的层表记录</returns>
        public static List<LayerTableRecord> GetAllLayers(this Database db)
        {
            //打开层表
            LayerTable lt = (LayerTable)db.LayerTableId.GetObject(OpenMode.ForRead);
            //用于返回层表记录的列表
            List<LayerTableRecord> ltrs = new List<LayerTableRecord>();
            foreach (ObjectId id in lt)//遍历层表
            {
                //打开层表记录
                LayerTableRecord ltr = (LayerTableRecord)id.GetObject(OpenMode.ForRead);
                ltrs.Add(ltr);//添加到返回列表中
            }
            return ltrs;//返回所有的层表记录
        }


        /// <summary>
        /// 亮显选择集中的实体
        /// </summary>
        /// <param name="selectionSet">选择集</param>
        public static void SelectHighlightEntities(this SelectionSet selectionSet)
        {
            if (selectionSet == null) return;
            ObjectIdCollection ids = new ObjectIdCollection(selectionSet.GetObjectIds());
            ids.HighlightEntities();
        }
        /// <summary>
        /// 亮显选择集中的实体
        /// </summary>
        /// <param name="ents">选择集</param>
        public static void SelectHighlightEntities(this List<Entity> ents)
        {
            ObjectIdCollection ids = new ObjectIdCollection();
            foreach (Entity entity in ents)
            {
                entity.Highlight();
            }


        }



        static double originRotation;//最初的文字角度
        /// <summary>
        ///  获取文字的包围盒
        /// </summary>
        public static Point3d GetTextCenter(this DBText text)
        {
            using (var tr = W.Trans)
            {
                originRotation = text.Rotation;//将文字最初的角度记录下来
                var orginTextPt = text.Position;//文字最初点的位置
                var result = tr.GetObject(text.Id, OpenMode.ForWrite) as DBText;
                result.Rotation = 0;//将文字角度人为的修改为0
                Point2d minPt = new Point2d(result.GeometricExtents.MinPoint.X, result.GeometricExtents.MinPoint.Y);//左下点
                Point2d maxPt = new Point2d(result.GeometricExtents.MaxPoint.X, result.GeometricExtents.MaxPoint.Y);//右上点
                var midPt = GeometryUtil.GetMidPoint(minPt.ToPoint3d(), maxPt.ToPoint3d());//中点
                var vec = midPt.GetVectorTo(result.Position);
                result.Rotation = originRotation;//还原为最初的角度
                Matrix3d curUCSMatrix = W.Doc.Editor.CurrentUserCoordinateSystem;
                CoordinateSystem3d curUCS = curUCSMatrix.CoordinateSystem3d;
                //绕当前UCS的Z轴将点midPt旋转指定的角度originRotation，基点为orginTextPt
                var lastMidPt = midPt.TransformBy(Matrix3d.Rotation(originRotation, curUCS.Zaxis, orginTextPt));//得到文字中点
                tr.Commit();
                return lastMidPt;
            }
        }


        public static void StringToDouble(this String str, ref double num)
        {
            if (Regex.IsMatch(str, @"^\d+$"))
            {
                num = Convert.ToDouble(str);
                //  return Convert.ToDouble(str);
            }

        }
        /// <summary>
        /// 获取指定名称的块属性值
        /// </summary>
        /// <param name="blockReferenceId">块参照的Id</param>
        /// <param name="attributeName这里指代的是“说明”这个属性名">属性名</param>
        /// <returns>返回指定名称的块属性值</returns>
        public static string GetAttributeInBlockReference(this ObjectId blockReferenceId, string attributeName)
        {
            string attributeValue = string.Empty; // 先定义属性值为空
            Database db = blockReferenceId.Database;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                // 获取块参照
                BlockReference bref = (BlockReference)trans.GetObject(blockReferenceId, OpenMode.ForRead);
                // 遍历块参照的属性
                foreach (ObjectId attId in bref.AttributeCollection)
                {
                    // 获取块参照属性对象
                    AttributeReference attRef = (AttributeReference)trans.GetObject(attId, OpenMode.ForRead, true);
                    //判断属性名是否为指定的属性名
                    if (attRef.Tag.ToUpper() == attributeName.ToUpper())
                    {
                        attributeValue = attRef.TextString;//获取属性值
                        break;
                    }
                }
                trans.Commit();
            }
            return attributeValue; //返回块属性值
        }





        public static List<PointD> DBPointsToPointDs(this List<DBPoint> pts)
        {
            List<PointD> ptds = new List<PointD>();
            foreach (var item in pts)
            {
                PointD point = new PointD();
                point.X = item.Position.X;
                point.Y = item.Position.Y;
                ptds.Add(point);
            }
            return ptds;


        }

      



        /// <summary>
        /// 得到多段线角点坐标
        /// </summary>
        /// <param name="polyline"></param>
        /// <returns></returns>
        public static List<Point3d> GetPolylineLocations(this Polyline polyline)

        {
            List<Point3d> pts = new List<Point3d>();
            for (int i = 0; i < polyline.NumberOfVertices; i++)
            {
                pts.Add(polyline.GetPoint2dAt(i).ToPoint3d());//引线坐标
            }

            return pts;
        }

        public static Polyline LineToPolyline(this Line line)
        {
            Point2dCollection pts = new Point2dCollection();
            pts.Add(line.StartPoint.ToPoint2d());
            pts.Add(line.EndPoint.ToPoint2d());
            var p = ModelUtil.CreatPolyline(pts, 0, false);
            return p;
        }

        public static List<Point3d> Point3dCollection2ToListPoint3d(this Point3dCollection pts)
        {
            List<Point3d> points = new List<Point3d>();

            foreach (Point3d p in pts)
            {
                points.Add(p);
            }

            return points;
        }


        /// <summary>
        /// 返回盒子中点
        /// </summary>
        /// <param name="ext">盒子</param>
        /// <returns></returns>
        public static Point3d MiddlePoint(this Extents3d ext)
        {
            double x = (ext.MaxPoint.X + ext.MinPoint.X) / 2;
            double y = (ext.MaxPoint.Y + ext.MinPoint.Y) / 2;
            double z = (ext.MaxPoint.Z + ext.MinPoint.Z) / 2;
            Point3d mp = new Point3d(x, y, z);
            return mp;
        }



        public static LineSegment2d LineToLineSegment2d(this Line line)
        {
            return new LineSegment2d(line.StartPoint.ToPoint2d(), line.EndPoint.ToPoint2d());
        }

        public static Line LineSegment2dToLine(this LineSegment2d line)
        {
            return new Line(line.StartPoint.ToPoint3d(), line.EndPoint.ToPoint3d());
        }

        /// <summary>
        /// 弧度转化为角度
        /// </summary>
        /// <param name="degree">角度</param>
        /// <returns></returns>
        public static double RadToAngle(this double degree)
        {
            return degree * 180 / Math.PI;
        }

        /// <summary>
        /// 角度转换为弧度
        /// </summary>
        /// <param name="angle">弧度</param>
        /// <returns></returns>
        public static double AngleToRad(this double angle)
        {
            return angle * Math.PI / 180;
        }
        public static Point3d ToPoint3d(this Coordinate coordinate)
        {

            return new Point3d(coordinate.X, coordinate.Y, coordinate.Z);
        }


        public static Coordinate ToCoordinates(this Point3d point3D)
        {

            return new Coordinate(point3D.X, point3D.Y, point3D.Z);
        }

        public static Polyline3d GetPolyline3d(this IGeometry geometry)
        {
            var cds = geometry.Coordinates.Take(3).Select(cd => cd.ToPoint3d()).ToArray();
            return new Polyline3d(Poly3dType.SimplePoly, new Point3dCollection(cds), true);
        }

        public static List<Point3d> GetPoints(this IGeometry geometry)
        {
            List<Point3d> cds = geometry.Coordinates.Take(3).Select(cd => cd.ToPoint3d()).ToList();
            return cds;
        }
        public static Point3dCollection GetVertices(this Polyline3d pl)
        {
            Point3dCollection pts = new Point3dCollection();
            using (Transaction tran = pl.Database.TransactionManager.StartTransaction())
            {
                foreach (ObjectId id in pl)
                {
                    //不可直接用point3d类
                    PolylineVertex3d pt = (PolylineVertex3d)tran.GetObject(
                        id, OpenMode.ForRead);
                    pts.Add(pt.Position);
                }
                tran.Commit();
            }
            return pts;
        }








    }
}
