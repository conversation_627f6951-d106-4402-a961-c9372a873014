﻿using OfficeOpenXml;
using SmartRouting.App.Commands.光缆数据.View;
using SmartRouting.App.Utils.EpplusUtils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Application = Autodesk.AutoCAD.ApplicationServices.Application;
using ComboBox = System.Windows.Forms.ComboBox;


namespace SmartRouting.App.Commands.光缆数据
{
    public class ImportExcelWpf
    {
        [CommandMethod("ImportExcelWpfCmd")]
        public static void ImportExcelWpfCmd()
        {
            Document doc = W.Doc;
            Editor ed = W.Ed;
            // 1. 选择Excel文件 (逻辑不变)
            PromptOpenFileOptions pofo = new PromptOpenFileOptions("选择要导入的Excel文件");
            pofo.Filter = "Excel 文件 (*.xlsx)|*.xlsx";
            PromptFileNameResult pfnr = ed.GetFileNameForOpen(pofo);
            if (pfnr.Status != PromptStatus.OK) return;
            string filePath = pfnr.StringResult;

            // 2. 获取并选择工作表 (逻辑不变)
            List<string> sheetNames;
            try { sheetNames = ExcelHelper_Epplus.GetSheetNames(filePath); } // GetExcelSheetNames 是之前定义的辅助方法
            catch (System.Exception ex) { ed.WriteMessage($"\n读取失败: {ex.Message}"); return; }
            if (sheetNames.Count == 0) { ed.WriteMessage("\n文件中无工作表。"); return; }

            string selectedSheetName = sheetNames[0];
            if (sheetNames.Count > 1)
            {
                // 仍然使用一个简单的WinForm来选择Sheet，因为它足够简单
                using (var sheetForm = new Form { Text = "选择工作表", StartPosition = FormStartPosition.CenterScreen, Size = new System.Drawing.Size(300, 120) })
                {
                    var combo = new ComboBox { DataSource = sheetNames, Dock = DockStyle.Top, Margin = new Padding(10) };
                    var btn = new System.Windows.Forms.Button { Text = "确定", Dock = DockStyle.Bottom, DialogResult = DialogResult.OK };
                    sheetForm.Controls.Add(combo);
                    sheetForm.Controls.Add(btn);
                    sheetForm.AcceptButton = btn;
                    if (Application.ShowModalDialog(sheetForm) != System.Windows.Forms.DialogResult.OK) return;
                    selectedSheetName = combo.SelectedItem.ToString();
                }
            }

            // 3. 实例化并显示 WPF 窗口
            WpfVisualSelect wpfWindow = new WpfVisualSelect(filePath, selectedSheetName);

            // 使用 ShowModalWindow 来显示 WPF 窗口
            bool? result =CadApp.ShowModalWindow(wpfWindow);

            if (result != true)
            {
                ed.WriteMessage("\n操作已取消。");
                return;
            }

            // 4. 获取用户选择的数据
            List<List<string>> data = wpfWindow.SelectedData;
            if (data == null || data.Count == 0)
            {
                ed.WriteMessage("\n没有选择任何数据。");
                return;
            }

            // 5. 创建并插入AutoCAD表格 (逻辑不变)
            using (Transaction tr = doc.Database.TransactionManager.StartTransaction())
            {
                Table table = ExcelHelper_Epplus. CreateAcadTable(doc.Database, data); // CreateAcadTable 是之前定义的辅助方法
                PromptPointResult ppr = ed.GetPoint("\n请选择表格的插入点:");
                if (ppr.Status != PromptStatus.OK) return;
                table.Position = ppr.Value;

                BlockTableRecord ms = (BlockTableRecord)tr.GetObject(SymbolUtilityServices.GetBlockModelSpaceId(doc.Database), OpenMode.ForWrite);
                ms.AppendEntity(table);
                tr.AddNewlyCreatedDBObject(table, true);

                tr.Commit();
                ed.WriteMessage($"\n成功创建表格，共 {data.Count} 行, {data[0].Count} 列。");
            }
        }
    }


  
}
