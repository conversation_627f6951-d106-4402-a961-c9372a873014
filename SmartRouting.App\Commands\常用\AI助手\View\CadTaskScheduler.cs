﻿namespace SmartRouting.App.Commands.常用.AI助手.View
{
    /// <summary>
    /// 一个简单的调度器，用于将WPF等非主线程的操作封送到CAD主线程中安全执行。
    /// 它使用Application.Idle事件来确保代码在正确的上下文中运行。
    /// </summary>
    public static class CadTaskScheduler
    {
        private static readonly Queue<Action> _taskQueue = new Queue<Action>();
        private static bool _isSubscribed = false;

        /// <summary>
        /// 将一个操作排入队列，以便在AutoCAD主线程上执行。
        /// </summary>
        /// <param name="action">要执行的操作。</param>
        public static void Enqueue(Action action)
        {
            if (action == null) return;
            lock (_taskQueue)
            {
                _taskQueue.Enqueue(action);
                SubscribeToIdle();
            }
        }

        private static void SubscribeToIdle()
        {
            // 只在未订阅时订阅，避免重复
            if (!_isSubscribed)
            {
                Application.Idle += OnApplicationIdle;
                _isSubscribed = true;
            }
        }

        private static void OnApplicationIdle(object sender, EventArgs e)
        {
            // 先取消订阅，这样我们的任务只会被执行一次。
            // 如果在任务执行期间有新的任务入队，SubscribeToIdle会重新订阅。
            Application.Idle -= OnApplicationIdle;
            _isSubscribed = false;

            // 在一个循环中执行队列中的所有任务，直到队列为空。
            while (true)
            {
                Action action = null;
                lock (_taskQueue)
                {
                    if (_taskQueue.Count > 0)
                    {
                        action = _taskQueue.Dequeue();
                    }
                }

                if (action != null)
                {
                    try
                    {
                        action.Invoke();
                    }
                    catch (Exception ex)
                    {
                        var doc = Application.DocumentManager.MdiActiveDocument;
                        if (doc != null)
                        {
                            doc.Editor.WriteMessage($"\n[CadTaskScheduler] Error executing task: {ex.Message}\n{ex.StackTrace}\n");
                        }
                    }
                }
                else
                {
                    // 队列为空，退出循环
                    break;
                }
            }
        }
    }
}
