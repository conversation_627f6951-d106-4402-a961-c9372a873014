﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartRouting.App.Utils
{
    public class MessageBoxUtil
    {
        public static void MessageInformation(string str)
        {
            MessageBox.Show(str, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        public static void Message_Error(object str)
        {
            MessageBox.Show(str + "", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        public static void MessageOutPutLog(string str, string path = @"D:\CADLog.txt")
        {

            try
            {
                //if (File.Exists(path))
                //{
                //    File.Delete(path);
                //}
                File.AppendAllText(path, str + "\r\n");
            }
            catch (System.Exception e)
            {

                MessageBoxUtil.Message_Error(e.Message);
            }

        }
    }
}
