﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="SmartRouting.App.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="线图层" Type="System.String" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="ExcelPath" Type="System.String" Scope="User">
      <Value Profile="(Default)">c:\</Value>
    </Setting>
    <Setting Name="LastCodes" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Prompt" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ApiKey" Type="System.String" Scope="User">
      <Value Profile="(Default)">请输入您的ApiKey</Value>
    </Setting>
    <Setting Name="工井照片" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="SS" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
  </Settings>
</SettingsFile>