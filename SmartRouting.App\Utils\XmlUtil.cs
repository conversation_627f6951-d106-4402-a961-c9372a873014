﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace SmartRouting.App.Utils
{
    /// <summary>
    /// XML文件操作类
    /// </summary>
    public class XmlUtil
    {
        public static string ReadAttribute(string xmlPath, string NodeName, string SettingName, string inputpara)
        {
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.Load(xmlPath);
            XmlElement xmlElem = xmlDoc.DocumentElement;//获取根节点
            XmlNodeList bodyNode = xmlElem.GetElementsByTagName(NodeName);
            if (bodyNode.Count > 0)
            {
                XmlElement xe = (XmlElement)bodyNode[0];
                if (xe.HasAttribute(SettingName))
                {
                    string Name = xe.Attributes[SettingName].Value;
                    return Name;
                }
                else
                {
                    xe.SetAttribute(SettingName, inputpara);
                    xmlDoc.Save(xmlPath);
                    return inputpara;
                }
            }
            else
            {
                XmlElement element1 = xmlDoc.CreateElement(NodeName);
                element1.SetAttribute(SettingName, inputpara);
                xmlElem.AppendChild(element1);
                xmlDoc.Save(xmlPath);
                return inputpara;
            }

        }

        public static void ModifyAttribute(string xmlPath, string NodeName, string SettingName, string SettingValue)
        {
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.Load(xmlPath);
            XmlElement xmlElem = xmlDoc.DocumentElement;//获取根节点
            XmlNodeList bodyNode = xmlElem.GetElementsByTagName(NodeName);
            bodyNode[0].Attributes[SettingName].Value = SettingValue;
            xmlDoc.Save(xmlPath);
        }
















    }

    // 定义XML根元素
    [XmlRoot("User_Menu")]
    public class UserMenu
    {
        [XmlAttribute("Background")]
        public string Background { get; set; }

        [XmlElement("Group")]
        public List<Group> Groups { get; set; }
    }

    // 定义Group元素
    public class Group
    {
        [XmlAttribute("Title")]
        public string Title { get; set; }

        [XmlAttribute("Background")]
        public string Background { get; set; }

        [XmlAttribute("Foreground")]
        public string Foreground { get; set; }

        [XmlElement("Item")]
        public List<Item> Items { get; set; }
    }

    // 定义Item元素
    public class Item
    {
        [XmlAttribute("Cmd")]
        public string Cmd { get; set; }

        [XmlAttribute("Text")]
        public string Text { get; set; }

        [XmlAttribute("Background")]
        public string Background { get; set; }

        [XmlAttribute("Foreground")]
        public string Foreground { get; set; }

        [XmlAttribute("ToolTip")]
        public string ToolTip { get; set; }
    }



    public class MenuData2Xml
    {
        public static UserMenu ConvertTuplesToGMDIMenu(List<Tuple<string, string, string, string>> tuples)
        {
            // 创建GMDIMenu对象
            UserMenu menu = new UserMenu
            {
                Background = "#F0F0F0",


                Groups = new List<Group>()
            };

            // 使用字典来分组元组
            var groupDict = new Dictionary<string, Group>();

            foreach (var tuple in tuples)
            {
                string groupTitle = tuple.Item1;
                string cmd = tuple.Item2;
                string text = tuple.Item3;
                string toolTip = tuple.Item4;

                // 如果组不存在，创建新组
                if (!groupDict.ContainsKey(groupTitle))
                {
                    Group group = new Group
                    {
                        Title = groupTitle,
                        Background = "#F0F0F0",
                        Foreground = "#000000",
                        Items = new List<Item>()
                    };
                    groupDict[groupTitle] = group;
                    menu.Groups.Add(group);
                }

                // 添加Item到对应的组
                groupDict[groupTitle].Items.Add(new Item
                {
                    Cmd = cmd,
                    Text = text,
                    Background = "#F0F0F0",
                    Foreground = "#000000",
                    ToolTip = toolTip
                });
            }

            return menu;
        }




    }

}
