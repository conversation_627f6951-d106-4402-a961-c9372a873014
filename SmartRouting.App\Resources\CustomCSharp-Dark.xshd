<?xml version="1.0"?>
<SyntaxDefinition name="C#" extensions=".cs" xmlns="http://icsharpcode.net/sharpdevelop/syntaxdefinition/2008">

    <Color name="Comment" foreground="#6A9955" />
    <Color name="String" foreground="#CE9178" />
    <Color name="Char" foreground="#CE9178" />
    <Color name="Preprocessor" foreground="#808080" />
    <Color name="Punctuation" foreground="#D4D4D4" />
    <Color name="MethodName" foreground="#DCDCAA" />
    <Color name="Number" foreground="#B5CEA8" />

    <Color name="Keywords" foreground="#569CD6" fontWeight="bold"/>
    <Color name="TypeKeywords" foreground="#569CD6" fontWeight="bold" />
    <Color name="ControlKeywords" foreground="#C586C0" fontWeight="bold" />

    <Color name="Visibility" foreground="#569CD6" fontWeight="bold" />
    <Color name="Namespace" foreground="#569CD6" fontWeight="bold" />
    <Color name="TypeName" foreground="#4EC9B0" />
    <Color name="ParameterName" foreground="#9CDCFE" />

    <!-- This is the main ruleset. -->
    <RuleSet>
        <Span color="Preprocessor">
            <Begin>\#</Begin>
        </Span>

        <Span color="Comment">
            <Begin>//</Begin>
        </Span>
        <Span color="Comment" multiline="true">
            <Begin>/\*</Begin>
            <End>\*/</End>
        </Span>

        <Span color="String">
            <Begin>"</Begin>
            <End>"</End>
            <RuleSet>
                <Span>
                    <Begin>\\</Begin>
                    <End>.</End>
                </Span>
            </RuleSet>
        </Span>
        
        <Span color="String" multiline="true">
			<Begin>@"[^"]</Begin>
			<End>"</End>
			<RuleSet>
				<!-- An escaped quote is a quote that is preceded by a quote. -->
				<Span>
					<Begin>""</Begin>
				</Span>
			</RuleSet>
		</Span>

        <Span color="Char">
            <Begin>'</Begin>
            <End>'</End>
            <RuleSet>
                <Span>
                    <Begin>\\</Begin>
                    <End>.</End>
                </Span>
            </RuleSet>
        </Span>

        <Keywords color="ControlKeywords">
            <Word>if</Word>
            <Word>else</Word>
            <Word>while</Word>
            <Word>for</Word>
            <Word>foreach</Word>
            <Word>do</Word>
            <Word>switch</Word>
            <Word>case</Word>
            <Word>default</Word>
            <Word>break</Word>
            <Word>continue</Word>
            <Word>return</Word>
            <Word>throw</Word>
            <Word>try</Word>
            <Word>catch</Word>
            <Word>finally</Word>
            <Word>goto</Word>
            <Word>yield</Word>
            <Word>new</Word>
        </Keywords>
        
        <Keywords color="TypeKeywords">
            <Word>bool</Word>
            <Word>byte</Word>
            <Word>char</Word>
            <Word>class</Word>
            <Word>decimal</Word>
            <Word>delegate</Word>
            <Word>double</Word>
            <Word>enum</Word>
            <Word>float</Word>
            <Word>int</Word>
            <Word>interface</Word>
            <Word>long</Word>
            <Word>object</Word>
            <Word>sbyte</Word>
            <Word>short</Word>
            <Word>string</Word>
            <Word>struct</Word>
            <Word>uint</Word>
            <Word>ulong</Word>
            <Word>ushort</Word>
            <Word>void</Word>
        </Keywords>

        <Keywords color="Keywords">
            <Word>as</Word>
            <Word>base</Word>
            <Word>checked</Word>
            <Word>const</Word>
            <Word>event</Word>
            <Word>explicit</Word>
            <Word>extern</Word>
            <Word>false</Word>
            <Word>fixed</Word>
            <Word>implicit</Word>
            <Word>in</Word>
            <Word>is</Word>
            <Word>lock</Word>
            <Word>null</Word>
            <Word>operator</Word>
            <Word>out</Word>
            <Word>override</Word>
            <Word>params</Word>
            <Word>readonly</Word>
            <Word>ref</Word>
            <Word>sealed</Word>
            <Word>sizeof</Word>
            <Word>static</Word>
            <Word>this</Word>
            <Word>true</Word>
            <Word>typeof</Word>
            <Word>unchecked</Word>
            <Word>unsafe</Word>
            <Word>using</Word>
            <Word>virtual</Word>
            <Word>volatile</Word>
        </Keywords>

        <Keywords color="Visibility">
            <Word>public</Word>
            <Word>private</Word>
            <Word>protected</Word>
            <Word>internal</Word>
            <Word>abstract</Word>
        </Keywords>
        
        <Keywords color="Namespace">
            <Word>namespace</Word>
        </Keywords>

        <!-- Mark previous token as type name -->
        <Rule color="TypeName">
            \b[A-Z][a-zA-Z0-9_]*
        </Rule>

        <!-- Mark previous token as method name -->
        <Rule color="MethodName">
            \b[A-Z][a-zA-Z0-9_]*(?=\s*\()
        </Rule>

        <!-- Digits -->
        <Rule color="Number">
            \b\d+[\.]?\d*([eE][\-\+]\d+)?
        </Rule>

        <Rule color="Punctuation">
            [?,.;()\[\]{}+\-/%*&lt;&gt;^+~|]
        </Rule>
    </RuleSet>
</SyntaxDefinition> 