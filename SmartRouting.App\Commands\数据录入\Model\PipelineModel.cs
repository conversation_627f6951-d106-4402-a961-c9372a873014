﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.数据录入.Model
{


    /// <summary>
    /// 管道类（线类）
    /// </summary>
    public class PipelineModel
    {


        public event PropertyChangedEventHandler PropertyChanged;

        protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private string _pipelineNumber;
        private TelecomOperatorEnum _telecomOperatorEnum;
        private MaterialEnum _materialEnum;
        private int _holeCount;
        private string _remarks;
        private WellModel _fromWell;
        private WellModel _toWell;
       // private ObjectId _pipelineId;

        //public ObjectId PipelineId
        //{
        //    get => _pipelineId;
        //    set
        //    {
        //        if (_pipelineId != value)
        //        {
        //            _pipelineId = value;
        //            OnPropertyChanged();
        //        }
        //    }
        //}


        /// <summary>
        ///  编号 
        /// </summary>
        public string PipelineNumber
        {
            get => _pipelineNumber;
            set
            {
                if (_pipelineNumber != value)
                {
                    _pipelineNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 权属
        /// </summary>
        public TelecomOperatorEnum TelecomOperatorEnum
        {
            get => _telecomOperatorEnum;
            set
            {
                if (_telecomOperatorEnum != value)
                {
                    _telecomOperatorEnum = value;
                    OnPropertyChanged();
                }
            }
        }





        /// <summary>
        /// 材质
        /// </summary>
        public MaterialEnum MaterialEnum
        {
            get => _materialEnum;
            set
            {
                if (_materialEnum != value)
                {
                    _materialEnum = value;
                    OnPropertyChanged();
                }
            }
        }





        /// <summary>
        /// 孔数
        /// </summary>
        public int HoleCount
        {
            get => _holeCount;
            set
            {
                if (_holeCount != value)
                {
                    _holeCount = value;
                    OnPropertyChanged();
                }
            }
        }




        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks
        {
            get => _remarks;
            set
            {
                if (_remarks != value)
                {
                    _remarks = value;
                    OnPropertyChanged();
                }
            }
        }


        /// <summary>
        /// 管道中分配来的线缆集合
        /// </summary>
        public ObservableCollection<CableModel> CableModels { get; set; }


        /// <summary>
        /// 这个管道来着哪个工井
        /// </summary>
        public WellModel FromWell
        {
            get => _fromWell;
            set
            {
                if (_fromWell != value)
                {
                    _fromWell = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 这个管道到哪个工井
        /// </summary>
        public WellModel ToWell
        {
            get => _toWell;
            set
            {
                if (_toWell != value)
                {
                    _toWell = value;
                    OnPropertyChanged();
                }
            }
        }

    }
}
