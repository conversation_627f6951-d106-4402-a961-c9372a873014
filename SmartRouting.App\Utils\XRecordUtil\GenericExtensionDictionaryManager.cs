﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils.XRecordUtil
{
    public static class GenericExtensionDictionaryManager
    {
        /// <summary>
        /// 将对象转换为TypedValueList
        /// </summary>
        public static TypedValueList ObjectToTypeValueList<T>(T obj)
        {
            if (obj == null)
                throw new ArgumentNullException(nameof(obj));

            var typeValueList = new TypedValueList();

            // 检查是否继承自BindableBase
            if (obj is BindableBase bindableObj)
            {
                return SerializeBindableBase(bindableObj);
            }

            // 普通对象序列化
            var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && p.CanWrite)
                .Where(p => !p.GetCustomAttributes(typeof(ExtensionDictionaryIgnoreAttribute), false).Any())
                .Where(p => !p.GetCustomAttributes(typeof(JsonIgnoreAttribute), false).Any())
                .ToArray();

            foreach (var property in properties)
            {
                try
                {
                    var value = property.GetValue(obj);
                    var typedValue = ConvertToTypedValue(value, property.PropertyType);

                    // 添加属性名作为标识
                    typeValueList.Add(new TypedValue((int)DxfCode.Text, $"PROP:{property.Name}"));
                    typeValueList.Add(typedValue);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"序列化属性 {property.Name} 失败: {ex.Message}");
                }
            }

            return typeValueList;
        }

        /// <summary>
        /// 专门处理BindableBase对象的序列化
        /// </summary>
        private static TypedValueList SerializeBindableBase(BindableBase bindableObj)
        {
            var typeValueList = new TypedValueList();

            // 添加类型信息
            typeValueList.Add(new TypedValue((int)DxfCode.Text, $"TYPE:{bindableObj.GetType().FullName}"));

            // 获取所有公共属性定义（用于类型检查）
            var propertyDefinitions = bindableObj.GetType()
                .GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && p.CanWrite)
                .Where(p => !p.GetCustomAttributes(typeof(ExtensionDictionaryIgnoreAttribute), false).Any())
                .Where(p => !p.GetCustomAttributes(typeof(JsonIgnoreAttribute), false).Any())
                .ToDictionary(p => p.Name, p => p);

            // 序列化PropertyBag中的属性
            foreach (var kvp in bindableObj.SerializableProperties)
            {
                var propertyName = kvp.Key;
                var value = kvp.Value;

                // 跳过计算属性和忽略的属性
                if (!propertyDefinitions.ContainsKey(propertyName))
                    continue;

                try
                {
                    var propertyType = propertyDefinitions[propertyName].PropertyType;
                    var typedValue = ConvertToTypedValue(value, propertyType);

                    // 添加属性名作为标识
                    typeValueList.Add(new TypedValue((int)DxfCode.Text, $"PROP:{propertyName}"));
                    typeValueList.Add(typedValue);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"序列化BindableBase属性 {propertyName} 失败: {ex.Message}");
                }
            }

            return typeValueList;
        }

        /// <summary>
        /// 将TypedValueList转换为对象
        /// </summary>
        public static T TypeValueListToObject<T>(TypedValueList typeValueList) where T : new()
        {
            if (typeValueList == null)
                throw new ArgumentNullException(nameof(typeValueList));

            var obj = new T();

            // 检查是否继承自BindableBase
            if (obj is BindableBase bindableObj)
            {
                return (T)(object)DeserializeBindableBase<T>(typeValueList, bindableObj);
            }

            // 普通对象反序列化
            var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && p.CanWrite)
                .ToDictionary(p => p.Name, p => p);

            for (int i = 0; i < typeValueList.Count - 1; i++)
            {
                var current = typeValueList[i];

                // 查找属性标识
                if (current.TypeCode == (int)DxfCode.Text &&
                    current.Value is string propIdentifier &&
                    propIdentifier.StartsWith("PROP:"))
                {
                    var propertyName = propIdentifier.Substring(5); // 移除 "PROP:" 前缀

                    if (properties.TryGetValue(propertyName, out var property) && i + 1 < typeValueList.Count)
                    {
                        try
                        {
                            var valueTypedValue = typeValueList[i + 1];
                            var convertedValue = ConvertFromTypedValue(valueTypedValue, property.PropertyType);
                            property.SetValue(obj, convertedValue);
                            i++; // 跳过下一个值，因为已经处理了
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"转换属性 {propertyName} 失败: {ex.Message}");
                        }
                    }
                }
            }

            return obj;
        }

        /// <summary>
        /// 专门处理BindableBase对象的反序列化
        /// </summary>
        private static T DeserializeBindableBase<T>(TypedValueList typeValueList, BindableBase bindableObj) where T : new()
        {
            // 获取所有公共属性定义
            var propertyDefinitions = typeof(T)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && p.CanWrite)
                .Where(p => !p.GetCustomAttributes(typeof(ExtensionDictionaryIgnoreAttribute), false).Any())
                .Where(p => !p.GetCustomAttributes(typeof(JsonIgnoreAttribute), false).Any())
                .ToDictionary(p => p.Name, p => p);

            for (int i = 0; i < typeValueList.Count - 1; i++)
            {
                var current = typeValueList[i];

                // 跳过类型信息
                if (current.TypeCode == (int)DxfCode.Text &&
                    current.Value is string typeIdentifier &&
                    typeIdentifier.StartsWith("TYPE:"))
                {
                    continue;
                }

                // 查找属性标识
                if (current.TypeCode == (int)DxfCode.Text &&
                    current.Value is string propIdentifier &&
                    propIdentifier.StartsWith("PROP:"))
                {
                    var propertyName = propIdentifier.Substring(5); // 移除 "PROP:" 前缀

                    if (propertyDefinitions.ContainsKey(propertyName) && i + 1 < typeValueList.Count)
                    {
                        try
                        {
                            var valueTypedValue = typeValueList[i + 1];
                            var propertyType = propertyDefinitions[propertyName].PropertyType;
                            var convertedValue = ConvertFromTypedValue(valueTypedValue, propertyType);

                            // 通过BindableBase的属性设置器设置值，这会自动处理PropertyBag
                            var property = propertyDefinitions[propertyName];
                            property.SetValue(bindableObj, convertedValue);

                            i++; // 跳过下一个值，因为已经处理了
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"转换BindableBase属性 {propertyName} 失败: {ex.Message}");
                        }
                    }
                }
            }

            return (T)(object)bindableObj;
        }

        /// <summary>
        /// 将值转换为TypedValue
        /// </summary>
        private static TypedValue ConvertToTypedValue(object value, Type type)
        {
            if (value == null)
                return new TypedValue((int)DxfCode.Text, "");

            // 处理枚举类型 - 序列化为字符串
            if (type.IsEnum)
            {
                return new TypedValue((int)DxfCode.Text, value.ToString());
            }

            // 处理可空枚举类型
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                var underlyingType = Nullable.GetUnderlyingType(type);
                if (underlyingType != null && underlyingType.IsEnum)
                {
                    return new TypedValue((int)DxfCode.Text, value.ToString());
                }
            }

            // 处理集合类型
            if (typeof(IEnumerable).IsAssignableFrom(type) && type != typeof(string))
            {
                var json = JsonConvert.SerializeObject(value);
                return new TypedValue((int)DxfCode.Text, json);
            }

            // 处理基本类型
            switch (Type.GetTypeCode(type))
            {
                case TypeCode.String:
                    return new TypedValue((int)DxfCode.Text, value.ToString());
                case TypeCode.Int32:
                    return new TypedValue((int)DxfCode.Int32, value);
                case TypeCode.Int16:
                    return new TypedValue((int)DxfCode.Int16, value);
                case TypeCode.Double:
                    return new TypedValue((int)DxfCode.Real, value);
                case TypeCode.Boolean:
                    return new TypedValue((int)DxfCode.Bool, value);
                case TypeCode.DateTime:
                    return new TypedValue((int)DxfCode.Text, ((DateTime)value).ToString("yyyy-MM-dd HH:mm:ss"));
                default:
                    // 对于复杂类型，使用JSON序列化
                    var json = JsonConvert.SerializeObject(value);
                    return new TypedValue((int)DxfCode.Text, json);
            }
        }

        /// <summary>
        /// 将TypedValue转换为指定类型的值
        /// </summary>
        private static object ConvertFromTypedValue(TypedValue typedValue, Type targetType)
        {
            var value = typedValue.Value;

            if (value == null)
                return GetDefaultValue(targetType);

            // 处理枚举类型
            if (targetType.IsEnum)
            {
                if (value is string stringValue)
                {
                    try
                    {
                        return Enum.Parse(targetType, stringValue);
                    }
                    catch
                    {
                        var enumValues = Enum.GetValues(targetType);
                        return enumValues.Length > 0 ? enumValues.GetValue(0) : GetDefaultValue(targetType);
                    }
                }
                return Enum.ToObject(targetType, value);
            }

            // 处理可空枚举类型
            if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                var underlyingType = Nullable.GetUnderlyingType(targetType);
                if (underlyingType != null && underlyingType.IsEnum && value is string stringValue)
                {
                    try
                    {
                        return Enum.Parse(underlyingType, stringValue);
                    }
                    catch
                    {
                        return null;
                    }
                }
            }

            // 处理集合类型
            if (typeof(IEnumerable).IsAssignableFrom(targetType) && targetType != typeof(string))
            {
                if (value is string jsonString && !string.IsNullOrEmpty(jsonString))
                {
                    try
                    {
                        return JsonConvert.DeserializeObject(jsonString, targetType);
                    }
                    catch
                    {
                        return GetDefaultValue(targetType);
                    }
                }
                return GetDefaultValue(targetType);
            }

            // 处理DateTime类型
            if (targetType == typeof(DateTime) || targetType == typeof(DateTime?))
            {
                if (value is string dateString && DateTime.TryParse(dateString, out var dateTime))
                {
                    return dateTime;
                }
                return targetType == typeof(DateTime?) ? (DateTime?)null : DateTime.MinValue;
            }

            // 处理基本类型转换
            try
            {
                if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
                {
                    var underlyingType = Nullable.GetUnderlyingType(targetType);
                    if (value.ToString() == "")
                        return null;
                    return Convert.ChangeType(value, underlyingType);
                }

                return Convert.ChangeType(value, targetType);
            }
            catch
            {
                // 如果基本类型转换失败，尝试JSON反序列化
                if (value is string jsonString && !string.IsNullOrEmpty(jsonString))
                {
                    try
                    {
                        return JsonConvert.DeserializeObject(jsonString, targetType);
                    }
                    catch
                    {
                        return GetDefaultValue(targetType);
                    }
                }

                return GetDefaultValue(targetType);
            }
        }

        /// <summary>
        /// 获取类型的默认值
        /// </summary>
        private static object GetDefaultValue(Type type)
        {
            if (type.IsValueType)
            {
                return Activator.CreateInstance(type);
            }
            return null;
        }
    }
}
