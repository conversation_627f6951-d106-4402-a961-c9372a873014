﻿using SmartRouting.App.Utils.NetUtil;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App
{
    public static class GlobalSetting
    {
        public static readonly string SaveCurveAndBlockTypeFilePath = "C:\\Plugin_ForCAD\\Config\\CurveAndBlockType.json";
        public static readonly string FilePath = "C:\\Plugin_ForCAD\\Config\\ModelConfig.json";
        //public static readonly string RegAppName_Well = "邑排摸信息_井体";
        //public static readonly string RegAppName_Pipe = "邑排摸信息_管道";

        /// <summary>
        /// 获取最后使用日期
        /// </summary>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <param name="day"></param>
        /// <returns></returns>
        [Obsolete]

        public static bool SetDeadLine(int year = 2025, int month = 12, int day = 30)
        {
            var beyondDeadLine = true;//超过使用期限
            try
            {
                var f = NetCertificatePolicy.VerifyNetDateTime(() =>
                {
                    var GMT = NetCertificatePolicy.GetNetDateTimes();//格林时间
                    var datetimeNow = NetCertificatePolicy.GMT2Local(GMT);//格林时间转化为本地时间
                    DateTime deadLine = new DateTime(year, month, day);
                    var detal = (deadLine - datetimeNow).TotalDays;
                    if (detal <= 0) return beyondDeadLine = true;
                    if (detal > 0) return beyondDeadLine = false;
                    return beyondDeadLine;
                });
            }
            catch (System.Exception)
            {

            }

            return beyondDeadLine;
        }
        /// <summary>
        /// 气泡弹窗显示
        /// </summary>
        public static void ShowBalloonTip()
        {
            // 显示成功通知
            var notifyIcon = new System.Windows.Forms.NotifyIcon
            {
                Icon = System.Drawing.SystemIcons.Information,
                Visible = true,
                BalloonTipTitle = "提示!",
                BalloonTipText = "信息录入成功!"
            };
            notifyIcon.BalloonTipClosed += (s, args) => (s as System.Windows.Forms.NotifyIcon)?.Dispose();
            notifyIcon.ShowBalloonTip(200);
        }
    }

}
