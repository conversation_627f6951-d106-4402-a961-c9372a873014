﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;

namespace SmartRouting.App.MVVM
{
    /// <summary>
    /// 泛型参数委托命令
    /// </summary>
    public class DelegateCommand<T> : ICommand where T : class
    {
        /// <summary>
        /// 是否可执行命令变更事件
        /// </summary>
        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        /// <summary>
        /// 命令所需执行的事件
        /// </summary>
        public Action<T> ExecuteCommand { get; set; }


        /// <summary>
        /// 命令是否可用所执行的事件
        /// </summary>
        public Func<T, bool> CanExecuteCommand { get; set; }

        ///// <summary>
        ///// 设置无参数执行命令
        ///// </summary>
        ///// <param name="execute"></param>
        //public DelegateCommand(Action execute)
        //{
        //    ExecuteCommand = new Action<T>((obj) => { execute(); });
        //}

        /// <summary>
        /// 设置执行命令
        /// </summary>
        /// <param name="execute"></param>
        public DelegateCommand(Action<T> execute)
        {
            ExecuteCommand = execute;
        }

        /// <summary>
        /// 设置执行命令和是否可执行命令
        /// </summary>
        /// <param name="execute"></param>
        /// <param name="canExecute"></param>
        public DelegateCommand(Action<T> execute, Func<T, bool> canExecute)
        {
            ExecuteCommand = execute;
            CanExecuteCommand = canExecute;
        }

        /// <summary>
        /// 命令可用性
        /// </summary>
        /// <param name="parameter"></param>
        /// <returns></returns>
        public virtual bool CanExecute(object parameter)
        {
            if (CanExecuteCommand != null)
            {
                return CanExecuteCommand(parameter as T);
            }
            else
            {
                return true;
            }
        }

        /// <summary>
        /// 命令具体执行
        /// </summary>
        /// <param name="parameter"></param>
        public virtual void Execute(object parameter)
        {
            ExecuteCommand(parameter as T);
        }
    }

    /// <summary>
    /// 委托命令
    /// </summary>
    public class DelegateCommand : ICommand
    {
        /// <summary>
        /// 是否可执行命令变更事件
        /// </summary>
        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        /// <summary>
        /// 命令所需执行的事件
        /// </summary>
        public Action<object> ExecuteCommand { get; set; }


        /// <summary>
        /// 命令是否可用所执行的事件
        /// </summary>
        public Func<object, bool> CanExecuteCommand { get; set; }

        /// <summary>
        /// 设置无参数执行命令
        /// </summary>
        /// <param name="execute"></param>
        public DelegateCommand(Action execute)
        {
            ExecuteCommand = new Action<object>((obj) => { execute(); });
        }

        /// <summary>
        /// 设置执行命令
        /// </summary>
        /// <param name="execute"></param>
        public DelegateCommand(Action<object> execute)
        {
            ExecuteCommand = execute;
        }

        /// <summary>
        /// 设置执行命令和是否可执行命令
        /// </summary>
        /// <param name="execute"></param>
        /// <param name="canExecute"></param>
        public DelegateCommand(Action<object> execute, Func<object, bool> canExecute)
        {
            ExecuteCommand = execute;
            CanExecuteCommand = canExecute;
        }

        /// <summary>
        /// 命令可用性
        /// </summary>
        /// <param name="parameter"></param>
        /// <returns></returns>
        public virtual bool CanExecute(object parameter)
        {
            if (CanExecuteCommand != null)
            {
                return CanExecuteCommand(parameter);
            }
            else
            {
                return true;
            }
        }

        /// <summary>
        /// 命令具体执行
        /// </summary>
        /// <param name="parameter"></param>
        public virtual void Execute(object parameter)
        {
            ExecuteCommand(parameter);
        }
    }
}
