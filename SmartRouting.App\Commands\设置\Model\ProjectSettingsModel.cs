﻿using SmartRouting.App.Commands.线路连接.Model;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Commands.设置.Model
{
    public class ProjectSettingsModel
    {
        /// <summary>
        /// 线图层集合
        /// </summary>
        public string CurveLayers { get; set; }
        /// <summary>
        /// 探测点集合
        /// </summary>
        public List<string> BlockNames { get; set; }
    }



}
