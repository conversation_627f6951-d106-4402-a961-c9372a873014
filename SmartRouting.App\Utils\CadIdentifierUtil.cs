﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    /// <summary>
    /// 提供检测当前CAD平台（AutoCAD/ZWCAD）及版本信息的功能。
    /// </summary>
    public static class CadIdentifierUtil
    {
        /// <summary>
        /// 定义CAD平台类型
        /// </summary>
        public enum CadPlatform
        {
            Unknown,
            AutoCAD,
            ZWCAD
        }

        private static CadPlatform? _platform;

        // AutoCAD版本号与产品年份的映射关系
        private static readonly Dictionary<string, string> AcadVersionMap = new Dictionary<string, string>
        {
            { "25.0", "AutoCAD 2025" },
            { "24.3", "AutoCAD 2024" },
            { "24.2", "AutoCAD 2023" },
            { "24.1", "AutoCAD 2022" },
            { "24.0", "AutoCAD 2021" },
            { "23.1", "AutoCAD 2020" },
            { "23.0", "AutoCAD 2019" },
            { "22.0", "AutoCAD 2018" },
            { "21.0", "AutoCAD 2017" },
            { "20.1", "AutoCAD 2016" },
            { "20.0", "AutoCAD 2015" },
            { "19.1", "AutoCAD 2014" },
            { "19.0", "AutoCAD 2013" },
            { "18.2", "AutoCAD 2012" },
            { "18.1", "AutoCAD 2011" },
            { "18.0", "AutoCAD 2010" },
            { "17.2", "AutoCAD 2009" },
            { "17.1", "AutoCAD 2008" },
            { "17.0", "AutoCAD 2007" }
        };

        /// <summary>
        /// 获取当前的CAD平台。
        /// </summary>
        /// <returns>返回一个 CadPlatform 枚举值。</returns>
        public static CadPlatform GetCadPlatform()
        {
            if (_platform.HasValue)
            {
                return _platform.Value;
            }

            string processName = Process.GetCurrentProcess().ProcessName.ToLower();

            if (processName.Contains("zwcad"))
            {
                _platform = CadPlatform.ZWCAD;
            }
            else if (processName.Contains("acad"))
            {
                _platform = CadPlatform.AutoCAD;
            }
            else
            {
                _platform = CadPlatform.Unknown;
            }

            return _platform.Value;
        }

        /// <summary>
        /// 获取AutoCAD的产品年份。
        /// </summary>
        /// <returns>如果当前是AutoCAD，则返回产品年份字符串（如 "2023"）；否则返回提示信息。</returns>
        public static string GetAutoCADVersion()
        {
            if (GetCadPlatform() != CadPlatform.AutoCAD)
            {
                return "Not running in AutoCAD.";
            }

            try
            {
                string acadVer = Application.GetSystemVariable("ACADVER").ToString();

                foreach (var entry in AcadVersionMap)
                {
                    if (acadVer.StartsWith(entry.Key))
                    {
                        // 从 "AutoCAD 2023" 中提取年份 "2023"
                        string[] parts = entry.Value.Split(' ');
                        return parts[parts.Length - 1];
                    }
                }

                return $"Unknown Version (ACADVER: {acadVer})";
            }
            catch (Exception ex)
            {
                return $"Failed to get AutoCAD version: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取ZWCAD的版本信息。
        /// </summary>
        /// <returns>如果当前是ZWCAD，则返回版本信息字符串；否则返回提示信息。</returns>
        public static string GetZWCADVersion()
        {
            if (GetCadPlatform() != CadPlatform.ZWCAD)
            {
                return "Not running in ZWCAD.";
            }

            try
            {
                object acadVer = Application.GetSystemVariable("ACADVER");
                return $"ZWCAD (ACADVER: {acadVer})";
            }
            catch (Exception ex)
            {
                return $"Failed to get ZWCAD version: {ex.Message}";
            }
        }
    }
}
