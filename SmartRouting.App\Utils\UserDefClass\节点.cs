﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils.UserDefClass
{
    public class 节点
    {
        public Point3d Pt;
        public List<线> 连接线 = new List<线>();
        public bool 标记 { get; set; } = false;
        public double Dist;
        public 节点 前点;
        public 节点(Point3d pt)
        {
            Pt = pt;
        }

    }
}
