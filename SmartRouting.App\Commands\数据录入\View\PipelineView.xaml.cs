﻿using SmartRouting.App.Commands.数据录入.Model;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using SmartRouting.App.Utils;
using GMDI_ForCAD.App.侧边菜单;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using SmartRouting.App.Utils.XRecordUtil;
using SmartRouting.App.Utils.Algorithms;

namespace SmartRouting.App.Commands.数据录入.View
{
    /// <summary>
    /// PipelineView.xaml 的交互逻辑
    /// </summary>
    public partial class PipelineView : Window
    {

        // 【新增】为 "权属" ComboBox 提供数据源
        // 这个属性返回 TelecomOperatorEnum 枚举的所有值的数组
        public Array TelecomOperatorEnumValues
        {
            get { return Enum.GetValues(typeof(TelecomOperatorEnum)); }
        }

        // 【新增】为 "材质" ComboBox 提供数据源
        // 这个属性返回 MaterialEnum 枚举的所有值的数组
        public Array MaterialEnumValues
        {
            get { return Enum.GetValues(typeof(MaterialEnum)); }
        }

        public PipelineModel m_PipelineModel { get; set; }

        /// <summary>
        /// 当前管道的ID
        /// </summary>
        private ObjectId m_ObjectId { get; set; }
        private Point3d m_Point3d { get; set; }
        /// <summary>
        /// 来自哪个工井的线缆
        /// </summary>
        private WellModel m_FromWell { get; set; }

        /// <summary>
        /// 到哪个工井的线缆
        /// </summary>
        private WellModel m_ToWell { get; set; }


        /// <summary>
        /// 对于当前管道没数据的情况
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fromWell"></param>
        /// <param name="toWell"></param>
        /// <param name="point3d"></param>
        public PipelineView(ObjectId id, WellModel fromWell, WellModel toWell, Point3d point3d)
        {

            m_ObjectId = id;
            m_FromWell = fromWell;
            m_Point3d = point3d;
            m_ToWell = toWell;
            InitializeComponent();
            InitializeData();
        }


        public PipelineView(ObjectId id, PipelineModel pipelineModel, Point3d point3d)
        {

            m_ObjectId = id;
            m_PipelineModel = pipelineModel;
            m_FromWell = m_PipelineModel.FromWell;
            m_ToWell = m_PipelineModel.ToWell;
            m_Point3d = point3d;
            InitializeComponent();
            InitializeData_ForExistData();
        }

        /// <summary>
        /// 如果存在数据，就直接提取数据显示
        /// </summary>
        private void InitializeData_ForExistData()
        {

            if (m_PipelineModel.CableModels != null && m_PipelineModel.CableModels.Count > 0 && m_PipelineModel.CableModels.All(x => x.IsSelected))
            {
                //针对已经录入信息的情况，但是没重置
                Btn_ImportData.IsEnabled = false;
                this.DataContext = m_PipelineModel;
            }
            else
            {

                var unUsedCount_Well = m_FromWell.CableModels.Where(x => !x.Used).Count();//来自的工井中没使用的个数
                var unUsedCount_Pipe = m_PipelineModel.CableModels.Where(x => !x.Used).Count();//当前管道中没使用的个数
              
                if (unUsedCount_Well == unUsedCount_Pipe)
                {
                 
                    //针对已经录入信息的情况，但是已经重置了，而且也没进行重新分配
                    m_PipelineModel.CableModels.Clear();
                    var cables = m_FromWell.CableModels.Where(x => !x.IsSelected);
                    foreach (var cable in cables)
                    {
                        m_PipelineModel.CableModels.Add(cable);
                    }
                    Btn_ImportData.IsEnabled = true;
                    this.DataContext = m_PipelineModel;
                }

                else
                {
                    //这种一般是管道的数据没更新过来
                   
                    m_PipelineModel.CableModels.Clear();
                    var cables = m_FromWell.CableModels.Where(x => !x.IsSelected);
                    foreach (var cable in cables)
                    {
                        m_PipelineModel.CableModels.Add(cable);
                    }
                    Btn_ImportData.IsEnabled = true;
                    this.DataContext = m_PipelineModel;
                }











            }

        }

        /// <summary>
        /// 当前管道不存在数据，需要从工井中提取
        /// </summary>
        private void InitializeData()
        {
            m_PipelineModel = new PipelineModel();
            ObservableCollection<CableModel> cableModels = new();
            var wellCables = m_FromWell.CableModels.Where(x => !x.Used);
            var fromWellName = m_FromWell.WellName;
            var toWellName = m_ToWell.WellName;

            foreach (var cableModel in wellCables)
            {
                cableModel.Used = false;
                cableModel.IsSelected = false;
                cableModels.Add(cableModel);
            }
            m_PipelineModel.Remarks = "";
            m_PipelineModel.HoleCount = 4;
            m_PipelineModel.CableModels = cableModels;
            m_PipelineModel.PipelineNumber = $"{fromWellName}->{toWellName}";
            m_PipelineModel.ToWell = m_ToWell;
            m_PipelineModel.FromWell = m_FromWell;
            this.DataContext = m_PipelineModel;
        }



        /// <summary>
        /// 从工井中分配线揽
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_ImportData_Click(object sender, RoutedEventArgs e)
        {

            using (var documentLock = W.DocLock)
            {
                using (var tr = W.Trans)
                {
                    W.FocusDoc();


                    var wellId = ExtensionDictionaryManager.FindFirstWellByName(m_FromWell.WellName);
                    if (m_PipelineModel.CableModels == null || m_PipelineModel.CableModels.Count == 0)
                    {
                        MessageBoxUtil.MessageInformation("没有可供选择的线缆。");
                        return;
                    }
                    var selectedCables = m_PipelineModel.CableModels.Where(c => c.IsSelected);

                    if (selectedCables.Any())
                    {
                        ObservableCollection<CableModel> cableModels = new ObservableCollection<CableModel>();
                        foreach (var item in selectedCables)
                        {
                            cableModels.Add(item);
                        }
                        m_PipelineModel.CableModels = cableModels;

                        try
                        {
                            // 安全检查：确认对象ID有效
                            if (m_ObjectId.IsNull || m_ObjectId.IsErased)
                            {
                                MessageBoxUtil.Message_Error("关联的对象已被删除，无法写入数据。");
                                return;
                            }

                            //同步更新相关工井的线缆信息
                            foreach (var wellCable in m_FromWell.CableModels)
                            {
                                if (selectedCables.Any(x => x.Equals(wellCable)))
                                {
                                    //W.Ed.WriteMessage("修改了");

                                    wellCable.Used = true;
                                    wellCable.CableTo = $"{m_PipelineModel.PipelineNumber}";



                                }
                            }

                            //先更新工井，再更新管道
                            var valueList_Well = GenericExtensionDictionaryManager.ObjectToTypeValueList<WellModel>(m_FromWell);
                            var succeed_Well = wellId.ModObjXrecord(tr, typeof(WellModel).Name, valueList_Well);
                            if (succeed_Well)
                            {
                                m_PipelineModel.FromWell = m_FromWell;
                                var valueList = GenericExtensionDictionaryManager.ObjectToTypeValueList<PipelineModel>(m_PipelineModel);
                                var succeed = m_ObjectId.ModObjXrecord(tr, typeof(PipelineModel).Name, valueList);
                                if (succeed)
                                {
                                    //气泡弹窗显示
                                    GlobalSetting.ShowBalloonTip();
                                }
                            }


                        }
                        catch (Exception ex)
                        {
                            MessageBoxUtil.Message_Error($"写入数据时发生严重错误：\n{ex.Message}\n\nStackTrace:\n{ex.StackTrace}");
                        }

                    }
                    else
                    {
                        MessageBoxUtil.MessageInformation("没有选择线缆。");
                        return;
                    }
                    tr.Commit();
                }
            }

        }



        private void Btn_Cancel_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }


        /// <summary>
        /// 重新分配线缆，就是清空当前线的扩展数据，再重新分配
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_ReImportData_Click(object sender, RoutedEventArgs e)
        {
            using (var documentLock = W.DocLock)
            {
                try
                {

                    using (var tr = W.Trans)
                    {
                        W.FocusDoc();
                        if (m_PipelineModel.CableModels == null || m_PipelineModel.CableModels.Count < 0 || m_PipelineModel.CableModels.All(x => !x.IsSelected))
                        {
                            MessageBoxUtil.MessageInformation("当前管道暂未分配线缆，无需重新分配!");
                            return;
                        }

                        var wellId = ExtensionDictionaryManager.FindFirstWellByName(m_FromWell.WellName);
                        var selectedPipeCables = m_PipelineModel.CableModels.Where(c => c.IsSelected);//当前选中的线缆
                        if (selectedPipeCables.Any())
                        {
                            Btn_ImportData.IsEnabled = true;
                            try
                            {
                                // 安全检查：确认对象ID有效
                                if (m_ObjectId.IsNull || m_ObjectId.IsErased)
                                {
                                    MessageBoxUtil.Message_Error("关联的对象已被删除，无法写入数据。");
                                    return;
                                }

                                //同步更新相关工井的线缆信息
                                foreach (var wellCable in m_FromWell.CableModels)
                                {
                                    if (selectedPipeCables.Any(x => x.CableEntryNumber.Equals(wellCable.CableEntryNumber)))
                                    {
                                        //W.Ed.WriteMessage("修改了");
                                        wellCable.Used = false;
                                        wellCable.CableTo = $"未分配";
                                        wellCable.IsSelected = false;
                                    }
                                }
                                //先更新工井，再更新管道(这里一定要更新所有与这个工井有关联的管道一起更新)
                                var valueList_Well = GenericExtensionDictionaryManager.ObjectToTypeValueList<WellModel>(m_FromWell);
                                var succeed_Well = wellId.ModObjXrecord(tr, typeof(WellModel).Name, valueList_Well);
                                if (succeed_Well)
                                {
                                    var wellModelForUnSelected = m_FromWell.CableModels.Where(x => !x.Used);
                                    m_PipelineModel.CableModels.Clear();
                                    foreach (var cableModel in wellModelForUnSelected)
                                    {
                                        m_PipelineModel.CableModels.Add(cableModel);
                                    }
                                    m_PipelineModel.FromWell = m_FromWell;
                                    var valueList = GenericExtensionDictionaryManager.ObjectToTypeValueList<PipelineModel>(m_PipelineModel);
                                    var succeed = m_ObjectId.ModObjXrecord(tr, typeof(PipelineModel).Name, valueList);
                                  //  var totalPipeIds = ExtensionDictionaryManager.FindAllPipelinesIdToWellName(m_FromWell.WellName);//所有与这个工井有关的管道id集合

                                    if (succeed)
                                    {
                                        //气泡弹窗显示
                                        GlobalSetting.ShowBalloonTip();
                                    }
                                }


                            }
                            catch (Exception ex)
                            {
                                MessageBoxUtil.Message_Error($"写入数据时发生严重错误：\n{ex.Message}\n\nStackTrace:\n{ex.StackTrace}");
                            }

                        }
                        else
                        {
                            MessageBoxUtil.MessageInformation("没有选择线缆。");
                            return;
                        }






                        tr.Commit();
                    }
                }
                catch (Exception ex)
                {

                    MessageBoxUtil.MessageInformation(ex.Message);
                }
            }

        }
    }
}
