﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils
{
    internal class RegularExpressionsUtil
    {

    

        /// <summary>
        /// 提取第一个小数
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static double ExtractFirstDecimal(string input)
        {
            string pattern = @"[+-]?\d+[\.]?\d*";
            Match match = Regex.Match(input, pattern);
            if (match.Success)
            {
                return match.Value.StringToDouble();
            }
            return 0.0;
        }
    }
}
