﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartRouting.App.Utils.UserDefClass
{
    public class 线图
    {

        public List<线> xxs = new List<线>();
        public List<节点> dds = new List<节点>();
        public 线图(List<Line> curves)
        {
            xxs.Clear();
            dds.Clear();
            List<Point3d> pts = new List<Point3d>();
            foreach (var c in curves)
            {
                pts.Add(c.StartPoint);
                pts.Add(c.EndPoint);
            }
            点去重(ref pts);
            foreach (var p in pts)
            {
                dds.Add(new 节点(p));
            }
            foreach (var line in curves)
            {
                xxs.Add(new 线(line));
            }
            foreach (var dd in dds)
            {
                var vv1 = from x in xxs
                          where x.StartNode == null
                          where x.Line.StartPoint.IsEqualTo(dd.Pt, new Tolerance(10e-3, 10e-3))
                          select x;
                foreach (var v in vv1)
                {
                    v.StartNode = dd;
                    dd.连接线.Add(v);
                }
                var vv2 = from x in xxs
                          where x.EndNode == null
                          where x.Line.EndPoint.IsEqualTo(dd.Pt, new Tolerance(10e-3, 10e-3))
                          select x;
                foreach (var v in vv2)
                {
                    v.EndNode = dd;
                    dd.连接线.Add(v);
                }
            }
        }

        public static void 点去重(ref List<Point3d> pts)
        {
            pts = pts.Distinct(new EqualityPoint3d()).ToList();
        }



        public void 寻路(Point3d p1, Point3d p2)
        {
            foreach (var a in dds)
            {
                a.Dist = double.MaxValue;
                a.标记 = false;
            }
            var aa = from a in dds orderby a.Pt.DistanceTo(p1) select a;
            var bb = from a in dds orderby a.Pt.DistanceTo(p2) select a;
            var start = aa.First();
            var end = bb.First();
            start.Dist = 0;
            while (true)
            {
                var zuiduanvv = from a in dds
                                where a.标记 == false
                                orderby a.Dist
                                select a;
                if (zuiduanvv.Count() == 0)
                {
                    break;
                }
                var zuiduan = zuiduanvv.First();
                zuiduan.标记 = true;
                foreach (var lj in zuiduan.连接线)
                {
                    var ot = lj.GetOther(zuiduan);
                    var dist = zuiduan.Dist + lj.Weight;
                    if (dist < ot.Dist)
                    {
                        ot.Dist = dist;
                        ot.前点 = zuiduan;
                    }


                }
            }

            List<Line> curs = new List<Line>();
            var dq = end;
            while (true)
            {
                if (dq.前点 != null)
                {
                    线 xx = GetFromAB(dq, dq.前点);
                    curs.Add(xx.Line);
                    dq = dq.前点;
                }
                else
                {
                    break;
                }
            }
            List<Point3d> points = new List<Point3d>();
            #region 采用JoinEntities的方式，貌似行不通

            List<Polyline> lines = new List<Polyline>();
            foreach (var item in curs)
            {
                var line = ModelUtil.CreateEntity2(item.LineToPolyline(), 2, "6666") as Polyline;
                lines.Add(line);
            }
            //  MessageBoxUtils.MessageInformation("AAAAAA");
            var first = lines.First();
            //  MessageBoxUtils.MessageInformation("111111");
            lines.RemoveAt(0);
            //  MessageBoxUtils.MessageInformation("BBBBB");
            using (var tr = W.Trans)
            {
                //  MessageBoxUtils.MessageInformation("333333");
                var list1 = tr.GetObject(first.Id, OpenMode.ForWrite) as Polyline;
                //  MessageBoxUtils.MessageInformation("444444");
                var ids = list1.JoinEntities(lines.ToArray());
                //  MessageBoxUtils.MessageInformation("CCCCCC");
                for (int i = 0; i < list1.NumberOfVertices; i++)
                {
                    points.Add(list1.GetPoint3dAt(i));
                }
                tr.Commit();
            }
            #endregion


        }


        private 线 GetFromAB(节点 d1, 节点 d2)
        {
            var vv = from x in xxs
                     where (x.StartNode == d1 && x.EndNode == d2) || (x.StartNode == d2 && x.EndNode == d1)
                     select x;
            if (vv.Count() > 0)
            {
                return vv.First();
            }
            else
            {
                return null;
            }
        }
    }


}
